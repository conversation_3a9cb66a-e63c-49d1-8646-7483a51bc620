#!/usr/bin/env python3
"""
Script de diagnóstico para identificar por qué las descargas no se inician en la aplicación.
"""

import sys
import os
import time
import threading
from unittest.mock import Mock

# Agregar el directorio actual al path para importar los módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from download_manager_requests import DownloadManager, DownloadStatus
    from sftp_manager import SFTPManager
    print("✅ Módulos importados correctamente")
except ImportError as e:
    print(f"❌ Error importando módulos: {e}")
    sys.exit(1)

def simulate_main_app_flow():
    """Simula el flujo exacto que usa la aplicación principal"""
    print("\n🔍 Simulando flujo de la aplicación principal...")
    
    # Crear SFTP manager
    sftp_manager = SFTPManager()
    
    # Crear download manager como lo hace la aplicación
    download_manager = DownloadManager(sftp_manager=sftp_manager)
    
    print(f"📊 Estado inicial del download manager:")
    print(f"   - is_running: {download_manager.is_running}")
    print(f"   - Número de descargas: {len(download_manager.downloads)}")
    
    # Simular conexión SFTP (mock)
    print("\n🔗 Simulando conexión SFTP...")
    mock_connected = True
    current_path = "/test"
    
    if not mock_connected:
        print("❌ No conectado al SFTP")
        return False
    
    print(f"✅ Conectado al SFTP, carpeta actual: {current_path}")
    
    # Simular selección de archivos M3U
    m3u_items = [
        {
            'name': 'test_movie.m3u8',
            'url': 'https://httpbin.org/delay/2'
        },
        {
            'name': 'test_series.m3u8', 
            'url': 'https://httpbin.org/delay/3'
        }
    ]
    
    print(f"\n📱 Archivos M3U seleccionados: {len(m3u_items)}")
    for item in m3u_items:
        print(f"   - {item['name']}: {item['url']}")
    
    # Configurar SFTP manager en download manager (como hace la app)
    if hasattr(download_manager, 'set_sftp_manager'):
        download_manager.set_sftp_manager(sftp_manager)
        print("✅ SFTP Manager configurado en Download Manager")
    else:
        print("⚠️ No se pudo configurar SFTP Manager")
    
    # Simular el proceso de añadir descargas
    print(f"\n🔽 Añadiendo descargas...")
    success_count = 0
    error_count = 0
    
    # Simular elección de descarga remota (SÍ)
    download_type = True  # True = remota, False = local
    print(f"🔽 Tipo de descarga: {'Remota (wget)' if download_type else 'Local'}")
    
    for item in m3u_items:
        try:
            if download_type:  # Descarga remota
                print(f"📡 Añadiendo descarga remota: {item['name']}")
                download_id = download_manager.add_remote_download(
                    url=item['url'],
                    filename=item['name'],
                    remote_path=current_path
                )
                print(f"✅ Descarga remota añadida: {download_id}")
            else:  # Descarga local
                print(f"💻 Añadiendo descarga local: {item['name']}")
                download_id = download_manager.add_download(
                    url=item['url'],
                    filename=item['name'],
                    remote_path=current_path
                )
                print(f"✅ Descarga local añadida: {download_id}")
            
            success_count += 1
            
        except Exception as e:
            print(f"❌ Error añadiendo descarga para {item['name']}: {e}")
            error_count += 1
    
    print(f"\n📊 Resumen de descargas añadidas:")
    print(f"   - Exitosas: {success_count}")
    print(f"   - Errores: {error_count}")
    print(f"   - Total en cola: {len(download_manager.downloads)}")
    print(f"   - Sistema corriendo: {download_manager.is_running}")
    
    # Verificar estado de las descargas
    print(f"\n📋 Estado de las descargas:")
    downloads = download_manager.get_downloads_status()
    for i, download in enumerate(downloads):
        print(f"   {i+1}. {download['filename']}")
        print(f"      Estado: {download['status']}")
        print(f"      Progreso: {download['progress']:.1f}%")
        print(f"      Es remota: {download.get('is_remote', False)}")
    
    # Simular monitoreo como lo hace la aplicación
    print(f"\n👁️ Simulando monitoreo de descargas...")
    for i in range(6):  # Monitorear por 6 iteraciones (3 segundos)
        time.sleep(0.5)
        downloads = download_manager.get_downloads_status()
        
        has_active = any(d.get('status') in ['Downloading', 'Processing', 'Queued'] for d in downloads)
        print(f"   Iteración {i+1}: {len(downloads)} descargas, activas: {has_active}")
        
        if downloads:
            for download in downloads:
                status = download['status']
                progress = download['progress']
                print(f"     - {download['filename']}: {status} ({progress:.1f}%)")
    
    # Limpiar
    print(f"\n🛑 Deteniendo sistema...")
    download_manager.stop_downloads()
    
    return success_count > 0

def check_download_manager_methods():
    """Verifica que todos los métodos necesarios existan"""
    print("\n🔍 Verificando métodos del Download Manager...")
    
    sftp = SFTPManager()
    dm = DownloadManager(sftp_manager=sftp)
    
    required_methods = [
        'add_download',
        'add_remote_download', 
        'get_downloads_status',
        'start_downloads',
        'stop_downloads',
        'set_sftp_manager'
    ]
    
    missing_methods = []
    for method in required_methods:
        if hasattr(dm, method):
            print(f"   ✅ {method}")
        else:
            print(f"   ❌ {method} - FALTA")
            missing_methods.append(method)
    
    dm.stop_downloads()
    
    if missing_methods:
        print(f"\n❌ Métodos faltantes: {missing_methods}")
        return False
    else:
        print(f"\n✅ Todos los métodos necesarios están presentes")
        return True

def check_sftp_manager_methods():
    """Verifica que todos los métodos necesarios del SFTP existan"""
    print("\n🔍 Verificando métodos del SFTP Manager...")
    
    sftp = SFTPManager()
    
    required_methods = [
        'is_connected',
        'verify_directory_exists'
    ]
    
    missing_methods = []
    for method in required_methods:
        if hasattr(sftp, method):
            print(f"   ✅ {method}")
        else:
            print(f"   ❌ {method} - FALTA")
            missing_methods.append(method)
    
    if missing_methods:
        print(f"\n❌ Métodos faltantes en SFTP: {missing_methods}")
        return False
    else:
        print(f"\n✅ Todos los métodos SFTP necesarios están presentes")
        return True

def main():
    """Función principal de diagnóstico"""
    print("🔍 DIAGNÓSTICO DE SISTEMA DE DESCARGAS")
    print("=" * 60)
    
    try:
        # Verificar métodos
        dm_ok = check_download_manager_methods()
        sftp_ok = check_sftp_manager_methods()
        
        if not dm_ok or not sftp_ok:
            print("\n❌ Faltan métodos necesarios, no se puede continuar")
            return False
        
        # Simular flujo completo
        print("\n" + "="*60)
        flow_ok = simulate_main_app_flow()
        
        print("\n" + "="*60)
        if flow_ok:
            print("🎉 ¡El sistema de descargas funciona correctamente!")
            print("💡 Si las descargas no aparecen en la aplicación, el problema puede estar en:")
            print("   - La conexión SFTP real")
            print("   - La validación de directorios")
            print("   - La actualización de la interfaz gráfica")
            print("   - Los callbacks de progreso")
        else:
            print("❌ Hay problemas en el sistema de descargas")
        
        return flow_ok
        
    except Exception as e:
        print(f"\n❌ Error durante el diagnóstico: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
