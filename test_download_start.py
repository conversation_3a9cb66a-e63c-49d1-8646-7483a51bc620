#!/usr/bin/env python3
"""
Script de prueba para verificar que las descargas se inicien correctamente.
"""

import sys
import os
import time
import threading
from unittest.mock import Mock, patch

# Agregar el directorio actual al path para importar los módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from download_manager_requests import DownloadManager, DownloadStatus
    from sftp_manager import SFTPManager
    print("✅ Módulos importados correctamente")
except ImportError as e:
    print(f"❌ Error importando módulos: {e}")
    sys.exit(1)

def test_download_auto_start():
    """Prueba que las descargas se inicien automáticamente"""
    print("\n🔍 Probando inicio automático de descargas...")
    
    # Crear mock del SFTP manager
    mock_sftp = Mock()
    mock_sftp.is_connected.return_value = True
    
    # Crear download manager
    dm = DownloadManager(sftp_manager=mock_sftp)
    
    # Verificar estado inicial
    assert dm.is_running == False, "❌ Download manager no debería estar corriendo inicialmente"
    print("✅ Estado inicial correcto: no está corriendo")
    
    # Añadir una descarga remota
    download_id = dm.add_remote_download(
        url="http://example.com/test.m3u8",
        filename="test.m3u8",
        remote_path="/test"
    )
    
    # Verificar que se inició automáticamente
    assert dm.is_running == True, "❌ Download manager debería haberse iniciado automáticamente"
    print("✅ Sistema de descargas se inició automáticamente")
    
    # Verificar que la descarga está en la cola
    downloads = dm.get_downloads_status()
    assert len(downloads) == 1, f"❌ Debería haber 1 descarga, pero hay {len(downloads)}"
    assert downloads[0]['id'] == download_id, "❌ ID de descarga no coincide"
    assert downloads[0]['status'] == 'Queued', f"❌ Estado debería ser 'Queued', pero es '{downloads[0]['status']}'"
    print("✅ Descarga añadida correctamente a la cola")
    
    # Limpiar
    dm.stop_downloads()
    return True

def test_download_local_start():
    """Prueba que las descargas locales se inicien automáticamente"""
    print("\n🔍 Probando inicio automático de descargas locales...")
    
    # Crear mock del SFTP manager
    mock_sftp = Mock()
    mock_sftp.is_connected.return_value = True
    
    # Crear download manager
    dm = DownloadManager(sftp_manager=mock_sftp)
    
    # Añadir una descarga local
    download_id = dm.add_download(
        url="http://example.com/test.m3u8",
        filename="test_local.m3u8",
        remote_path="/test"
    )
    
    # Verificar que se inició automáticamente
    assert dm.is_running == True, "❌ Download manager debería haberse iniciado automáticamente"
    print("✅ Sistema de descargas locales se inició automáticamente")
    
    # Verificar que la descarga está en la cola
    downloads = dm.get_downloads_status()
    assert len(downloads) == 1, f"❌ Debería haber 1 descarga, pero hay {len(downloads)}"
    assert downloads[0]['id'] == download_id, "❌ ID de descarga no coincide"
    print("✅ Descarga local añadida correctamente a la cola")
    
    # Limpiar
    dm.stop_downloads()
    return True

def test_multiple_downloads():
    """Prueba múltiples descargas"""
    print("\n🔍 Probando múltiples descargas...")
    
    # Crear mock del SFTP manager
    mock_sftp = Mock()
    mock_sftp.is_connected.return_value = True
    
    # Crear download manager
    dm = DownloadManager(sftp_manager=mock_sftp)
    
    # Añadir múltiples descargas
    download_ids = []
    for i in range(3):
        download_id = dm.add_remote_download(
            url=f"http://example.com/test{i}.m3u8",
            filename=f"test{i}.m3u8",
            remote_path="/test"
        )
        download_ids.append(download_id)
    
    # Verificar que todas están en la cola
    downloads = dm.get_downloads_status()
    assert len(downloads) == 3, f"❌ Debería haber 3 descargas, pero hay {len(downloads)}"
    print("✅ Múltiples descargas añadidas correctamente")
    
    # Verificar que el sistema está corriendo
    assert dm.is_running == True, "❌ Download manager debería estar corriendo"
    print("✅ Sistema de descargas corriendo con múltiples descargas")
    
    # Limpiar
    dm.stop_downloads()
    return True

def test_sftp_manager_assignment():
    """Prueba que el SFTP manager se asigne correctamente"""
    print("\n🔍 Probando asignación del SFTP manager...")
    
    # Crear SFTP manager real
    sftp = SFTPManager()
    
    # Crear download manager
    dm = DownloadManager(sftp_manager=sftp)
    
    # Verificar asignación inicial
    assert dm.sftp_manager == sftp, "❌ SFTP manager no se asignó correctamente"
    print("✅ SFTP manager asignado en constructor")
    
    # Probar método set_sftp_manager
    new_sftp = SFTPManager()
    result = dm.set_sftp_manager(new_sftp)
    
    assert result == True, "❌ set_sftp_manager debería retornar True"
    assert dm.sftp_manager == new_sftp, "❌ SFTP manager no se actualizó correctamente"
    print("✅ SFTP manager actualizado correctamente")
    
    # Limpiar
    dm.stop_downloads()
    return True

def run_all_tests():
    """Ejecuta todas las pruebas"""
    print("🚀 Iniciando pruebas de inicio de descargas...")
    print("=" * 60)
    
    tests = [
        ("Inicio automático de descargas remotas", test_download_auto_start),
        ("Inicio automático de descargas locales", test_download_local_start),
        ("Múltiples descargas", test_multiple_downloads),
        ("Asignación de SFTP manager", test_sftp_manager_assignment),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 Ejecutando: {test_name}")
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name}: PASÓ")
            else:
                failed += 1
                print(f"❌ {test_name}: FALLÓ")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Resultados: {passed} pasaron, {failed} fallaron")
    
    if failed == 0:
        print("🎉 ¡Todas las pruebas pasaron! Las descargas deberían iniciarse correctamente.")
        return True
    else:
        print("⚠️ Algunas pruebas fallaron. Revisar el sistema de descargas.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
