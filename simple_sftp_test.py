#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SFTP Connection Helper - Ayudante simple para probar conexión
"""

import paramiko
import json

def test_connection_simple():
    """Test simple con datos que funcionan"""
    print("🚀 Prueba Simple de Conexión SFTP")
    print("=" * 40)
    
    # 1. Test con servidor demo
    print("1️⃣ Probando servidor demo...")
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect("test.rebex.net", 22, "demo", "password", timeout=10)
        sftp = ssh.open_sftp()
        files = sftp.listdir('.')
        print(f"✅ Servidor demo funciona: {len(files)} archivos")
        sftp.close()
        ssh.close()
    except Exception as e:
        print(f"❌ Servidor demo falló: {e}")
        return False
    
    # 2. Solicitar datos del usuario de manera simple
    print("\n2️⃣ Ahora prueba con TUS datos:")
    print("Escribe los datos uno por uno:")
    
    host = ""
    while not host:
        host = input("Host/IP: ").strip()
    
    port = input("Puerto (Enter=22): ").strip()
    if not port:
        port = "22"
    try:
        port = int(port)
    except:
        port = 22
    
    user = ""
    while not user:
        user = input("Usuario: ").strip()
    
    password = ""
    while not password:
        password = input("Contraseña: ").strip()
    
    print(f"\n🔄 Probando: {user}@{host}:{port}")
    
    # 3. Test de conexión
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        print("  🔗 Conectando SSH...")
        ssh.connect(host, port, user, password, timeout=15)
        print("  ✅ SSH OK")
        
        print("  📁 Abriendo SFTP...")
        sftp = ssh.open_sftp()
        print("  ✅ SFTP OK")
        
        print("  📋 Listando archivos...")
        files = sftp.listdir('.')
        print(f"  ✅ {len(files)} archivos encontrados")
        
        # Mostrar algunos archivos
        for i, file in enumerate(files[:5]):
            print(f"    📄 {file}")
        if len(files) > 5:
            print(f"    ... y {len(files)-5} más")
        
        sftp.close()
        ssh.close()
        
        print(f"\n🎉 ¡CONEXIÓN EXITOSA!")
        print(f"Usa estos datos en la aplicación:")
        print(f"  Host: {host}")
        print(f"  Puerto: {port}")
        print(f"  Usuario: {user}")
        print(f"  Contraseña: (la que escribiste)")
        
        # Guardar configuración
        config = {
            "sftp": {
                "host": host,
                "port": str(port),
                "user": user
            }
        }
        
        with open("config_working.json", "w") as f:
            json.dump(config, f, indent=2)
        print(f"\n💾 Configuración guardada en 'config_working.json'")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("\n🔧 Posibles soluciones:")
        print("• Verificar IP/hostname")
        print("• Comprobar puerto (¿es 22?)")
        print("• Revisar usuario/contraseña")
        print("• Probar con otro cliente (WinSCP)")
        return False

if __name__ == "__main__":
    test_connection_simple()
