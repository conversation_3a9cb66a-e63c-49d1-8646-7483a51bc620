#!/usr/bin/env python3
"""
Script para diagnosticar por qué las descargas remotas están fallando.
"""

import sys
import os
import time

# Agregar el directorio actual al path para importar los módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_wget_failure():
    """Diagnostica por qué wget está fallando"""
    print("🔍 DIAGNÓSTICO DE FALLO DE WGET")
    print("=" * 60)
    
    try:
        from sftp_manager import SFTPManager
        
        # Crear conexión SFTP
        print("📱 Creando conexión SFTP...")
        sftp = SFTPManager()
        
        # Intentar conectar
        if not sftp.connect("root", "***************", "Maujose2024", 22):
            print("❌ No se pudo conectar al servidor SFTP")
            return False
        
        print("✅ Conexión SFTP exitosa")
        
        # Verificar directorio de destino
        print(f"\n📂 Verificando directorio /media...")
        if sftp.verify_directory_exists("/media"):
            print("✅ Directorio /media existe")
        else:
            print("❌ Directorio /media no existe")
            return False
        
        # Probar comando wget simple
        print(f"\n🧪 Probando comando wget simple...")
        
        test_url = "https://httpbin.org/json"
        test_filename = "test_wget.json"
        log_file = "/tmp/debug_wget.log"
        
        # Comando wget simplificado para prueba
        simple_cmd = f'cd /media && wget --timeout=10 --tries=1 --output-document="{test_filename}" "{test_url}" > {log_file} 2>&1'
        
        print(f"🔍 Comando de prueba: {simple_cmd}")
        
        # Ejecutar comando
        stdin, stdout, stderr = sftp.ssh_client.exec_command(simple_cmd, timeout=15)
        
        # Esperar a que termine
        exit_status = stdout.channel.recv_exit_status()
        output = stdout.read().decode('utf-8', errors='ignore')
        error = stderr.read().decode('utf-8', errors='ignore')
        
        print(f"📊 Resultado del comando:")
        print(f"   - Exit status: {exit_status}")
        print(f"   - Output: {output}")
        print(f"   - Error: {error}")
        
        # Verificar si el archivo se creó
        time.sleep(1)
        check_cmd = f'ls -la /media/{test_filename}'
        stdin, stdout, stderr = sftp.ssh_client.exec_command(check_cmd)
        file_check = stdout.read().decode('utf-8', errors='ignore')
        
        print(f"📁 Verificación del archivo:")
        print(f"   - Resultado: {file_check}")
        
        # Leer el log de wget
        log_cmd = f'cat {log_file}'
        stdin, stdout, stderr = sftp.ssh_client.exec_command(log_cmd)
        log_content = stdout.read().decode('utf-8', errors='ignore')
        
        print(f"📝 Contenido del log de wget:")
        print(f"   {log_content}")
        
        # Probar con la URL real del usuario
        print(f"\n🎬 Probando con URL real del usuario...")
        
        real_url = "https://zonamovie.live:8443/series/Maujose2024/20240613507/883259.mkv"
        real_filename = "test_real.mkv"
        real_log = "/tmp/debug_real_wget.log"
        
        # Solo probar conectividad, no descargar el archivo completo
        real_cmd = f'cd /media && timeout 10 wget --spider --timeout=5 --tries=1 --user-agent="XCIPTV" "{real_url}" > {real_log} 2>&1'
        
        print(f"🔍 Comando real (spider): {real_cmd}")
        
        stdin, stdout, stderr = sftp.ssh_client.exec_command(real_cmd, timeout=15)
        real_exit = stdout.channel.recv_exit_status()
        real_output = stdout.read().decode('utf-8', errors='ignore')
        real_error = stderr.read().decode('utf-8', errors='ignore')
        
        print(f"📊 Resultado del comando real:")
        print(f"   - Exit status: {real_exit}")
        print(f"   - Output: {real_output}")
        print(f"   - Error: {real_error}")
        
        # Leer el log del comando real
        real_log_cmd = f'cat {real_log}'
        stdin, stdout, stderr = sftp.ssh_client.exec_command(real_log_cmd)
        real_log_content = stdout.read().decode('utf-8', errors='ignore')
        
        print(f"📝 Contenido del log real:")
        print(f"   {real_log_content}")
        
        # Verificar conectividad básica
        print(f"\n🌐 Verificando conectividad básica...")
        
        ping_cmd = "ping -c 2 zonamovie.live"
        stdin, stdout, stderr = sftp.ssh_client.exec_command(ping_cmd, timeout=10)
        ping_result = stdout.read().decode('utf-8', errors='ignore')
        
        print(f"🏓 Resultado del ping:")
        print(f"   {ping_result}")
        
        # Verificar resolución DNS
        nslookup_cmd = "nslookup zonamovie.live"
        stdin, stdout, stderr = sftp.ssh_client.exec_command(nslookup_cmd, timeout=10)
        dns_result = stdout.read().decode('utf-8', errors='ignore')
        
        print(f"🔍 Resultado DNS:")
        print(f"   {dns_result}")
        
        # Limpiar archivos de prueba
        cleanup_cmd = f'rm -f /media/{test_filename} {log_file} {real_log}'
        sftp.ssh_client.exec_command(cleanup_cmd)
        
        sftp.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ Error en diagnóstico de wget: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_wget_command():
    """Analiza el comando wget que se está usando"""
    print("\n🔍 ANÁLISIS DEL COMANDO WGET")
    print("=" * 60)
    
    # Comando actual
    current_cmd = '''cd "/media" && nohup timeout 600 wget --continue --tries=3 --user-agent="XCIPTV" --progress=dot:mega --timeout=15 --output-document="Los Caballeros del Zodiaco_ Omega (2012) S01 E01" "https://zonamovie.live:8443/series/Maujose2024/20240613507/883259.mkv" > /tmp/wget_log_1751840262_Los Caballeros del Zodiaco_ Omega (2012) S01 E01.log 2>&1 &'''
    
    print(f"📋 Comando actual:")
    print(f"   {current_cmd}")
    
    print(f"\n🔍 Análisis de componentes:")
    print(f"   - cd '/media': ✅ Cambiar al directorio correcto")
    print(f"   - nohup: ✅ Ejecutar en segundo plano")
    print(f"   - timeout 600: ✅ Límite de 10 minutos")
    print(f"   - --continue: ✅ Reanudar descargas")
    print(f"   - --tries=3: ✅ 3 intentos")
    print(f"   - --user-agent='XCIPTV': ✅ User agent correcto")
    print(f"   - --progress=dot:mega: ✅ Progreso detallado")
    print(f"   - --timeout=15: ✅ Timeout de conexión")
    print(f"   - --output-document: ✅ Nombre de archivo")
    print(f"   - > log 2>&1: ✅ Redirigir output")
    print(f"   - &: ✅ Ejecutar en background")
    
    print(f"\n💡 Posibles problemas:")
    print(f"   1. 🌐 Conectividad: El servidor no puede acceder a zonamovie.live")
    print(f"   2. 🔐 Autenticación: La URL requiere autenticación específica")
    print(f"   3. 🚫 Bloqueo: El servidor bloquea el user-agent o IP")
    print(f"   4. 📁 Permisos: Sin permisos de escritura en /media")
    print(f"   5. 🔗 URL: La URL específica no existe o expiró")
    
    print(f"\n🔧 Comandos de diagnóstico sugeridos:")
    print(f"   1. wget --spider --timeout=5 'URL' (verificar si existe)")
    print(f"   2. curl -I 'URL' (verificar headers)")
    print(f"   3. ping zonamovie.live (verificar conectividad)")
    print(f"   4. ls -la /media (verificar permisos)")

def main():
    """Función principal de diagnóstico"""
    print("🔍 DIAGNÓSTICO COMPLETO DE FALLO DE WGET")
    print("=" * 60)
    
    # Análisis del comando
    analyze_wget_command()
    
    # Diagnóstico en vivo
    result = debug_wget_failure()
    
    print(f"\n{'='*60}")
    print("📊 CONCLUSIONES DEL DIAGNÓSTICO")
    print(f"{'='*60}")
    
    if result:
        print(f"✅ Diagnóstico completado")
        print(f"📝 Revisa los logs anteriores para identificar el problema específico")
    else:
        print(f"❌ No se pudo completar el diagnóstico")
    
    print(f"\n💡 Próximos pasos:")
    print(f"   1. Verificar conectividad del servidor a zonamovie.live")
    print(f"   2. Probar con URLs más simples primero")
    print(f"   3. Verificar permisos en /media")
    print(f"   4. Revisar logs de wget para errores específicos")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n💥 CRASH EN DIAGNÓSTICO: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
