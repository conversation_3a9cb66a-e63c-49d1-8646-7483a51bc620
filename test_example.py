#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ejemplo de uso del M3U Manager
"""

import time
import json
from m3u_parser import M3UParser
from sftp_manager import SFTPManager
from download_manager import DownloadManager

def test_m3u_parser():
    """Prueba el analizador M3U"""
    print("=== Probando M3U Parser ===")
    
    parser = M3UParser()
    
    # Ejemplo de contenido M3U
    sample_m3u = """#EXTM3U
#EXTINF:-1 tvg-id="canal1" tvg-name="Canal Ejemplo" tvg-logo="http://ejemplo.com/logo.png" group-title="Entretenimiento",Canal Ejemplo
http://ejemplo.com/stream1.m3u8
#EXTINF:-1 tvg-id="pelicula1" tvg-name="Película Ejemplo" tvg-logo="http://ejemplo.com/movie.jpg" group-title="Películas",Pel<PERSON><PERSON> Ejemplo (2024)
http://ejemplo.com/movie1.mp4
#EXTINF:-1 tvg-id="serie1" tvg-name="Serie Ejemplo T1E1" tvg-logo="http://ejemplo.com/serie.jpg" group-title="Series",Serie Ejemplo - Temporada 1 Episodio 1
http://ejemplo.com/serie_t1e1.mp4
"""
    
    # Simular archivo temporal
    with open('temp_test.m3u', 'w', encoding='utf-8') as f:
        f.write(sample_m3u)
    
    try:
        content = parser.parse_file('temp_test.m3u')
        print(f"Elementos encontrados: {len(content)}")
        
        for item in content:
            print(f"- {item['name']} ({item['type']}) - Grupo: {item['group']}")
        
        # Probar estadísticas
        stats = parser.get_content_stats(content)
        print(f"\nEstadísticas: {stats}")
        
        # Probar búsqueda
        resultados = parser.search_content(content, "ejemplo")
        print(f"\nBúsqueda 'ejemplo': {len(resultados)} resultados")
        
    except Exception as e:
        print(f"Error: {e}")
    
    finally:
        import os
        if os.path.exists('temp_test.m3u'):
            os.remove('temp_test.m3u')

def test_download_manager():
    """Prueba el gestor de descargas"""
    print("\n=== Probando Download Manager ===")
    
    dm = DownloadManager(max_concurrent_downloads=2)
    
    # Agregar descargas de ejemplo
    download1 = dm.add_download(
        url="http://example.com/file1.mp4",
        filename="archivo_ejemplo_1.mp4",
        remote_path="/downloads/movies"
    )
    
    download2 = dm.add_download(
        url="http://example.com/file2.mp4", 
        filename="archivo_ejemplo_2.mp4",
        remote_path="/downloads/movies"
    )
    
    print(f"Descarga 1 ID: {download1}")
    print(f"Descarga 2 ID: {download2}")
    
    # Mostrar estadísticas
    stats = dm.get_statistics()
    print(f"Estadísticas: {stats}")
    
    # Mostrar estado de descargas
    downloads_status = dm.get_downloads_status()
    for download in downloads_status:
        print(f"- {download['filename']}: {download['status']}")
    
    # Simular inicio (sin realmente descargar)
    print("\nSimulando inicio de descargas...")
    # dm.start_downloads()  # Comentado para evitar descargas reales
    
    # Guardar estado
    dm.save_queue_state('test_queue.json')
    print("Estado guardado en test_queue.json")
    
    # Limpiar
    dm.clear_queue()
    print("Cola limpiada")

def test_sftp_manager():
    """Prueba el gestor SFTP (sin conexión real)"""
    print("\n=== Probando SFTP Manager ===")
    
    sftp = SFTPManager()
    
    print(f"¿Conectado? {sftp.is_connected()}")
    
    # Mostrar métodos disponibles
    print("\nMétodos disponibles:")
    methods = [method for method in dir(sftp) if not method.startswith('_')]
    for method in methods[:10]:  # Mostrar solo los primeros 10
        print(f"- {method}")
    
    print("SFTP Manager listo para usar")

def simulate_workflow():
    """Simula un flujo de trabajo completo"""
    print("\n=== Simulando Flujo de Trabajo Completo ===")
    
    # 1. Parsear M3U
    parser = M3UParser()
    sample_content = [
        {
            'name': 'Película Acción 2024',
            'group': 'Películas',
            'type': 'movie',
            'url': 'http://example.com/movie.mp4',
            'duration': '7200'
        },
        {
            'name': 'Serie Drama T1E1',
            'group': 'Series',
            'type': 'series',
            'url': 'http://example.com/serie_e1.mp4',
            'duration': '2700'
        }
    ]
    
    print("1. Contenido M3U cargado:")
    for item in sample_content:
        print(f"   - {item['name']} ({item['type']})")
    
    # 2. Configurar descargas
    dm = DownloadManager()
    
    print("\n2. Agregando a cola de descarga:")
    for item in sample_content:
        download_id = dm.add_download(
            url=item['url'],
            filename=f"{item['name']}.mp4",
            remote_path="/downloads"
        )
        print(f"   - {item['name']} -> ID: {download_id}")
    
    # 3. Mostrar estadísticas
    stats = dm.get_statistics()
    print(f"\n3. Estadísticas finales: {stats}")
    
    print("\n¡Flujo de trabajo simulado exitosamente!")

def create_sample_config():
    """Crea un archivo de configuración de ejemplo"""
    print("\n=== Creando Configuración de Ejemplo ===")
    
    config = {
        "xtream": {
            "url": "http://tu-servidor.com:8080",
            "user": "tu_usuario",
            "password": "tu_contraseña"
        },
        "sftp": {
            "host": "tu-servidor-sftp.com",
            "port": "22",
            "user": "tu_usuario_ssh"
        },
        "download": {
            "max_concurrent": 3,
            "temp_dir": "./temp_downloads",
            "auto_upload": True
        }
    }
    
    with open('config_example.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print("Archivo 'config_example.json' creado")
    print("Edita este archivo con tus datos reales antes de usar la aplicación")

def main():
    """Función principal de pruebas"""
    print("M3U Manager - Script de Pruebas y Ejemplos")
    print("=" * 50)
    
    try:
        test_m3u_parser()
        test_download_manager()
        test_sftp_manager()
        simulate_workflow()
        create_sample_config()
        
        print("\n" + "=" * 50)
        print("✅ Todas las pruebas completadas exitosamente!")
        print("\nPara usar la aplicación completa, ejecuta:")
        print("   python main.py")
        
    except Exception as e:
        print(f"\n❌ Error durante las pruebas: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
