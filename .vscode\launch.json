{"version": "0.2.0", "configurations": [{"name": "Python: M3U Manager App", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/main.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}", "env": {"PYTHONPATH": "${workspaceFolder}"}}, {"name": "Python: Test Example", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/test_example.py", "console": "integratedTerminal", "cwd": "${workspaceFolder}"}]}