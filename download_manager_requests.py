#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Download Manager Alternative - Versión que usa requests en lugar de wget
"""

import os
import requests
import threading
import queue
import time
import json
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

class DownloadStatus(Enum):
    QUEUED = "En cola"
    DOWNLOADING = "Descargando"
    PAUSED = "Pausado" 
    COMPLETED = "Completado"
    ERROR = "Error"
    CANCELLED = "Cancelado"

@dataclass
class DownloadItem:
    id: str
    url: str
    filename: str
    remote_path: str
    local_path: str
    status: DownloadStatus
    progress: float = 0.0
    speed: str = "0 B/s"
    eta: str = "--:--"
    file_size: str = "0 B"
    file_size_bytes: int = 0
    downloaded_bytes: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: str = ""
    retry_count: int = 0
    max_retries: int = 3
    sftp_manager: Optional[Any] = None
    
    # Campos específicos para descargas remotas
    is_remote: bool = False
    remote_pid: Optional[str] = None
    remote_log_file: Optional[str] = None
    last_check_time: Optional[datetime] = None
    check_interval: int = 10  # Segundos entre verificaciones de estado
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'url': self.url,
            'filename': self.filename,
            'remote_path': self.remote_path,
            'local_path': self.local_path,
            'status': self.status.value,
            'progress': self.progress,
            'speed': self.speed,
            'eta': self.eta,
            'file_size': self.file_size,
            'file_size_bytes': self.file_size_bytes,
            'downloaded_bytes': self.downloaded_bytes,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'is_remote': self.is_remote,
            'remote_pid': self.remote_pid,
            'remote_log_file': self.remote_log_file,
            'last_check_time': self.last_check_time.isoformat() if self.last_check_time else None
        }

class DownloadManager:
    def __init__(self, max_concurrent_downloads: int = 3, sftp_manager: Optional[Any] = None):
        self.max_concurrent_downloads = max_concurrent_downloads
        self.downloads: Dict[str, DownloadItem] = {}
        self.download_queue = queue.Queue()
        self.active_downloads: Dict[str, threading.Thread] = {}
        self.is_running = False
        self.is_paused = False
        self.sftp_manager = sftp_manager
        
        # Eventos y locks
        self.pause_event = threading.Event()
        self.pause_event.set()  # Inicialmente no pausado
        self.stop_event = threading.Event()
        self.downloads_lock = threading.Lock()
        
        # Configuración
        self.temp_dir = os.path.join(os.getcwd(), 'temp_downloads')
        self.chunk_size = 8192  # 8KB chunks
        self.timeout = 15  # Reducido de 30 a 15 segundos para evitar long threads
        
        # Crear directorio temporal
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # Callbacks
        self.progress_callbacks: List[Callable] = []
        self.status_callbacks: List[Callable] = []
        
        # Hilo principal de gestión y monitoreo remoto
        self.manager_thread = None
        self.remote_monitor_thread = None
        
        # Configurar session requests
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def add_remote_download(self, url: str, filename: str, remote_path: str) -> str:
        """Añade una descarga remota a la cola."""
        download_id = self._add_download_item(url, filename, remote_path, is_remote=True)
        return download_id

    def _add_download_item(self, url: str, filename: str, remote_path: str, is_remote: bool = False) -> str:
        """Método interno para crear y añadir un item de descarga."""
        download_id = f"dl_{int(time.time() * 1000)}_{len(self.downloads)}"
        clean_filename = self._clean_filename(filename)
        
        # La ruta local no es relevante para descargas remotas, pero la mantenemos por consistencia
        local_path = os.path.join(self.temp_dir, clean_filename)
        
        download_item = DownloadItem(
            id=download_id,
            url=url,
            filename=clean_filename,
            remote_path=remote_path.replace('\\', '/'),
            local_path=local_path,
            status=DownloadStatus.QUEUED,
            sftp_manager=self.sftp_manager, # Asignar el manager
            is_remote=is_remote
        )
        
        with self.downloads_lock:
            self.downloads[download_id] = download_item
            self.download_queue.put(download_id)
        
        self._notify_status_change(download_id)
        return download_id
        
    def add_download(self, url: str, filename: str, remote_path: str, 
                    local_path: Optional[str] = None) -> str:
        """Agrega una nueva descarga LOCAL a la cola"""
        # Esta función ahora usa el método interno
        # Mantenemos `remote_path` por si se necesita para alguna lógica futura
        return self._add_download_item(url, filename, remote_path, is_remote=False)
    
    def start_downloads(self):
        """Inicia el sistema de descargas"""
        if not self.is_running:
            self.is_running = True
            self.stop_event.clear()
            self.pause_event.set()
            
            self.manager_thread = threading.Thread(target=self._download_manager, daemon=True)
            self.manager_thread.start()

            self.remote_monitor_thread = threading.Thread(target=self._monitor_remote_downloads, daemon=True)
            self.remote_monitor_thread.start()
            
            print("Sistema de descargas iniciado")
    
    def pause_downloads(self):
        """Pausa todas las descargas"""
        self.is_paused = True
        self.pause_event.clear()
        print("Descargas pausadas")
    
    def resume_downloads(self):
        """Reanuda las descargas pausadas"""
        self.is_paused = False
        self.pause_event.set()
        
        # Reencolar descargas pausadas
        with self.downloads_lock:
            for download_id, download in self.downloads.items():
                if download.status == DownloadStatus.PAUSED:
                    download.status = DownloadStatus.QUEUED
                    self.download_queue.put(download_id)
                    self._notify_status_change(download_id)
        
        print("Descargas reanudadas")
    
    def stop_downloads(self):
        """Detiene todas las descargas y hilos asociados"""
        print("🛑 Deteniendo sistema de descargas...")
        
        self.is_running = False
        self.stop_event.set()
        self.pause_event.set()  # Asegurar que los hilos no estén bloqueados esperando
        
        # Marcar descargas activas como canceladas
        with self.downloads_lock:
            for download_id, download in self.downloads.items():
                if download.status == DownloadStatus.DOWNLOADING:
                    download.status = DownloadStatus.CANCELLED
                    download.end_time = datetime.now()
                    self._notify_status_change(download_id)
        
        # Esperar a que termine el hilo manager con timeout
        if self.manager_thread and self.manager_thread.is_alive():
            self.manager_thread.join(timeout=3)
            if self.manager_thread.is_alive():
                print("⚠️ Manager thread no terminó en el tiempo esperado")
        
        # Esperar a que termine el hilo de monitoreo remoto con timeout
        if self.remote_monitor_thread and self.remote_monitor_thread.is_alive():
            self.remote_monitor_thread.join(timeout=3)
            if self.remote_monitor_thread.is_alive():
                print("⚠️ Remote monitor thread no terminó en el tiempo esperado")
        
        # Limpiar hilos activos de descargas
        active_threads = list(self.active_downloads.values())
        for thread in active_threads:
            if thread.is_alive():
                thread.join(timeout=1)  # Timeout corto ya que son daemon threads
        
        self.active_downloads.clear()
        
        print("Descargas detenidas")

    def _monitor_remote_downloads(self):
        """Hilo que monitorea el progreso de las descargas remotas."""
        print("👁️ Iniciando monitor de descargas remotas...")
        
        while self.is_running and not self.stop_event.is_set():
            try:
                downloads_to_check = []
                with self.downloads_lock:
                    for item in self.downloads.values():
                        if item.is_remote and item.status == DownloadStatus.DOWNLOADING and item.remote_log_file:
                            downloads_to_check.append(item)

                if not downloads_to_check:
                    # Usar wait con timeout en lugar de sleep simple
                    if self.stop_event.wait(timeout=2.0):
                        break
                    continue

                for item in downloads_to_check:
                    # Verificar si debemos parar antes de procesar cada item
                    if self.stop_event.is_set():
                        break
                        
                    try:
                        if not item.sftp_manager or not item.sftp_manager.is_connected():
                            continue

                        # Obtener progreso del log
                        progress = item.sftp_manager.get_remote_download_progress(item.remote_log_file)
                        
                        if progress > item.progress:
                            item.progress = progress
                            self._notify_progress_change(item.id)

                        # Verificar si el proceso ha terminado
                        status_check = item.sftp_manager.check_remote_download_status(
                            pid=item.remote_pid, 
                            log_file=item.remote_log_file, 
                            file_path=item.remote_path
                        )

                        if not status_check.get('is_running', False) and item.status == DownloadStatus.DOWNLOADING:
                            # Proceso terminado, verificar estado final
                            final_progress = item.sftp_manager.get_remote_download_progress(item.remote_log_file)
                            if final_progress >= 99.0:
                                item.progress = 100.0
                                item.status = DownloadStatus.COMPLETED
                                item.end_time = datetime.now()
                                print(f"✅ Descarga remota completada (verificada por monitor): {item.filename}")
                            else:
                                item.status = DownloadStatus.ERROR
                                item.error_message = "El proceso remoto terminó inesperadamente."
                                print(f"❌ Error en descarga remota (verificada por monitor): {item.filename}")
                            
                            self._notify_status_change(item.id)

                    except Exception as e:
                        print(f"Error monitoreando {item.filename}: {e}")
                
                # Usar wait con timeout en lugar de sleep simple
                if self.stop_event.wait(timeout=1.0):
                    break
                    
            except Exception as e:
                print(f"Error en monitor de descargas remotas: {e}")
                # En caso de error, esperar un poco antes de reintentar
                if self.stop_event.wait(timeout=5.0):
                    break
        
        print("🔚 Monitor de descargas remotas terminado")

    def _download_manager(self):
        """Gestiona la cola de descargas"""
        print("🎯 Iniciando gestor de descargas...")
        
        while self.is_running and not self.stop_event.is_set():
            try:
                # Esperar si está pausado con timeout
                if not self.pause_event.wait(timeout=1.0):
                    continue
                
                if self.stop_event.is_set():
                    break
                
                # Verificar si hay slots disponibles
                if len(self.active_downloads) >= self.max_concurrent_downloads:
                    # Usar wait con timeout en lugar de sleep simple
                    if self.stop_event.wait(timeout=1.0):
                        break
                    continue
                
                # Obtener siguiente descarga de la cola
                try:
                    download_id = self.download_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # Verificar que la descarga aún existe y está en cola
                with self.downloads_lock:
                    if download_id not in self.downloads:
                        continue
                    
                    download_item = self.downloads[download_id]
                    
                    # Crear y empezar hilo de descarga
                    if download_item.is_remote:
                        thread = threading.Thread(target=self._start_remote_download, args=(download_id,))
                    else:
                        thread = threading.Thread(target=self._start_local_download, args=(download_id,))
                        
                    self.active_downloads[download_id] = thread
                    thread.daemon = True
                    thread.start()
                    
            except Exception as e:
                print(f"Error en gestor de descargas: {e}")
                # En caso de error, esperar un poco antes de reintentar
                if self.stop_event.wait(timeout=2.0):
                    break
        
        print("🔚 Gestor de descargas terminado")

    def _start_remote_download(self, download_id: str):
        """Inicia una descarga remota usando SFTP manager."""
        item = self.downloads[download_id]
        
        if not self.sftp_manager or not self.sftp_manager.is_connected():
            item.status = DownloadStatus.ERROR
            item.error_message = "SFTP no conectado."
            self._notify_status_change(download_id)
            return

        try:
            item.status = DownloadStatus.DOWNLOADING
            item.start_time = datetime.now()
            self._notify_status_change(download_id)

            result = self.sftp_manager.remote_download(item.url, item.remote_path, item.filename)

            if result.get('success'):
                item.remote_pid = result.get('pid')
                item.remote_log_file = result.get('log_file')
                print(f"ℹ️ Descarga remota iniciada: PID={item.remote_pid}, Log={item.remote_log_file}")
                
                # Si la descarga fue muy rápida y ya completó
                if result.get('completed_fast'):
                    item.status = DownloadStatus.COMPLETED
                    item.progress = 100.0
                    item.end_time = datetime.now()
                    self._notify_status_change(download_id)
            else:
                item.status = DownloadStatus.ERROR
                item.error_message = result.get('message', "Error desconocido al iniciar descarga remota.")
                self._notify_status_change(download_id)

        except Exception as e:
            item.status = DownloadStatus.ERROR
            item.error_message = f"Excepción en descarga remota: {e}"
            self._notify_status_change(download_id)
        finally:
            with self.downloads_lock:
                if download_id in self.active_downloads:
                    del self.active_downloads[download_id]

    def _start_local_download(self, download_id: str):
        """Descarga un archivo localmente usando requests"""
        item = self.downloads[download_id]
        
        try:
            item.status = DownloadStatus.DOWNLOADING
            item.start_time = datetime.now()
            self._notify_status_change(download_id)
            
            # Crear directorio local si no existe
            os.makedirs(os.path.dirname(item.local_path), exist_ok=True)
            
            # Realizar request HEAD para obtener tamaño del archivo
            try:
                head_response = self.session.head(item.url, timeout=self.timeout)
                if 'content-length' in head_response.headers:
                    total_size = int(head_response.headers['content-length'])
                    item.file_size_bytes = total_size
                    item.file_size = self._format_size(total_size)
                    self._notify_status_change(download_id)
                else:
                    total_size = 0
            except:
                total_size = 0
            
            # Realizar descarga con streaming
            response = self.session.get(item.url, stream=True, timeout=self.timeout)
            response.raise_for_status()
            
            downloaded = 0
            start_time = time.time()
            last_update_time = start_time
            chunk_count = 0

            with open(item.local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if self.stop_event.is_set():
                        break

                    # Esperar si está pausado
                    if not self.pause_event.wait(timeout=0.1):
                        # Si se pausó, marcar como pausado y salir
                        with self.downloads_lock:
                            item.status = DownloadStatus.PAUSED
                            self._notify_status_change(download_id)
                        return

                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        chunk_count += 1

                        # Actualizar progreso de manera más inteligente
                        current_time = time.time()
                        # Actualizar cada 0.3 segundos O cada 100 chunks para mejor fluidez
                        if (current_time - last_update_time >= 0.3) or (chunk_count % 100 == 0):
                            self._update_progress(download_id, downloaded, total_size, start_time)
                            last_update_time = current_time
            
            # Verificar si se completó exitosamente
            if not self.stop_event.is_set():
                # Actualización final del progreso
                self._update_progress(download_id, downloaded, total_size, start_time)

                with self.downloads_lock:
                    item.status = DownloadStatus.COMPLETED
                    item.progress = 100.0
                    item.end_time = datetime.now()
                    item.downloaded_bytes = downloaded

                    # Subir archivo al servidor SFTP si está configurado
                    if item.sftp_manager and item.sftp_manager.is_connected():
                        self._upload_to_sftp(download_id)

                    self._notify_status_change(download_id)
            
        except requests.exceptions.RequestException as e:
            with self.downloads_lock:
                if item.retry_count < item.max_retries:
                    # Reintentar
                    item.retry_count += 1
                    item.status = DownloadStatus.QUEUED
                    self.download_queue.put(download_id)
                    print(f"Reintentando descarga {download_id} ({item.retry_count}/{item.max_retries})")
                else:
                    item.status = DownloadStatus.ERROR
                    item.error_message = f"Error de descarga: {e}"
                    item.end_time = datetime.now()
                
                self._notify_status_change(download_id)
                
        except Exception as e:
            with self.downloads_lock:
                item.status = DownloadStatus.ERROR
                item.error_message = f"Error inesperado: {e}"
                item.end_time = datetime.now()
                self._notify_status_change(download_id)
        
        finally:
            # Limpiar hilo activo
            if download_id in self.active_downloads:
                del self.active_downloads[download_id]
    
    def _update_progress(self, download_id: str, downloaded: int, total_size: int, start_time: float):
        """Actualiza el progreso de descarga"""
        with self.downloads_lock:
            item = self.downloads[download_id]
            
            item.downloaded_bytes = downloaded
            
            if total_size > 0:
                item.progress = (downloaded / total_size) * 100
            
            # Calcular velocidad
            elapsed_time = time.time() - start_time
            if elapsed_time > 0:
                speed_bytes_per_sec = downloaded / elapsed_time
                speed_bytes_per_sec = int(speed_bytes_per_sec)  # Convertir a entero para _format_size
                item.speed = f"{self._format_size(speed_bytes_per_sec)}/s"
                
                # Calcular ETA
                if total_size > 0 and speed_bytes_per_sec > 0:
                    remaining_bytes = total_size - downloaded
                    eta_seconds = remaining_bytes / speed_bytes_per_sec
                    item.eta = self._format_time(eta_seconds)
            
            self._notify_progress_change(download_id)
    
    def _upload_to_sftp(self, download_id: str):
        """Sube el archivo descargado al servidor SFTP"""
        item = None
        try:
            with self.downloads_lock:
                item = self.downloads[download_id]
            
            if not item.sftp_manager or not item.sftp_manager.is_connected():
                return
            
            # Construir ruta remota completa
            remote_file_path = os.path.join(item.remote_path, item.filename).replace('\\', '/')
            
            # Callback de progreso para la subida
            def upload_progress(transferred, total):
                if total > 0:
                    upload_percent = (transferred / total) * 100
                    with self.downloads_lock:
                        item.progress = 100.0 + (upload_percent * 0.1)  # Progreso extra para subida
                    self._notify_progress_change(download_id)
            
            # Subir archivo
            item.sftp_manager.upload_file(
                item.local_path,
                remote_file_path,
                progress_callback=upload_progress
            )
            
            # Eliminar archivo temporal
            try:
                os.remove(item.local_path)
            except Exception as e:
                print(f"Error eliminando archivo temporal: {e}")
            
            print(f"Archivo {item.filename} subido exitosamente a {remote_file_path}")
            
        except Exception as e:
            if item:
                with self.downloads_lock:
                    item.error_message = f"Error subiendo archivo: {e}"
                print(f"Error subiendo archivo {item.filename}: {e}")
            else:
                print(f"Error subiendo archivo: {e}")
    
    def _clean_filename(self, filename: str) -> str:
        """Limpia el nombre de archivo para ser válido en el sistema de archivos"""
        # Eliminar caracteres no válidos
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Limitar longitud
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        return filename
    
    def _format_size(self, size_bytes: int) -> str:
        """Formatea el tamaño en bytes a formato legible"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        import math
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s} {size_names[i]}"
    
    def _format_time(self, seconds: float) -> str:
        """Formatea tiempo en segundos a formato legible"""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            return f"{int(seconds // 60)}m {int(seconds % 60)}s"
        else:
            return f"{int(seconds // 3600)}h {int((seconds % 3600) // 60)}m"
    
    def _notify_status_change(self, download_id: str):
        """Notifica cambio de estado"""
        for callback in self.status_callbacks:
            try:
                callback(download_id)
            except Exception as e:
                print(f"Error en callback de estado: {e}")
    
    def _notify_progress_change(self, download_id: str):
        """Notifica cambio de progreso"""
        for callback in self.progress_callbacks:
            try:
                callback(download_id)
            except Exception as e:
                print(f"Error en callback de progreso: {e}")
    
    def add_progress_callback(self, callback: Callable):
        """Agrega callback para cambios de progreso"""
        self.progress_callbacks.append(callback)
    
    def add_status_callback(self, callback: Callable):
        """Agrega callback para cambios de estado"""
        self.status_callbacks.append(callback)
    
    def get_download(self, download_id: str) -> Optional[DownloadItem]:
        """Obtiene información de una descarga"""
        with self.downloads_lock:
            return self.downloads.get(download_id)
    
    def get_downloads_status(self) -> List[Dict[str, Any]]:
        """Obtiene estado de todas las descargas"""
        with self.downloads_lock:
            return [download.to_dict() for download in self.downloads.values()]
    
    def get_statistics(self) -> Dict[str, int]:
        """Obtiene estadísticas de descargas"""
        with self.downloads_lock:
            stats = {
                'total': len(self.downloads),
                'queued': 0,
                'active': 0,
                'completed': 0,
                'errors': 0,
                'cancelled': 0,
                'paused': 0
            }
            
            for download in self.downloads.values():
                if download.status == DownloadStatus.QUEUED:
                    stats['queued'] += 1
                elif download.status == DownloadStatus.DOWNLOADING:
                    stats['active'] += 1
                elif download.status == DownloadStatus.COMPLETED:
                    stats['completed'] += 1
                elif download.status == DownloadStatus.ERROR:
                    stats['errors'] += 1
                elif download.status == DownloadStatus.CANCELLED:
                    stats['cancelled'] += 1
                elif download.status == DownloadStatus.PAUSED:
                    stats['paused'] += 1
            
            return stats
    
    def save_queue_state(self, filename: str = 'download_queue.json'):
        """Guarda el estado de la cola de descargas"""
        try:
            state = {
                'downloads': [download.to_dict() for download in self.downloads.values()],
                'max_concurrent_downloads': self.max_concurrent_downloads,
                'saved_at': datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Error guardando estado de cola: {e}")
    
    def load_queue_state(self, filename: str = 'download_queue.json'):
        """Carga el estado de la cola de descargas"""
        try:
            if not os.path.exists(filename):
                return
            
            with open(filename, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            # Restaurar configuración
            self.max_concurrent_downloads = state.get('max_concurrent_downloads', 3)
            
            # Restaurar descargas (solo las que no estaban activas)
            for download_data in state.get('downloads', []):
                if download_data['status'] in ['En cola', 'Error']:
                    # Recrear item de descarga
                    download_item = DownloadItem(
                        id=download_data['id'],
                        url=download_data['url'],
                        filename=download_data['filename'],
                        remote_path=download_data['remote_path'],
                        local_path=download_data['local_path'],
                        status=DownloadStatus.QUEUED,  # Resetear a cola
                        retry_count=download_data.get('retry_count', 0),
                        max_retries=download_data.get('max_retries', 3)
                    )
                    
                    self.downloads[download_data['id']] = download_item
                    self.download_queue.put(download_data['id'])
            
            print(f"Estado de cola cargado: {len(self.downloads)} descargas restauradas")
            
        except Exception as e:
            print(f"Error cargando estado de cola: {e}")
    
    def set_sftp_manager(self, sftp_manager):
        """Establece o actualiza el gestor SFTP para operaciones remotas"""
        self.sftp_manager = sftp_manager
        print(f"✅ SFTP Manager actualizado en Download Manager")
        return True
    
    def clear_queue(self):
        """Elimina todas las descargas pendientes en la cola"""
        print("🧹 Limpiando cola de descargas...")
        try:
            # Crear una nueva cola y reemplazar la antigua
            with self.downloads_lock:
                old_queue = self.download_queue
                self.download_queue = queue.Queue()
                
                # Marcar como canceladas las descargas en cola
                while not old_queue.empty():
                    try:
                        download_id = old_queue.get_nowait()
                        if download_id in self.downloads:
                            self.downloads[download_id].status = DownloadStatus.CANCELLED
                            print(f"❌ Descarga cancelada: {self.downloads[download_id].filename}")
                    except queue.Empty:
                        break
                        
            print("✅ Cola de descargas limpiada")
            return True
        except Exception as e:
            print(f"❌ Error limpiando cola: {e}")
            return False
    
    def __del__(self):
        """Destructor - asegurar limpieza"""
        self.stop_downloads()

# Alias para mantener compatibilidad
DownloadManagerRequests = DownloadManager
