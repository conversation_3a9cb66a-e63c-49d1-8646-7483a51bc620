#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prueba de descarga con un archivo de video más grande y realista
"""

import paramiko
import time

# --- Configuración ---
HOSTNAME = "***************"
PORT = 22
USERNAME = "root"
PASSWORD = "5dhCkm5Dz1BpWgxZpdBisrOVo"

def test_video_download():
    """Prueba descarga de un archivo de video más grande"""
    
    print("🎬 PROBANDO DESCARGA DE VIDEO...")
    
    client = None
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        client.connect(
            hostname=HOSTNAME,
            port=PORT,
            username=USERNAME,
            password=PASSWORD,
            timeout=15,
            allow_agent=False,
            look_for_keys=False,
            compress=True
        )
        
        print("✅ Conectado al servidor")
        
        # URL de video de prueba (archivo pequeño de ejemplo)
        test_url = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
        test_file = "/tmp/test_video_download.mp4"
        log_file = "/tmp/wget_video_test.log"
        
        print(f"📥 URL: {test_url}")
        print(f"💾 Destino: {test_file}")
        
        # Limpiar archivos anteriores
        cleanup_cmd = f'rm -f {test_file} {log_file}'
        stdin, stdout, stderr = client.exec_command(cleanup_cmd)
        
        # Comando wget con progreso visible
        wget_cmd = (
            f'nohup wget --progress=dot:mega --timeout=60 --tries=3 '
            f'--user-agent="XCIPTV" '
            f'--output-document="{test_file}" "{test_url}" > {log_file} 2>&1 &'
        )
        
        print(f"🚀 Ejecutando wget...")
        start_time = time.time()
        
        # Ejecutar comando
        stdin, stdout, stderr = client.exec_command(wget_cmd)
        time.sleep(1)
        
        # Monitorear progreso por hasta 60 segundos
        max_wait = 60
        waited = 0
        
        print("⏳ Monitoreando descarga...")
        
        while waited < max_wait:
            time.sleep(3)
            waited += 3
            
            # Verificar si hay proceso wget corriendo
            check_process_cmd = f'ps aux | grep wget | grep "{test_url}" | grep -v grep'
            stdin, stdout, stderr = client.exec_command(check_process_cmd)
            process_info = stdout.read().decode('utf-8', errors='ignore').strip()
            
            # Verificar si el archivo existe
            check_file_cmd = f'[ -f "{test_file}" ] && stat -c "%s" "{test_file}" || echo "0"'
            stdin, stdout, stderr = client.exec_command(check_file_cmd)
            file_size = stdout.read().decode('utf-8', errors='ignore').strip()
            
            if process_info:
                print(f"🔄 [{waited}s] Descarga activa, archivo: {file_size} bytes")
            elif file_size != "0":
                print(f"✅ [{waited}s] Descarga completada, tamaño final: {file_size} bytes")
                break
            else:
                print(f"⚠️ [{waited}s] Sin proceso activo ni archivo")
        
        # Verificación final
        print("\n📊 RESULTADO FINAL:")
        
        # Verificar archivo final
        final_check_cmd = f'[ -f "{test_file}" ] && ls -lh "{test_file}" || echo "Archivo no encontrado"'
        stdin, stdout, stderr = client.exec_command(final_check_cmd)
        final_result = stdout.read().decode('utf-8', errors='ignore').strip()
        print(f"📄 {final_result}")
        
        # Mostrar log completo
        print("\n📝 LOG DE WGET:")
        log_cmd = f'cat {log_file} 2>/dev/null || echo "Sin log"'
        stdin, stdout, stderr = client.exec_command(log_cmd)
        log_content = stdout.read().decode('utf-8', errors='ignore')
        print(log_content)
        
        elapsed = time.time() - start_time
        print(f"\n⏱️ Tiempo total: {elapsed:.2f}s")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    test_video_download()
