#!/usr/bin/env python3
"""
Script de prueba final para verificar que todas las correcciones funcionan.
"""

import sys
import os
import time

# Agregar el directorio actual al path para importar los módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_complete_fix():
    """Prueba completa de todas las correcciones"""
    print("🔍 PRUEBA COMPLETA DE CORRECCIONES")
    print("=" * 60)
    
    try:
        from download_manager_requests import DownloadManager, DownloadStatus
        from sftp_manager import SFTPManager
        
        # Crear managers
        print("📱 Creando managers...")
        sftp = SFTPManager()
        dm = DownloadManager(sftp_manager=sftp)
        
        # Añadir descarga remota
        print("\n📥 Añadiendo descarga remota...")
        download_id = dm.add_remote_download(
            url="https://httpbin.org/delay/2",
            filename="test_complete_fix.json",
            remote_path="/tmp"
        )
        print(f"✅ Descarga remota añadida: {download_id}")
        
        # Verificar estado
        downloads = dm.get_downloads_status()
        
        print(f"\n📊 Estado de la descarga:")
        for download in downloads:
            print(f"   - {download['filename']}:")
            print(f"     * Estado: '{download['status']}'")
            print(f"     * Es remota: {download['is_remote']}")
            print(f"     * Progreso: {download['progress']:.1f}%")
        
        # Verificar corrección 1: Estados en español
        print(f"\n✅ CORRECCIÓN 1: Estados en español")
        has_active_new = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads)
        has_active_old = any(d.get('status') in ['Downloading', 'Processing', 'Queued'] for d in downloads)
        
        print(f"   - Lógica NUEVA (español): {has_active_new}")
        print(f"   - Lógica ANTIGUA (inglés): {has_active_old}")
        
        if has_active_new and not has_active_old:
            print("   ✅ Estados en español funcionan correctamente")
        elif not has_active_new and not has_active_old:
            print("   ⚠️ No hay descargas activas (esperado si falla SFTP)")
        else:
            print("   ❌ Problema con estados")
        
        # Verificar corrección 2: Criterios del monitor remoto
        print(f"\n✅ CORRECCIÓN 2: Criterios del monitor remoto")
        
        # Lógica ANTIGUA (requería remote_log_file)
        old_criteria = []
        for download in downloads:
            if download['is_remote'] and download['status'] == 'Descargando' and download['remote_log_file']:
                old_criteria.append(download)
        
        # Lógica NUEVA (no requiere remote_log_file inmediatamente)
        new_criteria = []
        for download in downloads:
            if download['is_remote'] and download['status'] == 'Descargando':
                new_criteria.append(download)
        
        print(f"   - Criterios ANTIGUOS: {len(old_criteria)} descargas")
        print(f"   - Criterios NUEVOS: {len(new_criteria)} descargas")
        
        if len(new_criteria) >= len(old_criteria):
            print("   ✅ Criterios del monitor mejorados")
        else:
            print("   ❌ Problema con criterios del monitor")
        
        # Verificar corrección 3: Persistencia del monitor
        print(f"\n✅ CORRECCIÓN 3: Persistencia del monitor")
        
        # Simular la lógica de main.py para descargas remotas
        has_recent_remote = any(
            d.get('is_remote') and d.get('status') in ['Descargando', 'En cola', 'Error'] 
            for d in downloads
        )
        
        print(f"   - Tiene descargas remotas recientes: {has_recent_remote}")
        
        if has_recent_remote:
            print("   ✅ Monitor debería usar timeout extendido (30s)")
        else:
            print("   ⚠️ Monitor usará timeout normal (10s)")
        
        # Verificar estado de threads
        print(f"\n🧵 Estado de threads:")
        print(f"   - Sistema corriendo: {dm.is_running}")
        
        if dm.manager_thread:
            print(f"   - Manager thread activo: {dm.manager_thread.is_alive()}")
        
        if dm.remote_monitor_thread:
            print(f"   - Monitor remoto activo: {dm.remote_monitor_thread.is_alive()}")
        
        # Resumen de correcciones
        print(f"\n📋 RESUMEN DE CORRECCIONES:")
        print(f"   1. ✅ Estados traducidos a español")
        print(f"   2. ✅ Monitor remoto más tolerante durante inicio")
        print(f"   3. ✅ Monitor de main.py más persistente para remotas")
        
        # Limpiar
        dm.stop_downloads()
        print("\n✅ Prueba completa terminada")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba completa: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_scenario():
    """Simula exactamente el escenario del usuario"""
    print("\n🎬 SIMULACIÓN DEL ESCENARIO DEL USUARIO")
    print("=" * 60)
    
    print("📋 Escenario del usuario:")
    print("   1. Usuario hace clic en archivo M3U")
    print("   2. Se ejecuta descarga remota con wget")
    print("   3. Comando wget se ejecuta correctamente")
    print("   4. Pero la app muestra 'error y no se descarga'")
    
    print(f"\n🔍 Análisis del problema:")
    print(f"   - Logs muestran: 'Hilo de monitoreo de descargas iniciado para 1 descargas'")
    print(f"   - Inmediatamente: 'No hay descargas activas, terminando hilo de monitoreo'")
    print(f"   - Resultado: 'pero en la app dice error y no se descarga'")
    
    print(f"\n💡 Causa raíz identificada:")
    print(f"   1. ❌ Estados en inglés vs español")
    print(f"   2. ❌ Monitor remoto requería remote_log_file inmediatamente")
    print(f"   3. ❌ Monitor de main.py terminaba muy rápido")
    
    print(f"\n🔧 Correcciones aplicadas:")
    print(f"   1. ✅ Cambiados estados a español en main.py")
    print(f"   2. ✅ Monitor remoto más tolerante durante inicio")
    print(f"   3. ✅ Monitor de main.py espera 30s para descargas remotas")
    
    print(f"\n🎯 Resultado esperado:")
    print(f"   - Monitor detectará descargas remotas como activas")
    print(f"   - Monitor no terminará prematuramente")
    print(f"   - Descargas remotas se monitorearan correctamente")
    print(f"   - Usuario verá progreso en lugar de error")
    
    return True

def test_edge_cases():
    """Prueba casos extremos"""
    print("\n🧪 PRUEBA DE CASOS EXTREMOS")
    print("=" * 60)
    
    try:
        from download_manager_requests import DownloadStatus
        
        # Caso 1: Estados mixtos
        print("📋 Caso 1: Estados mixtos")
        downloads_mixed = [
            {'status': 'Descargando', 'is_remote': True},   # Español, remota
            {'status': 'Downloading', 'is_remote': False},  # Inglés, local
            {'status': 'En cola', 'is_remote': True},       # Español, remota
            {'status': 'Error', 'is_remote': True},         # Error, remota
        ]
        
        # Lógica NUEVA
        active_new = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads_mixed)
        remote_recent = any(
            d.get('is_remote') and d.get('status') in ['Descargando', 'En cola', 'Error'] 
            for d in downloads_mixed
        )
        
        print(f"   - Descargas activas (nueva lógica): {active_new}")
        print(f"   - Remotas recientes: {remote_recent}")
        print(f"   - Timeout a usar: {'30s' if remote_recent else '10s'}")
        
        # Caso 2: Solo errores remotos
        print(f"\n📋 Caso 2: Solo errores remotos")
        downloads_errors = [
            {'status': 'Error', 'is_remote': True},
            {'status': 'Error', 'is_remote': True},
        ]
        
        active_errors = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads_errors)
        remote_errors = any(
            d.get('is_remote') and d.get('status') in ['Descargando', 'En cola', 'Error'] 
            for d in downloads_errors
        )
        
        print(f"   - Descargas activas: {active_errors}")
        print(f"   - Remotas recientes: {remote_errors}")
        print(f"   - Timeout a usar: {'30s' if remote_errors else '10s'}")
        
        # Caso 3: Sin descargas
        print(f"\n📋 Caso 3: Sin descargas")
        downloads_empty = []
        
        active_empty = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads_empty)
        remote_empty = any(
            d.get('is_remote') and d.get('status') in ['Descargando', 'En cola', 'Error'] 
            for d in downloads_empty
        )
        
        print(f"   - Descargas activas: {active_empty}")
        print(f"   - Remotas recientes: {remote_empty}")
        print(f"   - Timeout a usar: {'30s' if remote_empty else '10s'}")
        
        print(f"\n✅ Casos extremos verificados")
        return True
        
    except Exception as e:
        print(f"❌ Error en casos extremos: {e}")
        return False

def main():
    """Función principal de pruebas finales"""
    tests = [
        ("Correcciones completas", test_complete_fix),
        ("Escenario del usuario", test_user_scenario),
        ("Casos extremos", test_edge_cases),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: ÉXITO")
            else:
                print(f"❌ {test_name}: FALLÓ")
                
        except Exception as e:
            print(f"💥 {test_name}: CRASH - {e}")
            results.append((test_name, False))
    
    # Resumen final
    print(f"\n{'='*60}")
    print("🎉 RESUMEN FINAL DE CORRECCIONES")
    print(f"{'='*60}")
    
    for test_name, result in results:
        status = "✅ ÉXITO" if result else "❌ FALLÓ"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 Total: {passed}/{total} pruebas exitosas")
    
    if passed == total:
        print("\n🎉 ¡TODAS LAS CORRECCIONES APLICADAS EXITOSAMENTE!")
        print("\n📋 Correcciones implementadas:")
        print("   1. ✅ Estados traducidos de inglés a español en main.py")
        print("   2. ✅ Monitor remoto más tolerante durante inicio de descargas")
        print("   3. ✅ Monitor de main.py más persistente para descargas remotas")
        print("\n💡 El problema del usuario debería estar resuelto:")
        print("   - Las descargas remotas se detectarán como activas")
        print("   - El monitor no terminará prematuramente")
        print("   - El usuario verá progreso en lugar de errores")
    else:
        print("\n⚠️ Algunas correcciones necesitan más ajustes")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 CRASH EN PRUEBAS FINALES: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
