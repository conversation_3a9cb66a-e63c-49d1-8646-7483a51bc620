#!/usr/bin/env python3
"""
Script para probar que la corrección del timing funciona correctamente.
"""

import sys
import os
import time
import threading

# Agregar el directorio actual al path para importar los módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_monitor_timing():
    """Prueba que el monitor no termine prematuramente durante el inicio"""
    print("🔍 PRUEBA DE TIMING DEL MONITOR")
    print("=" * 60)
    
    try:
        from download_manager_requests import DownloadManager, DownloadStatus, DownloadItem
        from sftp_manager import SFTPManager
        import uuid
        from datetime import datetime
        
        # Crear managers
        print("📱 Creando managers...")
        sftp = SFTPManager()
        dm = DownloadManager(sftp_manager=sftp)
        
        # Simular una descarga remota en estado DOWNLOADING pero sin log_file aún
        print("\n🎭 Simulando descarga remota en proceso de inicio...")
        
        download_id = f"dl_{int(time.time() * 1000)}_test"
        
        # Crear item manualmente para simular el estado intermedio
        item = DownloadItem(
            id=download_id,
            url="https://httpbin.org/delay/2",
            filename="test_timing.json",
            remote_path="/tmp",
            local_path=None,
            is_remote=True
        )
        
        # Configurar estado DOWNLOADING pero SIN remote_log_file (simula timing issue)
        item.status = DownloadStatus.DOWNLOADING
        item.start_time = datetime.now()
        item.remote_pid = None  # Aún no configurado
        item.remote_log_file = None  # Aún no configurado
        
        # Añadir al manager
        with dm.downloads_lock:
            dm.downloads[download_id] = item
        
        print(f"✅ Descarga simulada añadida: {download_id}")
        print(f"   - Estado: {item.status.value}")
        print(f"   - Es remota: {item.is_remote}")
        print(f"   - PID remoto: {item.remote_pid}")
        print(f"   - Log remoto: {item.remote_log_file}")
        
        # Iniciar el sistema de descargas
        print(f"\n🚀 Iniciando sistema de descargas...")
        dm.start_downloads()
        
        # Verificar que el monitor no termine inmediatamente
        print(f"\n👁️ Verificando comportamiento del monitor...")
        
        # Esperar un poco para que el monitor tenga tiempo de verificar
        time.sleep(3)
        
        # Verificar estado del monitor
        monitor_alive = dm.remote_monitor_thread and dm.remote_monitor_thread.is_alive()
        print(f"   - Monitor remoto activo: {monitor_alive}")
        
        if monitor_alive:
            print("   ✅ El monitor NO terminó prematuramente")
            print("   💡 La corrección del timing funciona")
        else:
            print("   ❌ El monitor terminó prematuramente")
            print("   💡 La corrección del timing necesita más ajustes")
        
        # Verificar lógica de detección
        downloads = dm.get_downloads_status()
        has_active = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads)
        
        print(f"\n📊 Verificación de lógica de detección:")
        print(f"   - Descargas totales: {len(downloads)}")
        print(f"   - Tiene descargas activas: {has_active}")
        
        for download in downloads:
            print(f"   - {download['filename']}:")
            print(f"     * Estado: {download['status']}")
            print(f"     * Es remota: {download['is_remote']}")
            print(f"     * PID remoto: {download['remote_pid']}")
            print(f"     * Log remoto: {download['remote_log_file']}")
        
        # Simular configuración tardía de log_file
        print(f"\n⏰ Simulando configuración tardía de remote_log_file...")
        
        with dm.downloads_lock:
            if download_id in dm.downloads:
                dm.downloads[download_id].remote_log_file = "/tmp/test_log.log"
                dm.downloads[download_id].remote_pid = "12345"
                print("   ✅ remote_log_file y remote_pid configurados")
        
        # Verificar que ahora el monitor puede procesar la descarga
        time.sleep(2)
        
        print(f"\n📊 Estado después de configurar log_file:")
        downloads = dm.get_downloads_status()
        
        for download in downloads:
            if download['id'] == download_id:
                print(f"   - {download['filename']}:")
                print(f"     * Estado: {download['status']}")
                print(f"     * PID remoto: {download['remote_pid']}")
                print(f"     * Log remoto: {download['remote_log_file']}")
        
        # Limpiar
        dm.stop_downloads()
        print("\n✅ Prueba de timing completada")
        
        return monitor_alive
        
    except Exception as e:
        print(f"❌ Error en prueba de timing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_monitor_criteria():
    """Prueba los criterios del monitor paso a paso"""
    print("\n🔍 PRUEBA DE CRITERIOS DEL MONITOR")
    print("=" * 60)
    
    try:
        from download_manager_requests import DownloadManager, DownloadStatus
        from sftp_manager import SFTPManager
        
        # Crear managers
        sftp = SFTPManager()
        dm = DownloadManager(sftp_manager=sftp)
        
        # Simular diferentes estados de descarga
        test_cases = [
            {
                "name": "Descarga local activa",
                "is_remote": False,
                "status": DownloadStatus.DOWNLOADING,
                "remote_log_file": None,
                "should_monitor": False
            },
            {
                "name": "Descarga remota en cola",
                "is_remote": True,
                "status": DownloadStatus.QUEUED,
                "remote_log_file": None,
                "should_monitor": False
            },
            {
                "name": "Descarga remota descargando sin log",
                "is_remote": True,
                "status": DownloadStatus.DOWNLOADING,
                "remote_log_file": None,
                "should_monitor": True  # NUEVA LÓGICA: debería incluirse
            },
            {
                "name": "Descarga remota descargando con log",
                "is_remote": True,
                "status": DownloadStatus.DOWNLOADING,
                "remote_log_file": "/tmp/test.log",
                "should_monitor": True
            },
            {
                "name": "Descarga remota completada",
                "is_remote": True,
                "status": DownloadStatus.COMPLETED,
                "remote_log_file": "/tmp/test.log",
                "should_monitor": False
            }
        ]
        
        print("📋 Probando criterios del monitor:")
        
        for i, case in enumerate(test_cases):
            print(f"\n   {i+1}. {case['name']}:")
            
            # Simular la lógica del monitor (NUEVA)
            meets_criteria = case['is_remote'] and case['status'] == DownloadStatus.DOWNLOADING
            
            print(f"      - is_remote: {case['is_remote']}")
            print(f"      - status: {case['status'].value}")
            print(f"      - remote_log_file: {case['remote_log_file']}")
            print(f"      - Cumple criterios (NUEVA lógica): {meets_criteria}")
            print(f"      - Debería monitorearse: {case['should_monitor']}")
            
            if meets_criteria == case['should_monitor']:
                print(f"      ✅ CORRECTO")
            else:
                print(f"      ❌ INCORRECTO")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba de criterios: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_scenario():
    """Prueba un escenario real como el del usuario"""
    print("\n🌐 PRUEBA DE ESCENARIO REAL")
    print("=" * 60)
    
    try:
        from download_manager_requests import DownloadManager, DownloadStatus
        from sftp_manager import SFTPManager
        
        # Crear managers
        sftp = SFTPManager()
        dm = DownloadManager(sftp_manager=sftp)
        
        print("📥 Simulando escenario del usuario:")
        print("   1. Usuario hace clic en archivo M3U")
        print("   2. Se añade descarga remota")
        print("   3. Sistema inicia descarga")
        print("   4. Monitor debe detectar descarga activa")
        
        # Paso 1: Añadir descarga remota
        download_id = dm.add_remote_download(
            url="https://zonamovie.live:8443/series/test/test.mkv",
            filename="Los Caballeros del Zodiaco - Saint Seiya 1986 S01 E114",
            remote_path="/media"
        )
        
        print(f"\n✅ Descarga añadida: {download_id}")
        
        # Paso 2: Verificar estado inicial
        downloads = dm.get_downloads_status()
        
        print(f"\n📊 Estado inicial:")
        for download in downloads:
            print(f"   - {download['filename']}:")
            print(f"     * Estado: {download['status']}")
            print(f"     * Es remota: {download['is_remote']}")
        
        # Paso 3: Verificar detección de actividad
        has_active_old = any(d.get('status') in ['Downloading', 'Processing', 'Queued'] for d in downloads)
        has_active_new = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads)
        
        print(f"\n🔍 Detección de actividad:")
        print(f"   - Lógica ANTIGUA (inglés): {has_active_old}")
        print(f"   - Lógica NUEVA (español): {has_active_new}")
        
        # Paso 4: Verificar criterios del monitor
        monitor_would_check = []
        for download in downloads:
            if download['is_remote'] and download['status'] == 'Descargando':
                monitor_would_check.append(download)
        
        print(f"\n👁️ Monitor remoto:")
        print(f"   - Descargas que verificaría: {len(monitor_would_check)}")
        
        if monitor_would_check:
            print("   ✅ El monitor DEBERÍA mantenerse activo")
        else:
            print("   ❌ El monitor terminaría prematuramente")
        
        # Paso 5: Verificar threads
        print(f"\n🧵 Estado de threads:")
        print(f"   - Sistema corriendo: {dm.is_running}")
        
        if dm.manager_thread:
            print(f"   - Manager thread activo: {dm.manager_thread.is_alive()}")
        
        if dm.remote_monitor_thread:
            print(f"   - Monitor thread activo: {dm.remote_monitor_thread.is_alive()}")
        
        # Resultado
        success = has_active_new and len(monitor_would_check) > 0
        
        if success:
            print(f"\n🎉 ¡ESCENARIO EXITOSO!")
            print(f"   ✅ La lógica detecta descargas activas")
            print(f"   ✅ El monitor debería mantenerse activo")
        else:
            print(f"\n⚠️ Escenario problemático")
            print(f"   ❌ Hay problemas en la detección o monitoreo")
        
        dm.stop_downloads()
        return success
        
    except Exception as e:
        print(f"❌ Error en escenario real: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Función principal de pruebas"""
    tests = [
        ("Timing del monitor", test_monitor_timing),
        ("Criterios del monitor", test_monitor_criteria),
        ("Escenario real", test_real_scenario),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: ÉXITO")
            else:
                print(f"❌ {test_name}: FALLÓ")
                
        except Exception as e:
            print(f"💥 {test_name}: CRASH - {e}")
            results.append((test_name, False))
    
    # Resumen
    print(f"\n{'='*60}")
    print("📊 RESUMEN DE CORRECCIÓN DE TIMING")
    print(f"{'='*60}")
    
    for test_name, result in results:
        status = "✅ ÉXITO" if result else "❌ FALLÓ"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 Total: {passed}/{total} pruebas exitosas")
    
    if passed == total:
        print("🎉 ¡CORRECCIÓN DE TIMING EXITOSA!")
        print("✅ El monitor debería mantenerse activo durante el inicio")
        print("✅ Las descargas remotas deberían monitorearse correctamente")
    else:
        print("⚠️ La corrección de timing necesita más ajustes")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 CRASH EN SCRIPT DE TIMING: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
