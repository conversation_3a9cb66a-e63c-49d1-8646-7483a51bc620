#!/usr/bin/env python3
"""
Script para diagnosticar exactamente qué está pasando en el momento crítico
cuando el monitor de main.py termina prematuramente.
"""

import sys
import os
import time
import threading

# Agregar el directorio actual al path para importar los módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_monitor_logic():
    """Simula exactamente la lógica del monitor de main.py"""
    print("🔍 DIAGNÓSTICO DEL MONITOR DE MAIN.PY")
    print("=" * 60)
    
    try:
        from download_manager_requests import DownloadManager, DownloadStatus
        from sftp_manager import SFTPManager
        
        # Crear managers
        print("📱 Creando managers...")
        sftp = SFTPManager()
        dm = DownloadManager(sftp_manager=sftp)
        
        # Añadir descarga remota (simula el escenario del usuario)
        print("\n📥 Añadiendo descarga remota...")
        download_id = dm.add_remote_download(
            url="https://zonamovie.live:8443/series/test/test.mkv",
            filename="Los Caballeros del Zodiaco_ Omega (2012) S01 E01",
            remote_path="/media"
        )
        print(f"✅ Descarga remota añadida: {download_id}")
        
        # Esperar un momento para que se configure
        time.sleep(1)
        
        # Obtener estado exacto de las descargas
        downloads = dm.get_downloads_status()
        
        print(f"\n📊 ESTADO EXACTO DE LAS DESCARGAS:")
        print(f"   - Total descargas: {len(downloads)}")
        
        for i, download in enumerate(downloads):
            print(f"\n   📁 Descarga {i+1}:")
            print(f"      - filename: '{download['filename']}'")
            print(f"      - status: '{download['status']}'")
            print(f"      - is_remote: {download['is_remote']}")
            print(f"      - remote_log_file: {download['remote_log_file']}")
            print(f"      - remote_pid: {download['remote_pid']}")
            print(f"      - progress: {download['progress']:.1f}%")
        
        # Simular exactamente la lógica del monitor de main.py
        print(f"\n🔍 SIMULANDO LÓGICA DEL MONITOR DE MAIN.PY:")
        
        # Paso 1: Verificar descargas activas (lógica principal)
        has_active_downloads = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads)
        print(f"   1. has_active_downloads: {has_active_downloads}")
        
        if not has_active_downloads:
            print(f"   ❌ No hay descargas activas detectadas")
            
            # Paso 2: Verificar lógica de detección individual
            print(f"\n   🔍 Verificación individual:")
            for i, d in enumerate(downloads):
                status = d.get('status')
                in_active_states = status in ['Descargando', 'En cola']
                print(f"      - Descarga {i+1}: status='{status}' → activa={in_active_states}")
            
            # Paso 3: Simular la lógica de verificación adicional
            print(f"\n   🔍 Lógica de verificación adicional:")
            
            has_active = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads)
            print(f"      - has_active: {has_active}")
            
            has_remote_starting = any(
                d.get('is_remote') and d.get('status') == 'Descargando' and not d.get('remote_log_file')
                for d in downloads
            )
            print(f"      - has_remote_starting: {has_remote_starting}")
            
            # Verificar cada descarga para has_remote_starting
            print(f"\n      🔍 Verificación has_remote_starting individual:")
            for i, d in enumerate(downloads):
                is_remote = d.get('is_remote')
                status_downloading = d.get('status') == 'Descargando'
                no_log_file = not d.get('remote_log_file')
                meets_criteria = is_remote and status_downloading and no_log_file
                
                print(f"         - Descarga {i+1}:")
                print(f"           * is_remote: {is_remote}")
                print(f"           * status=='Descargando': {status_downloading}")
                print(f"           * not remote_log_file: {no_log_file}")
                print(f"           * meets_criteria: {meets_criteria}")
            
            should_continue = has_active or has_remote_starting
            print(f"\n      - should_continue: {should_continue}")
            
            if not should_continue:
                print(f"      ❌ Monitor terminaría aquí")
            else:
                print(f"      ✅ Monitor continuaría")
        else:
            print(f"   ✅ Hay descargas activas detectadas")
        
        # Verificar estado del sistema
        print(f"\n🧵 ESTADO DEL SISTEMA:")
        print(f"   - Sistema corriendo: {dm.is_running}")
        if dm.manager_thread:
            print(f"   - Manager thread activo: {dm.manager_thread.is_alive()}")
        if dm.remote_monitor_thread:
            print(f"   - Monitor remoto activo: {dm.remote_monitor_thread.is_alive()}")
        
        # Limpiar
        dm.stop_downloads()
        
        return has_active_downloads
        
    except Exception as e:
        print(f"❌ Error en diagnóstico: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_state_detection():
    """Prueba específica de detección de estados"""
    print("\n🔍 PRUEBA ESPECÍFICA DE DETECCIÓN DE ESTADOS")
    print("=" * 60)
    
    # Casos de prueba
    test_cases = [
        {
            "name": "Descarga remota en Error",
            "status": "Error",
            "is_remote": True,
            "remote_log_file": None
        },
        {
            "name": "Descarga remota Descargando sin log",
            "status": "Descargando", 
            "is_remote": True,
            "remote_log_file": None
        },
        {
            "name": "Descarga remota Descargando con log",
            "status": "Descargando",
            "is_remote": True, 
            "remote_log_file": "/tmp/test.log"
        },
        {
            "name": "Descarga local Descargando",
            "status": "Descargando",
            "is_remote": False,
            "remote_log_file": None
        }
    ]
    
    for case in test_cases:
        print(f"\n📋 {case['name']}:")
        
        # Lógica principal
        is_active = case['status'] in ['Descargando', 'En cola']
        print(f"   - is_active: {is_active}")
        
        # Lógica de remote_starting
        is_remote_starting = (
            case['is_remote'] and 
            case['status'] == 'Descargando' and 
            not case['remote_log_file']
        )
        print(f"   - is_remote_starting: {is_remote_starting}")
        
        # Resultado final
        would_continue = is_active or is_remote_starting
        print(f"   - would_continue: {would_continue}")
        
        if would_continue:
            print(f"   ✅ Monitor continuaría")
        else:
            print(f"   ❌ Monitor terminaría")

def main():
    """Función principal de diagnóstico"""
    print("🔍 DIAGNÓSTICO COMPLETO DEL TIMING DEL MONITOR")
    print("=" * 60)
    
    # Ejecutar diagnósticos
    result1 = debug_monitor_logic()
    test_state_detection()
    
    print(f"\n{'='*60}")
    print("📊 CONCLUSIONES DEL DIAGNÓSTICO")
    print(f"{'='*60}")
    
    print(f"🎯 El problema está en la lógica de detección del monitor de main.py")
    print(f"📝 Según los logs del usuario:")
    print(f"   - La descarga se añade correctamente")
    print(f"   - El comando wget se ejecuta")
    print(f"   - Pero el monitor termina inmediatamente")
    
    print(f"\n💡 Posibles causas:")
    print(f"   1. La descarga está en estado 'Error' en lugar de 'Descargando'")
    print(f"   2. La descarga tiene remote_log_file configurado")
    print(f"   3. El timing entre añadir descarga y verificar estado")
    
    print(f"\n🔧 Próximos pasos:")
    print(f"   1. Verificar por qué la descarga está en 'Error'")
    print(f"   2. Mejorar la detección para incluir errores recientes")
    print(f"   3. Añadir más logging para debug")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n💥 CRASH EN DIAGNÓSTICO: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
