#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnóstico Final y Definitivo con Logging Detallado
Genera un archivo paramiko.log con todos los detalles de la conexión.
"""

import paramiko
import logging
import time

# --- Configuración ---
HOSTNAME = "***************"
PORT = 22
USERNAME = "root"
PASSWORD = "5dhCkm5Dz1BpWgxZpdBisrOVo"
LOG_FILE = "paramiko.log"
CONNECTION_TIMEOUT = 15

def setup_logging():
    """Configura el logging de paramiko para ser muy verboso"""
    # Eliminar el archivo de log anterior si existe
    try:
        import os
        if os.path.exists(LOG_FILE):
            os.remove(LOG_FILE)
    except:
        pass
        
    logging.basicConfig(level=logging.DEBUG, 
                        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s',
                        filename=LOG_FILE,
                        filemode='w')

    # Silenciar otros loggers para enfocarnos en paramiko
    logging.getLogger("paramiko.transport").setLevel(logging.DEBUG)
    
    # Crear un handler para imprimir en consola también (opcional)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(levelname)s: %(message)s')
    console_handler.setFormatter(formatter)
    logging.getLogger('').addHandler(console_handler)


def test_connection_with_logging():
    """Intenta conectar usando la configuración más simple posible con logging"""
    
    print(f"\n🚀 INICIANDO DIAGNÓSTICO FINAL PARA {HOSTNAME}")
    print(f"📝 Se generará un log detallado en el archivo: {LOG_FILE}")
    print("="*50)
    
    client = None
    start_time = time.time()  # Inicializar start_time al principio
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        print(f"INFO: Intentando conectar a {USERNAME}@{HOSTNAME}:{PORT}...")
        connection_start_time = time.time()
        
        client.connect(
            hostname=HOSTNAME,
            port=PORT,
            username=USERNAME,
            password=PASSWORD,
            timeout=CONNECTION_TIMEOUT,
            allow_agent=False,
            look_for_keys=False,
            compress=True  # Probar con compresión activada, es más común
        )
        
        elapsed = time.time() - connection_start_time
        logging.info(f"¡CONEXIÓN EXITOSA en {elapsed:.2f}s!")
        print(f"INFO: ✅ ¡CONEXIÓN EXITOSA en {elapsed:.2f}s!")
        
        # Si conecta, hacer una prueba simple
        stdin, stdout, stderr = client.exec_command('ls -l /')
        print("INFO: Resultado de 'ls -l /':")
        print(stdout.read().decode())
        
        # Ahora probar una descarga real usando wget
        print("\n🌐 PROBANDO DESCARGA CON WGET...")
        test_download_with_wget(client)
        
    except Exception as e:
        elapsed = time.time() - start_time
        logging.error(f"CONEXIÓN FALLIDA después de {elapsed:.2f}s: {e}", exc_info=True)
        print(f"ERROR: ❌ CONEXIÓN FALLIDA: {e}")
        
    finally:
        if client:
            client.close()
            print("INFO: Conexión cerrada.")
            
    print("="*50)
    print(f"🏁 DIAGNÓSTICO COMPLETADO.")
    print(f"👉 Por favor, revisa el archivo '{LOG_FILE}' y envíame su contenido.")

def test_download_with_wget(client):
    """Prueba una descarga real usando wget en el servidor remoto"""
    try:
        print("="*50)
        print("🔽 INICIANDO PRUEBA DE DESCARGA REMOTA")
        
        # URL de prueba - un archivo pequeño para testear
        test_url = "https://www.google.com/favicon.ico"
        test_file = "/tmp/test_download_favicon.ico"
        log_file = "/tmp/wget_test.log"
        
        print(f"📥 URL de prueba: {test_url}")
        print(f"💾 Archivo destino: {test_file}")
        print(f"📝 Log: {log_file}")
        
        # Limpiar archivos anteriores si existen
        cleanup_cmd = f'rm -f {test_file} {log_file}'
        stdin, stdout, stderr = client.exec_command(cleanup_cmd)
        
        # Comando wget con timeout y en segundo plano
        wget_cmd = (
            f'nohup wget --continue --tries=0 --user-agent="XCIPTV" '
            f'--progress=dot:mega '
            f'--output-document="{test_file}" "{test_url}" > {log_file} 2>&1 &'
        )
        
        print(f"🚀 Ejecutando: {wget_cmd}")
        
        # Ejecutar wget
        start_time = time.time()
        stdin, stdout, stderr = client.exec_command(wget_cmd)
        
        # Esperar un poco para que wget inicie
        print("⏳ Esperando que wget inicie...")
        time.sleep(2)
        
        # Verificar si el proceso wget está corriendo
        check_process_cmd = f'ps aux | grep wget | grep "{test_url}" | grep -v grep'
        stdin, stdout, stderr = client.exec_command(check_process_cmd)
        process_info = stdout.read().decode('utf-8', errors='ignore')
        
        if process_info:
            print(f"✅ Proceso wget iniciado: {process_info.strip()}")
            
            # Extraer PID
            try:
                pid = process_info.split()[1]
                print(f"🆔 PID del proceso: {pid}")
            except (IndexError, ValueError):
                pid = None
                print("⚠️ No se pudo extraer el PID")
            
            # Esperar hasta 30 segundos para que termine la descarga
            print("⏳ Esperando que termine la descarga (máximo 30s)...")
            max_wait = 30
            waited = 0
            
            while waited < max_wait:
                time.sleep(2)
                waited += 2
                
                # Verificar si el proceso sigue corriendo
                if pid:
                    check_running_cmd = f'ps -p {pid} -o pid= | wc -l'
                    stdin, stdout, stderr = client.exec_command(check_running_cmd)
                    is_running = int(stdout.read().decode('utf-8', errors='ignore').strip()) > 0
                    
                    if not is_running:
                        print(f"✅ Proceso wget terminó después de {waited}s")
                        break
                    else:
                        print(f"🔄 Proceso aún corriendo... ({waited}s)")
                else:
                    # Sin PID, verificar si el archivo existe
                    check_file_cmd = f'[ -f "{test_file}" ] && echo "EXISTS" || echo "NOT_EXISTS"'
                    stdin, stdout, stderr = client.exec_command(check_file_cmd)
                    file_exists = stdout.read().decode('utf-8', errors='ignore').strip() == "EXISTS"
                    
                    if file_exists:
                        print(f"✅ Archivo descargado después de {waited}s")
                        break
            
            # Verificar resultado final
            print("\n📊 VERIFICANDO RESULTADO FINAL:")
            
            # 1. Verificar si el archivo existe
            check_file_cmd = f'[ -f "{test_file}" ] && echo "EXISTS" || echo "NOT_EXISTS"'
            stdin, stdout, stderr = client.exec_command(check_file_cmd)
            file_exists = stdout.read().decode('utf-8', errors='ignore').strip() == "EXISTS"
            
            if file_exists:
                # Obtener información del archivo
                file_info_cmd = f'ls -lh "{test_file}"'
                stdin, stdout, stderr = client.exec_command(file_info_cmd)
                file_info = stdout.read().decode('utf-8', errors='ignore').strip()
                print(f"✅ Archivo descargado exitosamente: {file_info}")
                
                # Verificar contenido del archivo (primeros bytes)
                head_cmd = f'hexdump -C "{test_file}" | head -3'
                stdin, stdout, stderr = client.exec_command(head_cmd)
                file_content = stdout.read().decode('utf-8', errors='ignore')
                if file_content:
                    print(f"📄 Contenido (hex): {file_content}")
            else:
                print("❌ El archivo NO fue descargado")
            
            # 2. Mostrar log de wget
            print("\n📝 LOG DE WGET:")
            log_cmd = f'cat {log_file}'
            stdin, stdout, stderr = client.exec_command(log_cmd)
            log_content = stdout.read().decode('utf-8', errors='ignore')
            if log_content:
                print(log_content)
            else:
                print("⚠️ No hay contenido en el log")
                
        else:
            print("❌ No se pudo iniciar el proceso wget")
            
            # Verificar errores inmediatos
            print("\n🔍 VERIFICANDO ERRORES INMEDIATOS:")
            error_check_cmd = f'cat {log_file} 2>/dev/null || echo "No log file"'
            stdin, stdout, stderr = client.exec_command(error_check_cmd)
            error_content = stdout.read().decode('utf-8', errors='ignore')
            print(f"Error/Log content: {error_content}")
        
        elapsed = time.time() - start_time
        print(f"\n⏱️ Tiempo total de prueba: {elapsed:.2f}s")
        
    except Exception as e:
        print(f"❌ ERROR en prueba de descarga: {e}")
        logging.error(f"Error en prueba de descarga: {e}", exc_info=True)

if __name__ == "__main__":
    setup_logging()
    test_connection_with_logging()
