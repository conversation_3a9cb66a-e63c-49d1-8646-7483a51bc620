#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de descarga con monitoreo de progreso en tiempo real
Simula lo que debería hacer la aplicación principal
"""

import paramiko
import time
import threading
from datetime import datetime

HOSTNAME = "***************"
PORT = 22
USERNAME = "root"
PASSWORD = "5dhCkm5Dz1BpWgxZpdBisrOVo"

class DownloadProgressMonitor:
    def __init__(self):
        self.client = None
        self.downloads = {}
        self.monitoring = False
        
    def connect(self):
        """Conectar al servidor"""
        print("🔌 Conectando al servidor...")
        self.client = paramiko.SSHClient()
        self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        self.client.connect(
            hostname=HOSTNAME,
            port=PORT,
            username=USERNAME,
            password=PASSWORD,
            timeout=15,
            allow_agent=False,
            look_for_keys=False,
            compress=True
        )
        print("✅ Conectado al servidor")
        
    def start_download(self, url, filename):
        """Iniciar una descarga remota"""
        download_id = f"dl_{int(time.time())}"
        remote_path = f"/tmp/{filename}"
        log_path = f"/tmp/wget_log_{download_id}.log"
        
        # Comando wget con progreso detallado
        wget_cmd = (
            f'nohup wget --progress=dot:giga --timeout=60 --tries=3 '
            f'--user-agent="XCIPTV" '
            f'--continue '  # Permitir continuar descargas interrumpidas
            f'--output-document="{remote_path}" "{url}" > {log_path} 2>&1 &'
        )
        
        print(f"\n🚀 Iniciando descarga: {filename}")
        print(f"📥 URL: {url}")
        print(f"💾 Destino: {remote_path}")
        print(f"📝 Log: {log_path}")
        
        # Limpiar archivos anteriores
        cleanup_cmd = f'rm -f {remote_path} {log_path}'
        stdin, stdout, stderr = self.client.exec_command(cleanup_cmd)
        
        # Ejecutar wget
        stdin, stdout, stderr = self.client.exec_command(wget_cmd)
        time.sleep(1)
        
        # Buscar PID del proceso
        check_process_cmd = f'ps aux | grep wget | grep "{url}" | grep -v grep'
        stdin, stdout, stderr = self.client.exec_command(check_process_cmd)
        process_info = stdout.read().decode('utf-8', errors='ignore').strip()
        
        pid = None
        if process_info:
            try:
                pid = process_info.split()[1]
                print(f"🆔 PID del proceso: {pid}")
            except (IndexError, ValueError):
                pass
        
        # Registrar descarga
        self.downloads[download_id] = {
            'filename': filename,
            'url': url,
            'remote_path': remote_path,
            'log_path': log_path,
            'pid': pid,
            'start_time': datetime.now(),
            'status': 'DOWNLOADING',
            'progress': 0,
            'size': '0 B',
            'speed': '0 B/s'
        }
        
        return download_id
        
    def check_download_progress(self, download_id):
        """Verificar progreso de una descarga específica"""
        if download_id not in self.downloads:
            return None
            
        download = self.downloads[download_id]
        
        try:
            # 1. Verificar si el proceso sigue corriendo
            is_running = False
            if download['pid']:
                check_running_cmd = f'ps -p {download["pid"]} -o pid= | wc -l'
                stdin, stdout, stderr = self.client.exec_command(check_running_cmd)
                is_running = int(stdout.read().decode('utf-8', errors='ignore').strip()) > 0
            
            # 2. Verificar tamaño del archivo
            check_size_cmd = f'[ -f "{download["remote_path"]}" ] && stat -c "%s" "{download["remote_path"]}" || echo "0"'
            stdin, stdout, stderr = self.client.exec_command(check_size_cmd)
            current_size_bytes = int(stdout.read().decode('utf-8', errors='ignore').strip())
            
            # 3. Analizar log para obtener progreso
            tail_log_cmd = f'tail -n 20 {download["log_path"]} 2>/dev/null || echo ""'
            stdin, stdout, stderr = self.client.exec_command(tail_log_cmd)
            log_content = stdout.read().decode('utf-8', errors='ignore')
            
            # Buscar información de progreso en el log
            total_size = None
            progress_percent = 0
            speed = "0 B/s"
            
            # Buscar líneas con información de tamaño total
            for line in log_content.split('\n'):
                if 'Length:' in line and '[' in line:
                    # Extraer tamaño total: "Length: 1048576 (1.0M) [application/octet-stream]"
                    try:
                        parts = line.split('Length:')[1].split('[')[0].strip()
                        total_size = int(parts.split('(')[0].strip())
                    except:
                        pass
                elif '%' in line and 'K' in line or 'M' in line:
                    # Extraer progreso: "     0K .......... .......... .......... .......... ..........  4%  123M 0s"
                    try:
                        percent_part = [p for p in line.split() if '%' in p]
                        if percent_part:
                            progress_percent = int(percent_part[0].replace('%', ''))
                        
                        # Extraer velocidad
                        speed_part = [p for p in line.split() if ('K' in p or 'M' in p or 'G' in p) and (not percent_part or p != percent_part[0])]
                        if speed_part:
                            speed = f"{speed_part[0]}/s"
                    except:
                        pass
            
            # Calcular progreso si no se pudo extraer del log
            if progress_percent == 0 and total_size and current_size_bytes > 0:
                progress_percent = min(100, int((current_size_bytes / total_size) * 100))
            
            # Formatear tamaño actual
            def format_size(bytes_val):
                for unit in ['B', 'KB', 'MB', 'GB']:
                    if bytes_val < 1024:
                        return f"{bytes_val:.1f} {unit}"
                    bytes_val /= 1024
                return f"{bytes_val:.1f} TB"
            
            # Actualizar información
            download['size'] = format_size(current_size_bytes)
            download['progress'] = progress_percent
            download['speed'] = speed
            
            # Determinar estado
            if not is_running and current_size_bytes > 0:
                # Verificar si la descarga se completó exitosamente
                if 'saved [' in log_content or progress_percent >= 100:
                    download['status'] = 'COMPLETED'
                    download['progress'] = 100
                else:
                    download['status'] = 'ERROR'
            elif not is_running and current_size_bytes == 0:
                download['status'] = 'ERROR'
            else:
                download['status'] = 'DOWNLOADING'
                
            return download
            
        except Exception as e:
            print(f"❌ Error verificando progreso: {e}")
            return download
    
    def monitor_downloads(self):
        """Monitor todas las descargas activas"""
        self.monitoring = True
        print("\n📊 INICIANDO MONITOREO DE DESCARGAS...")
        print("=" * 80)
        
        while self.monitoring and self.downloads:
            # Verificar cada descarga
            active_downloads = 0
            
            for download_id in list(self.downloads.keys()):
                download = self.check_download_progress(download_id)
                if download:
                    status_icon = {
                        'DOWNLOADING': '🔄',
                        'COMPLETED': '✅',
                        'ERROR': '❌'
                    }.get(download['status'], '❓')
                    
                    elapsed = datetime.now() - download['start_time']
                    elapsed_str = f"{int(elapsed.total_seconds())}s"
                    
                    print(f"{status_icon} {download['filename'][:30]:<30} | "
                          f"{download['progress']:>3}% | "
                          f"{download['size']:>8} | "
                          f"{download['speed']:>10} | "
                          f"{elapsed_str:>6}")
                    
                    if download['status'] == 'DOWNLOADING':
                        active_downloads += 1
                    elif download['status'] in ['COMPLETED', 'ERROR']:
                        # Remover descargas terminadas después de mostrarlas un rato
                        if elapsed.total_seconds() > 30:
                            del self.downloads[download_id]
            
            if active_downloads == 0 and len(self.downloads) == 0:
                print("\n🏁 Todas las descargas completadas")
                break
                
            print("-" * 80)
            time.sleep(3)  # Actualizar cada 3 segundos
        
        self.monitoring = False
    
    def start_monitoring_thread(self):
        """Iniciar monitoreo en hilo separado"""
        monitor_thread = threading.Thread(target=self.monitor_downloads, daemon=True)
        monitor_thread.start()
        return monitor_thread
    
    def disconnect(self):
        """Desconectar del servidor"""
        self.monitoring = False
        if self.client:
            self.client.close()
            print("🔌 Desconectado del servidor")

def main():
    monitor = DownloadProgressMonitor()
    
    try:
        # Conectar
        monitor.connect()
        
        # Iniciar algunas descargas de prueba
        download1 = monitor.start_download("https://httpbin.org/uuid", "test1.json")
        time.sleep(1)
        download2 = monitor.start_download("https://www.google.com/favicon.ico", "favicon.ico")
        
        # Iniciar monitoreo
        monitor_thread = monitor.start_monitoring_thread()
        
        # Esperar a que terminen las descargas
        monitor_thread.join()
        
    except KeyboardInterrupt:
        print("\n⏹️ Interrumpido por usuario")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        monitor.disconnect()

if __name__ == "__main__":
    main()
