#!/usr/bin/env python3
"""
Script de prueba simple para verificar el flujo básico de descargas.
"""

import sys
import os
import time

# Agregar el directorio actual al path para importar los módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from download_manager_requests import DownloadManager, DownloadStatus
    from sftp_manager import SFTPManager
    print("✅ Módulos importados correctamente")
except ImportError as e:
    print(f"❌ Error importando módulos: {e}")
    sys.exit(1)

def test_basic_flow():
    """Prueba el flujo básico sin mocks"""
    print("\n🔍 Probando flujo básico de descargas...")
    
    # Crear SFTP manager (sin conexión real)
    sftp = SFTPManager()
    
    # Crear download manager
    dm = DownloadManager(sftp_manager=sftp)
    
    print(f"📊 Estado inicial - is_running: {dm.is_running}")
    print(f"📊 Número de descargas: {len(dm.downloads)}")
    
    # Añadir una descarga local (no requiere conexión SFTP)
    print("\n➕ Añadiendo descarga local...")
    download_id = dm.add_download(
        url="https://httpbin.org/delay/1",  # URL que responde después de 1 segundo
        filename="test.json",
        remote_path="/test"
    )
    
    print(f"✅ Descarga añadida con ID: {download_id}")
    print(f"📊 Estado después de añadir - is_running: {dm.is_running}")
    
    # Esperar un momento para que se procese
    print("\n⏳ Esperando 3 segundos para ver el progreso...")
    time.sleep(3)
    
    # Verificar estado de las descargas
    downloads = dm.get_downloads_status()
    print(f"\n📋 Estado de descargas:")
    for i, download in enumerate(downloads):
        print(f"  {i+1}. ID: {download['id']}")
        print(f"     Archivo: {download['filename']}")
        print(f"     Estado: {download['status']}")
        print(f"     Progreso: {download['progress']:.1f}%")
        print(f"     URL: {download['url']}")
    
    # Limpiar
    print("\n🛑 Deteniendo sistema...")
    dm.stop_downloads()
    
    return True

def test_queue_status():
    """Prueba el estado de la cola"""
    print("\n🔍 Probando estado de la cola...")
    
    # Crear SFTP manager
    sftp = SFTPManager()
    
    # Crear download manager
    dm = DownloadManager(sftp_manager=sftp)
    
    # Añadir varias descargas
    urls = [
        "https://httpbin.org/delay/1",
        "https://httpbin.org/delay/2", 
        "https://httpbin.org/delay/3"
    ]
    
    download_ids = []
    for i, url in enumerate(urls):
        download_id = dm.add_download(
            url=url,
            filename=f"test{i}.json",
            remote_path="/test"
        )
        download_ids.append(download_id)
        print(f"✅ Añadida descarga {i+1}: {download_id}")
    
    print(f"\n📊 Total de descargas en cola: {len(dm.downloads)}")
    print(f"📊 Sistema corriendo: {dm.is_running}")
    
    # Mostrar estado inicial
    downloads = dm.get_downloads_status()
    print(f"\n📋 Estado inicial de {len(downloads)} descargas:")
    for download in downloads:
        print(f"  - {download['filename']}: {download['status']}")
    
    # Esperar un poco para ver cambios
    print("\n⏳ Esperando 5 segundos para ver progreso...")
    time.sleep(5)
    
    # Mostrar estado final
    downloads = dm.get_downloads_status()
    print(f"\n📋 Estado después de 5 segundos:")
    for download in downloads:
        print(f"  - {download['filename']}: {download['status']} ({download['progress']:.1f}%)")
    
    # Limpiar
    dm.stop_downloads()
    return True

def main():
    """Función principal"""
    print("🚀 Iniciando pruebas simples de descarga...")
    print("=" * 60)
    
    try:
        print("\n🧪 Prueba 1: Flujo básico")
        test_basic_flow()
        
        print("\n" + "="*60)
        print("\n🧪 Prueba 2: Estado de cola")
        test_queue_status()
        
        print("\n" + "="*60)
        print("🎉 ¡Pruebas completadas!")
        
    except Exception as e:
        print(f"\n❌ Error durante las pruebas: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
