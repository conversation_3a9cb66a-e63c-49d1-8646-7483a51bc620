#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test final de la aplicación con descarga remota y monitoreo de progreso
"""

import time
import threading
from sftp_manager import SFTPManager
from download_manager_requests import DownloadManager

def test_app_download_with_progress():
    print("🚀 TEST FINAL DE APLICACIÓN CON PROGRESO")
    print("=" * 60)
    
    # Crear instancias como en la app real
    sftp_manager = SFTPManager()
    download_manager = DownloadManager()
    
    try:
        # Conectar usando el método que funciona
        print("🔌 Conectando al servidor...")
        success = sftp_manager.connect_direct(
            hostname="***************",
            port=22,
            username="root",
            password="5dhCkm5Dz1BpWgxZpdBisrOVo",
            timeout=15
        )
        
        if not success:
            print("❌ No se pudo conectar")
            return
        
        # Iniciar sistema de descargas
        download_manager.start_downloads()
        
        # Agregar una descarga remota como en la app
        print("\n📥 Agregando descarga remota...")
        download_id = download_manager.add_remote_download(
            url="https://httpbin.org/uuid",
            filename="test_app_download.json",
            remote_path="/tmp",
            sftp_manager=sftp_manager
        )
        
        print(f"✅ Descarga agregada con ID: {download_id}")
        
        # Monitorear progreso por 30 segundos
        print("\n📊 MONITOREANDO PROGRESO DE LA APLICACIÓN:")
        print("Tiempo | Archivo | Estado | Progreso | Tamaño")
        print("-" * 60)
        
        start_time = time.time()
        last_status = ""
        
        while time.time() - start_time < 30:
            time.sleep(1)
            elapsed = int(time.time() - start_time)
            
            # Obtener estado actual
            downloads = download_manager.get_downloads_status()
            current_download = None
            
            for download in downloads:
                if download.get('id') == download_id:
                    current_download = download
                    break
            
            if current_download:
                status = current_download.get('status', 'UNKNOWN')
                progress = current_download.get('progress', 0)
                filename = current_download.get('filename', 'unknown')
                file_size = current_download.get('file_size', '0 B')
                is_remote = current_download.get('is_remote', False)
                
                status_icon = {
                    'DOWNLOADING': '🔄',
                    'COMPLETED': '✅',
                    'ERROR': '❌',
                    'QUEUED': '⏳'
                }.get(status, '❓')
                
                remote_indicator = "📡" if is_remote else "💻"
                
                current_status = f"{elapsed:>3}s | {remote_indicator} {filename[:15]:<15} | {status_icon} {status:<12} | {progress:>6.1f}% | {file_size}"
                
                # Solo imprimir si el estado cambió
                if current_status != last_status:
                    print(current_status)
                    last_status = current_status
                
                # Si se completó, mostrar detalles finales
                if status == 'COMPLETED':
                    print(f"\n🎉 DESCARGA COMPLETADA:")
                    print(f"   Archivo: {filename}")
                    print(f"   Tamaño: {file_size}")
                    print(f"   Tiempo: {elapsed}s")
                    
                    # Verificar en el servidor
                    if sftp_manager.is_connected():
                        remote_file = f"/tmp/{filename}"
                        check_cmd = f'[ -f "{remote_file}" ] && ls -lh "{remote_file}" || echo "Archivo no encontrado"'
                        stdin, stdout, stderr = sftp_manager.ssh_client.exec_command(check_cmd)
                        result = stdout.read().decode('utf-8', errors='ignore').strip()
                        print(f"   Verificación: {result}")
                    
                    break
                    
                elif status == 'ERROR':
                    error_msg = current_download.get('error_message', 'Error desconocido')
                    print(f"\n❌ ERROR EN DESCARGA:")
                    print(f"   Archivo: {filename}")
                    print(f"   Error: {error_msg}")
                    break
            else:
                print(f"{elapsed:>3}s | Descarga no encontrada")
        
        # Estadísticas finales
        print(f"\n📈 ESTADÍSTICAS FINALES:")
        stats = download_manager.get_statistics()
        for key, value in stats.items():
            print(f"   {key.title()}: {value}")
        
    except Exception as e:
        print(f"❌ Error en test: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Limpiar
        try:
            download_manager.stop_downloads()
            sftp_manager.disconnect()
            print("\n🔌 Recursos liberados")
        except:
            pass

if __name__ == "__main__":
    test_app_download_with_progress()
