#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para verificar que la aplicación funciona correctamente
"""

def test_extension_extraction():
    """Prueba la extracción de extensiones de URLs"""
    import urllib.parse
    import os
    
    test_urls = [
        "https://zonamovie.live:8443/series/Maujose2024/20240613507/699309.mkv",
        "https://example.com/video.mp4",
        "https://server.com/movie.avi?token=123",
        "https://stream.net/file.m4v",
        "https://cdn.com/video",  # Sin extensión
    ]
    
    for url in test_urls:
        parsed_url = urllib.parse.urlparse(url)
        url_path = parsed_url.path
        
        # Obtener extensión de la URL
        url_extension = ""
        if '.' in url_path:
            url_extension = os.path.splitext(url_path)[1]
            # Limpiar la extensión de parámetros adicionales
            if '?' in url_extension:
                url_extension = url_extension.split('?')[0]
        
        filename = "Test Movie"
        
        # Agregar extensión al filename si no la tiene y la URL la tiene
        if url_extension and not filename.endswith(url_extension):
            filename_with_ext = filename + url_extension
            print(f"✅ URL: {url}")
            print(f"   Extensión extraída: '{url_extension}'")
            print(f"   Archivo final: {filename_with_ext}")
        else:
            print(f"⚠️ URL: {url}")
            print(f"   Sin extensión o ya incluida: '{filename}'")
        print()

def test_wget_command():
    """Muestra ejemplo del comando wget que se generaría"""
    url = "https://zonamovie.live:8443/series/Maujose2024/20240613507/699309.mkv"
    filename = "Los Caballeros del Zodiaco - Saint Seiya 1986 S01 E114 699309"
    remote_path = "/home/<USER>/downloads"
    user_agent = "XCIPTV"
    
    # Simular la extracción de extensión
    import urllib.parse
    import os
    parsed_url = urllib.parse.urlparse(url)
    url_path = parsed_url.path
    url_extension = ""
    if '.' in url_path:
        url_extension = os.path.splitext(url_path)[1]
        if '?' in url_extension:
            url_extension = url_extension.split('?')[0]
    
    if url_extension and not filename.endswith(url_extension):
        filename_with_ext = filename + url_extension
    else:
        filename_with_ext = filename
    
    log_path = f"/tmp/wget_log_{filename_with_ext}.log"
    
    wget_cmd = (
        f'cd "{remote_path}" && '
        f'nohup timeout 600 wget --continue --tries=3 --user-agent="{user_agent}" '
        f'--progress=dot:mega --timeout=15 '
        f'--output-document="{filename_with_ext}" "{url}" > {log_path} 2>&1 &'
    )
    
    print("🔧 Comando wget que se ejecutaría:")
    print(wget_cmd)
    print()
    print(f"📁 Archivo final: {filename_with_ext}")

if __name__ == "__main__":
    print("=== PRUEBA DE EXTRACCIÓN DE EXTENSIONES ===")
    test_extension_extraction()
    
    print("=== PRUEBA DE COMANDO WGET ===")
    test_wget_command()
    
    print("✅ Todas las pruebas completadas")
