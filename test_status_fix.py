#!/usr/bin/env python3
"""
Script para probar que la corrección de estados funciona correctamente.
"""

import sys
import os
import time

# Agregar el directorio actual al path para importar los módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_status_detection():
    """Prueba la detección de estados corregida"""
    print("🔍 PRUEBA DE DETECCIÓN DE ESTADOS CORREGIDA")
    print("=" * 60)
    
    try:
        from download_manager_requests import DownloadManager, DownloadStatus
        from sftp_manager import SFTPManager
        
        # Crear managers
        print("📱 Creando managers...")
        sftp = SFTPManager()
        dm = DownloadManager(sftp_manager=sftp)
        
        # Añadir descarga remota
        print("\n📥 Añadiendo descarga remota...")
        download_id = dm.add_remote_download(
            url="https://httpbin.org/delay/2",
            filename="test_status_fix.json",
            remote_path="/tmp"
        )
        print(f"✅ Descarga remota añadida: {download_id}")
        
        # Verificar estado
        print("\n📊 Verificando estado de la descarga:")
        downloads = dm.get_downloads_status()
        
        for download in downloads:
            print(f"   - {download['filename']}:")
            print(f"     * Estado: '{download['status']}'")
            print(f"     * Es remota: {download['is_remote']}")
            print(f"     * Progreso: {download['progress']:.1f}%")
            print(f"     * PID remoto: {download['remote_pid']}")
            print(f"     * Log remoto: {download['remote_log_file']}")
        
        # Probar lógica de detección corregida
        print("\n🔍 Probando lógica de detección corregida:")
        
        # Lógica ANTIGUA (incorrecta)
        old_logic = any(d.get('status') in ['Downloading', 'Processing', 'Queued'] for d in downloads)
        print(f"   - Lógica ANTIGUA (inglés): {old_logic}")
        
        # Lógica NUEVA (corregida)
        new_logic = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads)
        print(f"   - Lógica NUEVA (español): {new_logic}")
        
        # Verificar que la nueva lógica funciona
        if new_logic and not old_logic:
            print("   ✅ ¡CORRECCIÓN EXITOSA! La nueva lógica detecta descargas activas")
        elif old_logic and new_logic:
            print("   ⚠️ Ambas lógicas funcionan (caso inesperado)")
        elif not new_logic and not old_logic:
            print("   ❌ Ninguna lógica detecta descargas activas")
        else:
            print("   ❓ Resultado inesperado")
        
        # Mostrar todos los estados posibles
        print(f"\n📋 Estados definidos en DownloadStatus:")
        for status in DownloadStatus:
            print(f"   - {status.name}: '{status.value}'")
        
        # Limpiar
        dm.stop_downloads()
        print("\n✅ Prueba completada")
        
        return new_logic  # Retornar True si la nueva lógica funciona
        
    except Exception as e:
        print(f"❌ Error en prueba: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_remote_download_flow():
    """Prueba el flujo completo de descarga remota"""
    print("\n🌐 PRUEBA DE FLUJO DE DESCARGA REMOTA")
    print("=" * 60)
    
    try:
        from download_manager_requests import DownloadManager, DownloadStatus
        from sftp_manager import SFTPManager
        
        # Crear managers
        sftp = SFTPManager()
        dm = DownloadManager(sftp_manager=sftp)
        
        # Añadir descarga remota
        download_id = dm.add_remote_download(
            url="https://httpbin.org/delay/1",
            filename="test_remote_flow.json",
            remote_path="/tmp"
        )
        
        print(f"✅ Descarga remota añadida: {download_id}")
        
        # Monitorear el flujo
        print("\n👁️ Monitoreando flujo de descarga...")
        
        for i in range(8):  # 4 segundos
            downloads = dm.get_downloads_status()
            
            if downloads:
                download = downloads[0]
                status = download['status']
                is_remote = download['is_remote']
                
                # Verificar detección de actividad
                is_active_new = status in ['Descargando', 'En cola']
                is_active_old = status in ['Downloading', 'Processing', 'Queued']
                
                print(f"   t={i*0.5}s: {status} | Remota: {is_remote} | Activa(nueva): {is_active_new} | Activa(vieja): {is_active_old}")
                
                if status in ['Completado', 'Error']:
                    print(f"   ✅ Descarga terminó: {status}")
                    break
            
            time.sleep(0.5)
        
        # Verificar criterios del monitor remoto
        print(f"\n👁️ Verificando criterios del monitor remoto:")
        downloads = dm.get_downloads_status()
        
        for download in downloads:
            print(f"   - {download['filename']}:")
            
            # Criterios del monitor remoto
            is_remote = download['is_remote']
            status_downloading = download['status'] == 'Descargando'
            has_log_file = download['remote_log_file'] is not None
            
            print(f"     * is_remote: {is_remote} {'✅' if is_remote else '❌'}")
            print(f"     * status == 'Descargando': {status_downloading} {'✅' if status_downloading else '❌'}")
            print(f"     * tiene remote_log_file: {has_log_file} {'✅' if has_log_file else '❌'}")
            
            meets_criteria = is_remote and status_downloading and has_log_file
            print(f"     * Cumple criterios para monitoreo: {meets_criteria} {'✅' if meets_criteria else '❌'}")
        
        dm.stop_downloads()
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba de flujo: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_app_logic():
    """Prueba la lógica de la aplicación principal"""
    print("\n📱 PRUEBA DE LÓGICA DE APLICACIÓN PRINCIPAL")
    print("=" * 60)
    
    try:
        from download_manager_requests import DownloadManager, DownloadStatus
        from sftp_manager import SFTPManager
        
        # Crear managers
        sftp = SFTPManager()
        dm = DownloadManager(sftp_manager=sftp)
        
        # Añadir descarga remota
        download_id = dm.add_remote_download(
            url="https://httpbin.org/delay/1",
            filename="test_main_logic.json",
            remote_path="/tmp"
        )
        
        # Simular la lógica de main.py
        downloads = dm.get_downloads_status()
        
        print(f"📊 Simulando lógica de main.py:")
        
        # Lógica del hilo de monitoreo (línea 1393)
        has_active_downloads = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads)
        print(f"   - has_active_downloads (línea 1393): {has_active_downloads}")
        
        # Lógica de estadísticas (líneas 1153-1156)
        queued = sum(1 for d in downloads if d.get('status') == 'En cola')
        active = sum(1 for d in downloads if d.get('status') in ['Descargando'])
        completed = sum(1 for d in downloads if d.get('status') == 'Completado')
        error = sum(1 for d in downloads if d.get('status') == 'Error')
        
        print(f"   - Estadísticas: En cola: {queued} | Activas: {active} | Completadas: {completed} | Errores: {error}")
        
        # Lógica de verificación de inactividad (línea 1415)
        inactive_check = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads)
        print(f"   - inactive_check (línea 1415): {inactive_check}")
        
        # Lógica de reinicio de hilo (línea 1436)
        restart_check = any(d.get('status') in ['Descargando'] for d in downloads)
        print(f"   - restart_check (línea 1436): {restart_check}")
        
        # Verificar consistencia
        all_checks = [has_active_downloads, active > 0, inactive_check, restart_check]
        consistent = all(all_checks) or not any(all_checks)
        
        print(f"\n🔍 Verificación de consistencia:")
        print(f"   - Todas las verificaciones: {all_checks}")
        print(f"   - Consistente: {consistent} {'✅' if consistent else '❌'}")
        
        if has_active_downloads:
            print("   ✅ El hilo de monitoreo DEBERÍA iniciarse")
        else:
            print("   ❌ El hilo de monitoreo NO se iniciará")
        
        dm.stop_downloads()
        return has_active_downloads
        
    except Exception as e:
        print(f"❌ Error en prueba de lógica principal: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Función principal de pruebas"""
    tests = [
        ("Detección de estados", test_status_detection),
        ("Flujo de descarga remota", test_remote_download_flow),
        ("Lógica de aplicación principal", test_main_app_logic),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: ÉXITO")
            else:
                print(f"❌ {test_name}: FALLÓ")
                
        except Exception as e:
            print(f"💥 {test_name}: CRASH - {e}")
            results.append((test_name, False))
    
    # Resumen
    print(f"\n{'='*60}")
    print("📊 RESUMEN DE CORRECCIÓN")
    print(f"{'='*60}")
    
    for test_name, result in results:
        status = "✅ ÉXITO" if result else "❌ FALLÓ"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 Total: {passed}/{total} pruebas exitosas")
    
    if passed == total:
        print("🎉 ¡CORRECCIÓN EXITOSA!")
        print("✅ El monitoreo de descargas debería funcionar ahora")
        print("💡 Las descargas remotas deberían detectarse como activas")
    else:
        print("⚠️ La corrección necesita más ajustes")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 CRASH EN SCRIPT DE PRUEBAS: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
