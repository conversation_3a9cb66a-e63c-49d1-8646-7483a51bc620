#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script simple para probar el tema visual
"""

import tkinter as tk
from tkinter import ttk
from dark_theme import apply_dark_theme

def test_theme():
    """Prueba el tema visual"""
    root = tk.Tk()
    root.title("Prueba Tema NVIDIA")
    root.geometry("600x400")
    
    # Aplicar tema
    theme_manager = apply_dark_theme(root)
    
    # Crear algunos widgets de prueba
    main_frame = ttk.Frame(root)
    main_frame.pack(fill='both', expand=True, padx=10, pady=10)
    
    # Título
    title_label = ttk.Label(main_frame, text="Tema Oscuro NVIDIA", style="Title.TLabel")
    title_label.pack(pady=10)
    
    # Botones de prueba
    btn_frame = ttk.Frame(main_frame)
    btn_frame.pack(fill='x', pady=10)
    
    normal_btn = ttk.But<PERSON>(btn_frame, text="Botón Normal")
    normal_btn.pack(side='left', padx=5)
    
    accent_btn = ttk.Button(btn_frame, text="Botón Acento", style="Accent.TButton")
    accent_btn.pack(side='left', padx=5)
    
    # Entry de prueba
    entry_frame = ttk.Frame(main_frame)
    entry_frame.pack(fill='x', pady=10)
    
    ttk.Label(entry_frame, text="Texto:").pack(side='left')
    test_entry = ttk.Entry(entry_frame, width=30)
    test_entry.pack(side='left', padx=5)
    test_entry.insert(0, "Texto de prueba")
    
    # Treeview de prueba
    tree_frame = ttk.LabelFrame(main_frame, text="Lista de Prueba")
    tree_frame.pack(fill='both', expand=True, pady=10)
    
    columns = ('Columna1', 'Columna2', 'Columna3')
    tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=6)
    
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=150)
    
    # Datos de prueba
    for i in range(5):
        tree.insert('', 'end', values=(f'Dato {i+1}', f'Valor {i+1}', f'Info {i+1}'))
    
    tree.pack(fill='both', expand=True, padx=5, pady=5)
    
    # Barra de progreso
    progress_frame = ttk.Frame(main_frame)
    progress_frame.pack(fill='x', pady=10)
    
    ttk.Label(progress_frame, text="Progreso:").pack(side='left')
    progress = ttk.Progressbar(progress_frame, mode='determinate', value=65)
    progress.pack(side='left', fill='x', expand=True, padx=5)
    ttk.Label(progress_frame, text="65%").pack(side='left')
    
    print("✅ Tema aplicado - Ventana de prueba creada")
    root.mainloop()

if __name__ == "__main__":
    test_theme()
