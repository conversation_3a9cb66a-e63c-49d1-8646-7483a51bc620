#!/usr/bin/env python3
"""
Test específico para verificar que el comando wget se genera correctamente
"""

import sys
import os
import shlex

# Añadir el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_wget_command_generation():
    """Test de generación del comando wget"""
    print("🧪 TEST DE GENERACIÓN DEL COMANDO WGET")
    print("=" * 60)
    
    # Simular los parámetros problemáticos
    filename = "Los Caballeros del Zodiaco: Omega (2012) S01 E01"
    url = "https://zonamovie.live:8443/series/Maujose2024/20240613507/883259.mkv"
    remote_path = "/media"
    
    # Agregar extensión
    filename_with_ext = filename + ".mkv"
    
    # Escapar caracteres especiales para bash
    safe_filename = shlex.quote(filename_with_ext)
    safe_remote_path = shlex.quote(remote_path)
    safe_url = shlex.quote(url)
    
    # Generar log path
    import time
    log_timestamp = int(time.time())
    log_filename = f"wget_log_{log_timestamp}_{filename_with_ext}.log"
    log_path = f"/tmp/{log_filename}"
    safe_log_path = shlex.quote(log_path)
    
    print(f"📁 Filename original: {filename}")
    print(f"📁 Filename con ext: {filename_with_ext}")
    print(f"🔒 Filename escapado: {safe_filename}")
    print(f"🔒 Remote path escapado: {safe_remote_path}")
    print(f"🔒 URL escapada: {safe_url}")
    print(f"🔒 Log path escapado: {safe_log_path}")
    print()
    
    # Generar comando wget (versión corregida)
    user_agent = "XCIPTV"
    wget_cmd = (
        f'cd {safe_remote_path} && '
        f'nohup timeout 300 wget --continue --tries=0 --user-agent="{user_agent}" '
        f'--progress=dot:mega --timeout=5 '
        f'--output-document={safe_filename} {safe_url} > {safe_log_path} 2>&1 &'
    )
    
    print("✅ COMANDO WGET GENERADO:")
    print("-" * 40)
    print(wget_cmd)
    print()
    
    # Verificar que no hay caracteres problemáticos sin escapar
    print("🔍 VERIFICACIÓN DE SEGURIDAD:")
    print("-" * 30)
    
    # Buscar paréntesis sin escapar
    import re
    unescaped_parens = re.findall(r'[^\']\([^\']*\)[^\']*', wget_cmd)
    if unescaped_parens:
        print(f"❌ Paréntesis sin escapar encontrados: {unescaped_parens}")
    else:
        print("✅ No se encontraron paréntesis sin escapar")
    
    # Verificar que los nombres de archivo están entre comillas simples
    if f"'{filename_with_ext}'" in wget_cmd:
        print("✅ Filename está correctamente escapado con comillas simples")
    else:
        print("❌ Filename NO está escapado correctamente")
    
    # Verificar que no hay comillas dobles alrededor del filename
    if f'"{filename_with_ext}"' in wget_cmd:
        print("❌ PROBLEMA: Filename aún tiene comillas dobles")
    else:
        print("✅ Filename no tiene comillas dobles problemáticas")
    
    print()
    print("🧪 SIMULANDO EJECUCIÓN DEL COMANDO:")
    print("-" * 40)
    
    # Simular lo que bash vería
    print("Bash interpretaría:")
    parts = wget_cmd.split(' && ')
    for i, part in enumerate(parts, 1):
        print(f"  {i}. {part}")
    
    print()
    print("✅ TEST COMPLETADO")
    
    return wget_cmd

def test_sftp_manager_method():
    """Test del método real de SFTPManager"""
    print("\n🔧 TEST DEL MÉTODO REAL DE SFTPMANAGER")
    print("=" * 60)
    
    try:
        from sftp_manager import SFTPManager
        
        # Crear instancia (sin conectar)
        sftp = SFTPManager()
        
        # Simular parámetros
        url = "https://zonamovie.live:8443/series/Maujose2024/20240613507/883259.mkv"
        remote_path = "/media"
        filename = "Los Caballeros del Zodiaco: Omega (2012) S01 E01"
        
        print(f"📥 URL: {url}")
        print(f"📁 Remote path: {remote_path}")
        print(f"📄 Filename: {filename}")
        print()
        
        # Simular la lógica del método remote_download (sin ejecutar)
        import urllib.parse
        parsed_url = urllib.parse.urlparse(url)
        url_path = parsed_url.path
        
        # Obtener extensión de la URL
        url_extension = ""
        if '.' in url_path:
            url_extension = os.path.splitext(url_path)[1]
            if '?' in url_extension:
                url_extension = url_extension.split('?')[0]
        
        # Agregar extensión al filename
        if url_extension and not filename.endswith(url_extension):
            filename_with_ext = filename + url_extension
            print(f"📄 Agregando extensión '{url_extension}': {filename} -> {filename_with_ext}")
        else:
            filename_with_ext = filename
        
        # Escapar caracteres especiales
        import shlex
        safe_filename = shlex.quote(filename_with_ext)
        safe_remote_path = shlex.quote(remote_path)
        safe_url = shlex.quote(url)
        
        print(f"🔒 Filename escapado: {filename_with_ext} -> {safe_filename}")
        
        # Generar log path
        import time
        log_timestamp = int(time.time())
        log_filename = f"wget_log_{log_timestamp}_{filename_with_ext}.log"
        log_path = f"/tmp/{log_filename}"
        safe_log_path = shlex.quote(log_path)
        
        # Generar comando
        user_agent = "XCIPTV"
        wget_cmd = (
            f'cd {safe_remote_path} && '
            f'nohup timeout 300 wget --continue --tries=0 --user-agent="{user_agent}" '
            f'--progress=dot:mega --timeout=5 '
            f'--output-document={safe_filename} {safe_url} > {safe_log_path} 2>&1 &'
        )
        
        print("\n✅ COMANDO GENERADO POR SFTPMANAGER:")
        print("-" * 50)
        print(wget_cmd)
        
        print("\n✅ TEST DEL MÉTODO REAL COMPLETADO")
        
    except Exception as e:
        print(f"❌ Error en test del método real: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Test de generación de comando
    cmd = test_wget_command_generation()
    
    # Test del método real
    test_sftp_manager_method()
    
    print("\n" + "=" * 60)
    print("🎯 RESUMEN:")
    print("✅ El comando wget debería generarse correctamente")
    print("✅ Los caracteres especiales deberían estar escapados")
    print("✅ No debería haber errores de sintaxis en bash")
    print("=" * 60)
