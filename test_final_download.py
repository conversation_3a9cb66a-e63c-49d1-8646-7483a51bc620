#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Prueba final con URL que sí funciona
"""

import paramiko
import time

HOSTNAME = "***************"
PORT = 22
USERNAME = "root"
PASSWORD = "5dhCkm5Dz1BpWgxZpdBisrOVo"

def test_working_download():
    print("🎯 PRUEBA FINAL CON URL CONFIABLE...")
    
    client = None
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        client.connect(
            hostname=HOSTNAME,
            port=PORT,
            username=USERNAME,
            password=PASSWORD,
            timeout=15,
            allow_agent=False,
            look_for_keys=False,
            compress=True
        )
        
        print("✅ Conectado al servidor")
        
        # URL que sabemos que funciona (texto pequeño)
        test_url = "https://httpbin.org/uuid"
        test_file = "/tmp/test_final_download.json"
        log_file = "/tmp/wget_final_test.log"
        
        print(f"📥 URL: {test_url}")
        print(f"💾 Destino: {test_file}")
        
        # Limpiar archivos anteriores
        cleanup_cmd = f'rm -f {test_file} {log_file}'
        stdin, stdout, stderr = client.exec_command(cleanup_cmd)
        
        # Comando wget
        wget_cmd = (
            f'wget --timeout=30 --tries=3 '
            f'--user-agent="XCIPTV" '
            f'--output-document="{test_file}" "{test_url}" > {log_file} 2>&1'
        )
        
        print(f"🚀 Ejecutando wget (síncrono)...")
        start_time = time.time()
        
        # Ejecutar comando síncrono para ver resultado inmediato
        stdin, stdout, stderr = client.exec_command(wget_cmd)
        
        # Esperar a que termine
        exit_status = stdout.channel.recv_exit_status()
        elapsed = time.time() - start_time
        
        print(f"⏱️ Wget terminó en {elapsed:.2f}s con código: {exit_status}")
        
        # Verificar resultado
        if exit_status == 0:
            print("✅ Wget ejecutado exitosamente")
            
            # Verificar archivo
            check_cmd = f'[ -f "{test_file}" ] && ls -lh "{test_file}" && cat "{test_file}"'
            stdin, stdout, stderr = client.exec_command(check_cmd)
            result = stdout.read().decode('utf-8', errors='ignore')
            print(f"📄 Resultado:\n{result}")
        else:
            print(f"❌ Wget falló con código {exit_status}")
        
        # Mostrar log
        print("\n📝 LOG:")
        log_cmd = f'cat {log_file}'
        stdin, stdout, stderr = client.exec_command(log_cmd)
        log_content = stdout.read().decode('utf-8', errors='ignore')
        print(log_content)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    test_working_download()
