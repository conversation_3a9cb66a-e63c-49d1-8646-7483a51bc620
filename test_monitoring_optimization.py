#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test optimizado del sistema de monitoreo de descargas remotas
Verifica que el nuevo algoritmo con timeouts funcione correctamente.
"""

import sys
import os
import time
import threading
from datetime import datetime, timedelta

# Agregar el directorio actual al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from download_manager_requests import DownloadManager, DownloadStatus
from sftp_manager import SFTPManager

def test_monitoring_with_startup_delay():
    """Prueba que el monitor espere correctamente durante el inicio de wget"""
    print("🧪 PRUEBA: Monitoreo con retraso de inicio")
    print("=" * 60)
    
    # Configurar conexión SFTP
    sftp_manager = SFTPManager()
    config = {
        'hostname': '**************',
        'port': 22,
        'username': 'root',
        'password': 'Montes2019'
    }
    
    if not sftp_manager.connect(**config):
        print("❌ Error conectando por SFTP")
        return False
    
    print("✅ Conectado por SFTP")
    
    # Configurar download manager
    dm = DownloadManager()
    dm.set_sftp_manager(sftp_manager)
    dm.start_downloads()
    
    # Monitor de eventos para seguimiento
    events = []
    
    def track_status_change(download_id):
        with dm.downloads_lock:
            item = dm.downloads.get(download_id)
            if item:
                events.append({
                    'time': time.time(),
                    'type': 'status_change',
                    'status': item.status.value,
                    'progress': item.progress,
                    'message': getattr(item, 'error_message', '')
                })
                print(f"📊 Status: {item.status.value} ({item.progress:.1f}%)")
                if item.error_message:
                    print(f"   ⚠️ Error: {item.error_message}")
    
    def track_progress_change(download_id):
        with dm.downloads_lock:
            item = dm.downloads.get(download_id)
            if item:
                events.append({
                    'time': time.time(),
                    'type': 'progress_change',
                    'status': item.status.value,
                    'progress': item.progress
                })
    
    dm.add_status_callback(track_status_change)
    dm.add_progress_callback(track_progress_change)
    
    # Iniciar descarga de prueba
    test_url = "https://ia801504.us.archive.org/11/items/SampleVideo1280x7205mb/SampleVideo_1280x720_5mb.mp4"
    filename = f"test_monitoring_opt_{int(time.time())}.mp4"
    remote_path = "/root"
    
    print(f"\n🚀 Iniciando descarga de prueba...")
    print(f"   📄 Archivo: {filename}")
    print(f"   🌐 URL: {test_url}")
    print(f"   📂 Destino: {remote_path}")
    
    start_time = time.time()
    download_id = dm.add_remote_download(test_url, filename, remote_path)
    
    # Monitorear durante los primeros 60 segundos
    print(f"\n👁️ Monitoreando descarga por 60 segundos...")
    monitor_duration = 60.0
    
    while time.time() - start_time < monitor_duration:
        with dm.downloads_lock:
            item = dm.downloads.get(download_id)
            if not item:
                break
                
            # Mostrar estado cada 5 segundos
            elapsed = time.time() - start_time
            if int(elapsed) % 5 == 0 and (elapsed - int(elapsed)) < 0.1:
                print(f"⏱️ {elapsed:.1f}s - {item.status.value} ({item.progress:.1f}%)")
                
                # Mostrar información adicional de debugging
                if hasattr(item, 'download_start_time') and item.download_start_time:
                    download_elapsed = time.time() - item.download_start_time
                    print(f"   📊 Tiempo desde inicio: {download_elapsed:.1f}s")
                    
                if hasattr(item, 'remote_pid') and item.remote_pid:
                    print(f"   🔧 PID remoto: {item.remote_pid}")
                    
                if hasattr(item, 'remote_log_file') and item.remote_log_file:
                    print(f"   📄 Log file: {item.remote_log_file}")
            
            # Salir si la descarga terminó
            if item.status in [DownloadStatus.COMPLETED, DownloadStatus.ERROR]:
                print(f"🏁 Descarga terminada: {item.status.value}")
                break
        
        time.sleep(0.1)
    
    # Obtener estado final
    final_item = dm.downloads.get(download_id)
    total_time = time.time() - start_time
    
    print(f"\n📋 RESULTADOS:")
    print(f"   ⏱️ Tiempo total: {total_time:.1f}s")
    
    if final_item:
        print(f"   📊 Estado final: {final_item.status.value}")
        print(f"   📈 Progreso final: {final_item.progress:.1f}%")
        
        if final_item.error_message:
            print(f"   ❌ Error: {final_item.error_message}")
    else:
        print(f"   ❌ Item de descarga no encontrado")
        return False
    
    # Analizar eventos
    print(f"\n📊 ANÁLISIS DE EVENTOS:")
    status_changes = [e for e in events if e['type'] == 'status_change']
    progress_changes = [e for e in events if e['type'] == 'progress_change']
    
    print(f"   📈 Cambios de estado: {len(status_changes)}")
    print(f"   📊 Cambios de progreso: {len(progress_changes)}")
    
    # Mostrar timeline de eventos importantes
    for event in status_changes:
        elapsed = event['time'] - start_time
        print(f"   🕐 {elapsed:6.1f}s: {event['status']}")
        if event['message']:
            print(f"       💬 {event['message']}")
    
    # Verificar que no haya errores prematuros de timeout
    premature_errors = [e for e in status_changes 
                       if e['status'] == 'Error' and (e['time'] - start_time) < 30]
    
    if premature_errors:
        print(f"   ⚠️ ADVERTENCIA: {len(premature_errors)} errores prematuros (< 30s)")
        for error in premature_errors:
            elapsed = error['time'] - start_time
            print(f"       🕐 {elapsed:.1f}s: {error['message']}")
    else:
        print(f"   ✅ Sin errores prematuros de timeout")
    
    # Limpiar
    dm.stop_downloads()
    sftp_manager.disconnect()
    
    # Determinar éxito
    success = (
        final_item is not None and
        final_item.status in [DownloadStatus.COMPLETED, DownloadStatus.DOWNLOADING] and
        len(premature_errors) == 0
    )
    
    print(f"\n🎯 RESULTADO: {'✅ ÉXITO' if success else '❌ FALLO'}")
    return success

def test_timeout_handling():
    """Prueba que los timeouts funcionen correctamente"""
    print("\n" + "="*60)
    print("🧪 PRUEBA: Manejo de timeouts")
    print("=" * 60)
    
    # Simular descarga que nunca inicia (URL inexistente)
    sftp_manager = SFTPManager()
    config = {
        'hostname': '**************',
        'port': 22,
        'username': 'root',
        'password': 'Montes2019'
    }
    
    if not sftp_manager.connect(**config):
        print("❌ Error conectando por SFTP")
        return False
    
    dm = DownloadManager()
    dm.set_sftp_manager(sftp_manager)
    dm.start_downloads()
    
    # URL que causará timeout
    bad_url = "http://192.168.999.999/nonexistent.mp4"  # IP inexistente
    filename = f"test_timeout_{int(time.time())}.mp4"
    remote_path = "/root"
    
    print(f"🚀 Iniciando descarga con timeout...")
    print(f"   🌐 URL problemática: {bad_url}")
    
    start_time = time.time()
    download_id = dm.add_remote_download(bad_url, filename, remote_path)
    
    # Monitorear por 45 segundos (debe timeout a los 30s)
    timeout_detected = False
    while time.time() - start_time < 45:
        with dm.downloads_lock:
            item = dm.downloads.get(download_id)
            if not item:
                break
                
            if item.status == DownloadStatus.ERROR:
                if "timeout" in item.error_message.lower():
                    timeout_detected = True
                    elapsed = time.time() - start_time
                    print(f"✅ Timeout detectado después de {elapsed:.1f}s")
                    print(f"   💬 Mensaje: {item.error_message}")
                break
        
        time.sleep(1)
    
    dm.stop_downloads()
    sftp_manager.disconnect()
    
    success = timeout_detected
    print(f"\n🎯 RESULTADO: {'✅ ÉXITO' if success else '❌ FALLO'}")
    return success

if __name__ == "__main__":
    print("🚀 INICIANDO PRUEBAS DE OPTIMIZACIÓN DE MONITOREO")
    print("=" * 80)
    
    try:
        # Ejecutar pruebas
        test1_success = test_monitoring_with_startup_delay()
        test2_success = test_timeout_handling()
        
        # Resultado final
        print("\n" + "=" * 80)
        print("📊 RESUMEN FINAL:")
        print(f"   🧪 Prueba de monitoreo con retraso: {'✅' if test1_success else '❌'}")
        print(f"   🧪 Prueba de manejo de timeouts: {'✅' if test2_success else '❌'}")
        
        overall_success = test1_success and test2_success
        print(f"\n🎯 RESULTADO GENERAL: {'✅ TODAS LAS PRUEBAS EXITOSAS' if overall_success else '❌ ALGUNAS PRUEBAS FALLARON'}")
        
        if overall_success:
            print("\n🎉 El sistema de monitoreo optimizado funciona correctamente!")
            print("   ✅ Espera correctamente durante el inicio de wget")
            print("   ✅ Detecta timeouts apropiadamente")
            print("   ✅ No genera errores prematuros")
        else:
            print("\n⚠️ Se detectaron problemas en el sistema de monitoreo.")
            print("   Revisar logs para más detalles.")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Prueba interrumpida por el usuario")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        import traceback
        traceback.print_exc()
