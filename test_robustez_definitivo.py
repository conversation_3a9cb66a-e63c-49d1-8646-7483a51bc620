#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test definitivo de robustez SFTP - Verifica que no hay timeouts ni cuelgues
"""

import sys
import time
import threading
from sftp_manager import SFTPManager

def test_timeout_robustness():
    """Test de robustez contra timeouts"""
    print("🧪 PRUEBA DE ROBUSTEZ ANTI-TIMEOUT")
    print("=" * 50)
    
    # Test 1: Servidor inválido (debería fallar rápido)
    print("\n1️⃣ Test con servidor inválido (debería timeout rápido)...")
    
    sftp = SFTPManager()
    
    start_time = time.time()
    try:
        # Intentar conectar a un servidor que no existe
        result = sftp.connect("192.168.99.99", 22, "fake_user", "fake_pass", timeout=5)
        print(f"❌ Error: No debería conectar a servidor inválido")
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"✅ Falló como esperado en {elapsed:.2f}s: {e}")
        if elapsed > 12:
            print(f"⚠️  Timeout demoró más de lo esperado: {elapsed:.2f}s")
        else:
            print(f"✅ Timeout correcto: {elapsed:.2f}s")
    finally:
        sftp.disconnect()
    
    # Test 2: Puerto cerrado (debería fallar muy rápido)
    print("\n2️⃣ Test con puerto cerrado...")
    
    sftp = SFTPManager()
    start_time = time.time()
    try:
        # Intentar conectar a un puerto cerrado en localhost
        result = sftp.connect("127.0.0.1", 9999, "fake_user", "fake_pass", timeout=5)
        print(f"❌ Error: No debería conectar a puerto cerrado")
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"✅ Falló como esperado en {elapsed:.2f}s: {e}")
        if elapsed > 10:
            print(f"⚠️  Timeout demoró más de lo esperado: {elapsed:.2f}s")
        else:
            print(f"✅ Rechazo rápido: {elapsed:.2f}s")
    finally:
        sftp.disconnect()
    
    # Test 3: Servidor demo válido (debería conectar)
    print("\n3️⃣ Test con servidor demo válido...")
    
    sftp = SFTPManager()
    start_time = time.time()
    try:
        # Conectar al servidor demo
        result = sftp.connect("test.rebex.net", 22, "demo", "password", timeout=8)
        elapsed = time.time() - start_time
        
        if result:
            print(f"✅ Conexión exitosa en {elapsed:.2f}s")
            
            # Test de operación básica
            try:
                files = sftp.list_directory("/")
                print(f"✅ Listado exitoso: {len(files)} archivos")
            except Exception as e:
                print(f"⚠️  Error en listado: {e}")
                
        else:
            print(f"❌ Falló la conexión en {elapsed:.2f}s")
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ Error conectando en {elapsed:.2f}s: {e}")
    finally:
        sftp.disconnect()
    
    print("\n" + "=" * 50)
    print("🏁 PRUEBAS COMPLETADAS")
    
def test_multiple_connections():
    """Test de múltiples conexiones rápidas"""
    print("\n🔄 Test de conexiones múltiples...")
    
    for i in range(3):
        print(f"\nConexión {i+1}/3...")
        sftp = SFTPManager()
        
        start_time = time.time()
        try:
            result = sftp.connect("test.rebex.net", 22, "demo", "password", timeout=6)
            elapsed = time.time() - start_time
            
            if result:
                print(f"✅ Conexión {i+1} exitosa en {elapsed:.2f}s")
            else:
                print(f"❌ Conexión {i+1} falló en {elapsed:.2f}s")
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"❌ Error conexión {i+1} en {elapsed:.2f}s: {e}")
        finally:
            sftp.disconnect()
            time.sleep(0.5)  # Pausa entre conexiones

def test_thread_safety():
    """Test de seguridad en threads"""
    print("\n🧵 Test de seguridad en threads...")
    
    def connect_worker(worker_id):
        print(f"Worker {worker_id} iniciando...")
        sftp = SFTPManager()
        start_time = time.time()  # Mover aquí para evitar unbound
        
        try:
            if worker_id % 2 == 0:
                # Mitad conecta a servidor válido
                result = sftp.connect("test.rebex.net", 22, "demo", "password", timeout=5)
                server = "test.rebex.net"
            else:
                # Mitad conecta a servidor inválido
                result = sftp.connect("192.168.99.99", 22, "fake", "fake", timeout=5)
                server = "192.168.99.99"
            
            elapsed = time.time() - start_time
            
            if result:
                print(f"Worker {worker_id}: ✅ Conectado a {server} en {elapsed:.2f}s")
            else:
                print(f"Worker {worker_id}: ❌ Falló {server} en {elapsed:.2f}s")
                
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"Worker {worker_id}: ❌ Error en {elapsed:.2f}s: {str(e)[:50]}")
        finally:
            sftp.disconnect()
    
    # Crear múltiples threads
    threads = []
    for i in range(4):
        t = threading.Thread(target=connect_worker, args=(i,), daemon=True)
        threads.append(t)
        t.start()
    
    # Esperar que terminen con timeout
    for t in threads:
        t.join(timeout=15)
    
    print("✅ Test de threads completado")

def test_real_server():
    """Test con servidor real del usuario"""
    print("\n🎯 Test con servidor real (***************)...")
    
    sftp = SFTPManager()
    start_time = time.time()
    try:
        # Conectar al servidor real con credenciales proporcionadas
        result = sftp.connect("***************", 22, "root", "5dhCkm5Dz1BpWgxZpdBisrOVo", timeout=10)
        elapsed = time.time() - start_time
        
        if result:
            print(f"✅ ¡CONEXIÓN EXITOSA! en {elapsed:.2f}s")
            
            # Test de operación básica
            try:
                files = sftp.list_directory("/")
                print(f"✅ Listado exitoso: {len(files)} archivos")
                
                # Intentar listar directorio home
                try:
                    home_files = sftp.list_directory("/root")
                    print(f"✅ Listado /root: {len(home_files)} archivos")
                except Exception as e:
                    print(f"⚠️  No se pudo acceder a /root: {e}")
                    
            except Exception as e:
                print(f"⚠️  Error en listado: {e}")
                
        else:
            print(f"❌ Falló la conexión en {elapsed:.2f}s")
    except Exception as e:
        elapsed = time.time() - start_time
        error_msg = str(e)
        print(f"❌ Error conectando en {elapsed:.2f}s: {error_msg}")
        
        # Análisis del error
        if "Authentication failed" in error_msg:
            print("🔐 Problema de autenticación - verificar usuario/contraseña")
        elif "Connection refused" in error_msg:
            print("🚫 Conexión rechazada - SSH no activo o puerto bloqueado")
        elif "timeout" in error_msg.lower() or "timed out" in error_msg:
            print("⏱️  Timeout - posible bloqueo de firewall o red")
        elif "Name or service not known" in error_msg:
            print("🌐 Error DNS - verificar que la IP sea correcta")
        elif "No route to host" in error_msg:
            print("🛣️  Sin ruta al host - problema de red")
        else:
            print(f"❓ Error desconocido: {error_msg}")
            
    finally:
        sftp.disconnect()
    
    print("\n" + "=" * 50)
    print("🏁 PRUEBA DE SERVIDOR REAL COMPLETADA")
    
if __name__ == "__main__":
    print("🚀 INICIANDO TESTS DE ROBUSTEZ SFTP")
    print("Objetivo: Verificar que no hay timeouts largos ni cuelgues")
    print()
    
    total_start = time.time()
    
    try:
        test_timeout_robustness()
        test_real_server()  # Probar servidor real primero
        test_multiple_connections()
        test_thread_safety()
        test_real_server()
        
        total_elapsed = time.time() - total_start
        print(f"\n🎉 TODOS LOS TESTS COMPLETADOS EN {total_elapsed:.2f}s")
        
        if total_elapsed > 60:
            print("⚠️  Los tests demoraron más de 1 minuto - revisar optimizaciones")
        else:
            print("✅ Tiempo total aceptable")
            
    except KeyboardInterrupt:
        print("\n🛑 Tests interrumpidos por el usuario")
    except Exception as e:
        print(f"\n❌ Error en tests: {e}")
    
    print("\n📋 RESUMEN:")
    print("- Todos los timeouts deben ser menores a 12 segundos")
    print("- No debe haber cuelgues ni threads zombi")
    print("- Los errores deben ser informativos y rápidos")
    print("- Las conexiones válidas deben funcionar correctamente")
