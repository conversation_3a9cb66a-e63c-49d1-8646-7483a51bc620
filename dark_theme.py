#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuración de estilos y tema visual para M3U Download Manager
Te<PERSON>: Negro con verde NVIDIA
"""

import tkinter as tk
from tkinter import ttk

class DarkGreenTheme:
    """Tema oscuro con acentos verdes de NVIDIA"""
    
    # Colores principales
    BACKGROUND_DARK = "#1e1e1e"      # Negro carbón
    BACKGROUND_MEDIUM = "#2d2d30"    # Gris oscuro
    BACKGROUND_LIGHT = "#3e3e42"     # Gris medio
    
    # Verde NVIDIA
    NVIDIA_GREEN = "#76b900"         # Verde NVIDIA principal
    NVIDIA_GREEN_DARK = "#5a8a00"    # Verde más oscuro
    NVIDIA_GREEN_LIGHT = "#9ed625"   # Verde más claro
    
    # Colores de texto
    TEXT_WHITE = "#ffffff"           # Blanco puro
    TEXT_LIGHT = "#cccccc"           # Gris claro
    TEXT_MEDIUM = "#969696"          # Gris medio
    TEXT_DISABLED = "#656565"        # Gris oscuro
    
    # Colores de estado
    SUCCESS_COLOR = "#4caf50"        # Verde éxito
    ERROR_COLOR = "#f44336"          # Rojo error
    WARNING_COLOR = "#ff9800"        # Naranja advertencia
    INFO_COLOR = "#2196f3"           # Azul información
    
    # Colores especiales
    SELECTION_COLOR = "#404040"      # Gris para selección
    BORDER_COLOR = "#555555"         # Gris para bordes
    SCROLLBAR_COLOR = "#404040"      # Color de scrollbar

class StyleManager:
    """Gestiona la aplicación del tema a la aplicación"""
    
    def __init__(self, root):
        self.root = root
        self.theme = DarkGreenTheme()
        self._setup_style()
    
    def _setup_style(self):
        """Configura el estilo TTK y la ventana principal"""
        # Configurar ventana principal
        self.root.configure(bg=self.theme.BACKGROUND_DARK)
        
        # Crear y configurar el estilo TTK
        self.style = ttk.Style()
        
        # Intentar usar un tema base oscuro si está disponible
        try:
            available_themes = self.style.theme_names()
            if 'clam' in available_themes:
                self.style.theme_use('clam')
            elif 'alt' in available_themes:
                self.style.theme_use('alt')
        except:
            pass
        
        self._configure_frame_styles()
        self._configure_button_styles()
        self._configure_entry_styles()
        self._configure_label_styles()
        self._configure_treeview_styles()
        self._configure_progressbar_styles()
        self._configure_combobox_styles()
        self._configure_notebook_styles()
        self._configure_panedwindow_styles()
    
    def _configure_frame_styles(self):
        """Configura estilos para Frame y LabelFrame"""
        # Frame normal
        self.style.configure("TFrame",
            background=self.theme.BACKGROUND_DARK,
            borderwidth=0,
            relief="flat"
        )
        
        # LabelFrame
        self.style.configure("TLabelframe",
            background=self.theme.BACKGROUND_DARK,
            borderwidth=1,
            relief="solid",
            bordercolor=self.theme.BORDER_COLOR
        )
        
        self.style.configure("TLabelframe.Label",
            background=self.theme.BACKGROUND_DARK,
            foreground=self.theme.NVIDIA_GREEN,
            font=('Arial', 9, 'bold')
        )
    
    def _configure_button_styles(self):
        """Configura estilos para botones"""
        # Botón normal
        self.style.configure("TButton",
            background=self.theme.BACKGROUND_LIGHT,
            foreground=self.theme.TEXT_WHITE,
            borderwidth=1,
            relief="solid",
            bordercolor=self.theme.BORDER_COLOR,
            font=('Arial', 9),
            padding=(10, 5)
        )
        
        self.style.map("TButton",
            background=[
                ('active', self.theme.NVIDIA_GREEN),
                ('pressed', self.theme.NVIDIA_GREEN_DARK),
                ('disabled', self.theme.BACKGROUND_MEDIUM)
            ],
            foreground=[
                ('active', self.theme.TEXT_WHITE),
                ('pressed', self.theme.TEXT_WHITE),
                ('disabled', self.theme.TEXT_DISABLED)
            ],
            bordercolor=[
                ('active', self.theme.NVIDIA_GREEN),
                ('pressed', self.theme.NVIDIA_GREEN_DARK),
                ('disabled', self.theme.BORDER_COLOR)
            ]
        )
        
        # Botón de acción principal (verde)
        self.style.configure("Accent.TButton",
            background=self.theme.NVIDIA_GREEN,
            foreground=self.theme.TEXT_WHITE,
            borderwidth=1,
            relief="solid",
            bordercolor=self.theme.NVIDIA_GREEN_DARK,
            font=('Arial', 9, 'bold'),
            padding=(12, 6)
        )
        
        self.style.map("Accent.TButton",
            background=[
                ('active', self.theme.NVIDIA_GREEN_LIGHT),
                ('pressed', self.theme.NVIDIA_GREEN_DARK)
            ]
        )
    
    def _configure_entry_styles(self):
        """Configura estilos para Entry"""
        self.style.configure("TEntry",
            fieldbackground=self.theme.BACKGROUND_MEDIUM,
            background=self.theme.BACKGROUND_MEDIUM,
            foreground=self.theme.TEXT_WHITE,
            borderwidth=1,
            relief="solid",
            bordercolor=self.theme.BORDER_COLOR,
            insertcolor=self.theme.NVIDIA_GREEN,
            selectbackground=self.theme.NVIDIA_GREEN,
            selectforeground=self.theme.TEXT_WHITE
        )
        
        self.style.map("TEntry",
            fieldbackground=[
                ('focus', self.theme.BACKGROUND_LIGHT),
                ('!focus', self.theme.BACKGROUND_MEDIUM)
            ],
            bordercolor=[
                ('focus', self.theme.NVIDIA_GREEN),
                ('!focus', self.theme.BORDER_COLOR)
            ]
        )
    
    def _configure_label_styles(self):
        """Configura estilos para Label"""
        # Label normal
        self.style.configure("TLabel",
            background=self.theme.BACKGROUND_DARK,
            foreground=self.theme.TEXT_LIGHT,
            font=('Arial', 9)
        )
        
        # Label de título
        self.style.configure("Title.TLabel",
            background=self.theme.BACKGROUND_DARK,
            foreground=self.theme.NVIDIA_GREEN,
            font=('Arial', 12, 'bold')
        )
        
        # Label de estado
        self.style.configure("Status.TLabel",
            background=self.theme.BACKGROUND_DARK,
            foreground=self.theme.TEXT_MEDIUM,
            font=('Arial', 8)
        )
        
        # Label de éxito
        self.style.configure("Success.TLabel",
            background=self.theme.BACKGROUND_DARK,
            foreground=self.theme.SUCCESS_COLOR,
            font=('Arial', 9, 'bold')
        )
        
        # Label de error
        self.style.configure("Error.TLabel",
            background=self.theme.BACKGROUND_DARK,
            foreground=self.theme.ERROR_COLOR,
            font=('Arial', 9, 'bold')
        )
    
    def _configure_treeview_styles(self):
        """Configura estilos para Treeview"""
        self.style.configure("Treeview",
            background=self.theme.BACKGROUND_MEDIUM,
            foreground=self.theme.TEXT_WHITE,
            fieldbackground=self.theme.BACKGROUND_MEDIUM,
            borderwidth=1,
            relief="solid",
            bordercolor=self.theme.BORDER_COLOR,
            rowheight=25
        )
        
        self.style.configure("Treeview.Heading",
            background=self.theme.BACKGROUND_LIGHT,
            foreground=self.theme.NVIDIA_GREEN,
            font=('Arial', 9, 'bold'),
            borderwidth=1,
            relief="solid",
            bordercolor=self.theme.BORDER_COLOR
        )
        
        self.style.map("Treeview",
            background=[
                ('selected', self.theme.SELECTION_COLOR),
                ('!selected', self.theme.BACKGROUND_MEDIUM)
            ],
            foreground=[
                ('selected', self.theme.TEXT_WHITE),
                ('!selected', self.theme.TEXT_WHITE)
            ]
        )
        
        self.style.map("Treeview.Heading",
            background=[
                ('active', self.theme.NVIDIA_GREEN),
                ('!active', self.theme.BACKGROUND_LIGHT)
            ],
            foreground=[
                ('active', self.theme.TEXT_WHITE),
                ('!active', self.theme.NVIDIA_GREEN)
            ]
        )
    
    def _configure_progressbar_styles(self):
        """Configura estilos para Progressbar"""
        self.style.configure("TProgressbar",
            background=self.theme.NVIDIA_GREEN,
            troughcolor=self.theme.BACKGROUND_MEDIUM,
            borderwidth=1,
            relief="solid",
            bordercolor=self.theme.BORDER_COLOR,
            lightcolor=self.theme.NVIDIA_GREEN_LIGHT,
            darkcolor=self.theme.NVIDIA_GREEN_DARK
        )
    
    def _configure_combobox_styles(self):
        """Configura estilos para Combobox"""
        self.style.configure("TCombobox",
            fieldbackground=self.theme.BACKGROUND_MEDIUM,
            background=self.theme.BACKGROUND_MEDIUM,
            foreground=self.theme.TEXT_WHITE,
            borderwidth=1,
            relief="solid",
            bordercolor=self.theme.BORDER_COLOR,
            arrowcolor=self.theme.NVIDIA_GREEN,
            selectbackground=self.theme.NVIDIA_GREEN,
            selectforeground=self.theme.TEXT_WHITE
        )
        
        self.style.map("TCombobox",
            fieldbackground=[
                ('focus', self.theme.BACKGROUND_LIGHT),
                ('!focus', self.theme.BACKGROUND_MEDIUM)
            ],
            bordercolor=[
                ('focus', self.theme.NVIDIA_GREEN),
                ('!focus', self.theme.BORDER_COLOR)
            ]
        )
    
    def _configure_notebook_styles(self):
        """Configura estilos para Notebook (pestañas)"""
        self.style.configure("TNotebook",
            background=self.theme.BACKGROUND_DARK,
            borderwidth=0
        )
        
        self.style.configure("TNotebook.Tab",
            background=self.theme.BACKGROUND_MEDIUM,
            foreground=self.theme.TEXT_LIGHT,
            borderwidth=1,
            relief="solid",
            bordercolor=self.theme.BORDER_COLOR,
            padding=(12, 8)
        )
        
        self.style.map("TNotebook.Tab",
            background=[
                ('selected', self.theme.NVIDIA_GREEN),
                ('active', self.theme.BACKGROUND_LIGHT),
                ('!active', self.theme.BACKGROUND_MEDIUM)
            ],
            foreground=[
                ('selected', self.theme.TEXT_WHITE),
                ('active', self.theme.TEXT_WHITE),
                ('!active', self.theme.TEXT_LIGHT)
            ]
        )
    
    def _configure_panedwindow_styles(self):
        """Configura estilos para PanedWindow"""
        self.style.configure("TPanedwindow",
            background=self.theme.BACKGROUND_DARK,
            borderwidth=0
        )
        
        self.style.configure("Sash",
            background=self.theme.NVIDIA_GREEN,
            borderwidth=2,
            relief="raised"
        )
    
    def configure_scrollbar(self, scrollbar):
        """Configura un scrollbar individual"""
        scrollbar.configure(
            bg=self.theme.BACKGROUND_MEDIUM,
            troughcolor=self.theme.BACKGROUND_DARK,
            activebackground=self.theme.NVIDIA_GREEN,
            borderwidth=1,
            relief="solid",
            highlightthickness=0
        )
    
    def get_color(self, color_name):
        """Obtiene un color del tema"""
        return getattr(self.theme, color_name, self.theme.TEXT_WHITE)

def apply_dark_theme(root):
    """Función de conveniencia para aplicar el tema oscuro"""
    return StyleManager(root)
