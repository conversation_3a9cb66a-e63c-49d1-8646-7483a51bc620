# 🔧 Guía de Solución de Problemas SFTP

## ❌ Problema: "No se conecta el SFTP"

### 🔍 Diagnóstico Paso a Paso

#### 1. Verificar Datos de Conexión
Asegúrate de que tienes los datos correctos:
- **Host/IP**: Dirección del servidor (ej: *************, servidor.ejemplo.com)
- **Puerto**: Normalmente 22 (puede ser diferente)
- **Usuario**: Tu nombre de usuario SSH
- **Contraseña**: Tu contraseña SSH

#### 2. Probar con Servidor de Prueba
Usa estos datos para probar la conexión:
- **Host**: `test.rebex.net`
- **Puerto**: `22`
- **Usuario**: `demo`
- **Contraseña**: `password`

Si esto funciona, el problema está en tus datos de conexión.

#### 3. Verificar Conectividad de Red

**En Windows PowerShell:**
```powershell
# Verificar conectividad básica
Test-NetConnection tu-servidor.com -Port 22

# O usar telnet
telnet tu-servidor.com 22
```

**En Command Prompt:**
```cmd
ping tu-servidor.com
```

#### 4. Ejecutar Diagnóstico Automático

**Opción A: Script de Diagnóstico Completo**
```cmd
python test_sftp_diagnosis.py
```

**Opción B: Script Simple**
1. Edita `test_sftp_simple.py` con tus datos
2. Ejecuta: `python test_sftp_simple.py`

### 🚨 Errores Comunes y Soluciones

#### Error: "No se puede resolver el hostname"
**Causa**: El nombre del servidor no existe o hay problemas de DNS
**Solución**: 
- Usar IP directa en lugar del nombre
- Verificar que el nombre del servidor sea correcto
- Comprobar configuración DNS

#### Error: "Conexión rechazada al puerto 22"
**Causa**: El servidor SSH no está ejecutándose o usa otro puerto
**Solución**:
- Verificar que el servicio SSH esté activo
- Comprobar si usa un puerto diferente (ej: 2222)
- Revisar configuración del firewall

#### Error: "Usuario o contraseña incorrectos"
**Causa**: Credenciales erróneas
**Solución**:
- Verificar usuario y contraseña
- Intentar conectar con otro cliente (WinSCP, FileZilla)
- Comprobar si la cuenta está bloqueada

#### Error: "Timeout de conexión"
**Causa**: Problemas de red o firewall
**Solución**:
- Verificar conectividad de red
- Comprobar configuración del firewall
- Intentar desde otra red

### 🔧 Herramientas de Diagnóstico

#### 1. Clientes SFTP Alternativos para Probar
- **WinSCP** (Windows): https://winscp.net/
- **FileZilla** (Multiplataforma): https://filezilla-project.org/
- **PuTTY** (Windows): https://www.putty.org/

#### 2. Comandos de Red Útiles

**PowerShell:**
```powershell
# Test de puerto específico
Test-NetConnection servidor.com -Port 22

# Información de red
Get-NetRoute
ipconfig /all
```

**Command Prompt:**
```cmd
# Ping básico
ping servidor.com

# Traceroute
tracert servidor.com

# Información de conexiones
netstat -an | findstr :22
```

### 📝 Información para Recopilar

Si el problema persiste, recopila esta información:

1. **Mensaje de error exacto** de la aplicación
2. **Datos de conexión** (sin la contraseña)
3. **Resultado de ping** al servidor
4. **Resultado de Test-NetConnection** al puerto 22
5. **¿Funciona con otros clientes SFTP?**
6. **¿Estás detrás de un firewall corporativo?**
7. **¿Estás usando VPN?**

### 🎯 Pasos de Solución Rápida

1. **Prueba con servidor demo**: `test.rebex.net:22` (usuario: `demo`, contraseña: `password`)
2. **Verifica conectividad**: `ping tu-servidor.com`
3. **Prueba el puerto**: `Test-NetConnection tu-servidor.com -Port 22`
4. **Usa cliente alternativo**: Instala WinSCP y prueba los mismos datos
5. **Revisa los logs**: Ejecuta la aplicación y observa los mensajes en consola

### 🔒 Consideraciones de Seguridad

- Algunos servidores requieren **autenticación por clave SSH** en lugar de contraseña
- Puede haber **restricciones de IP** que bloqueen tu conexión
- Algunos proveedores requieren **configuración adicional** para SFTP

### 📞 Si Nada Funciona

1. **Contacta al administrador** del servidor para verificar:
   - Estado del servicio SSH
   - Configuración del usuario
   - Restricciones de IP/firewall

2. **Proporciona información técnica**:
   - IP desde la que te conectas
   - Mensaje de error exacto
   - Resultado de pruebas de conectividad

---

## 🛠️ Mejoras Implementadas en la Aplicación

### Diagnóstico Mejorado
- ✅ Mensajes de error más detallados
- ✅ Logging de pasos de conexión
- ✅ Sugerencias específicas por tipo de error
- ✅ Validación previa de datos

### Scripts de Diagnóstico
- ✅ `test_sftp_diagnosis.py` - Diagnóstico completo
- ✅ `test_sftp_simple.py` - Test básico personalizable
- ✅ `config_demo.json` - Configuración con servidor de prueba

### Interfaz Mejorada
- ✅ Estados de conexión más claros
- ✅ Mensajes informativos de éxito
- ✅ Indicadores visuales de progreso de conexión
