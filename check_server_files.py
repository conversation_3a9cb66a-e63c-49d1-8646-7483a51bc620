#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verificar si los archivos se están descargando realmente
"""

import paramiko
import time

HOSTNAME = "***************"
PORT = 22
USERNAME = "root"
PASSWORD = "5dhCkm5Dz1BpWgxZpdBisrOVo"

def check_server_files():
    print("🔍 VERIFICANDO ARCHIVOS EN SERVIDOR...")
    
    client = paramiko.SSHClient()
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    client.connect(
        hostname=HOSTNAME,
        port=PORT,
        username=USERNAME,
        password=PASSWORD,
        timeout=15,
        allow_agent=False,
        look_for_keys=False,
        compress=True
    )
    
    print("✅ Conectado")
    
    # Verificar archivos recientes en /tmp
    print("\n📂 Archivos recientes en /tmp:")
    recent_cmd = 'find /tmp -name "*test*" -o -name "*download*" -o -name "*wget*" | head -20'
    stdin, stdout, stderr = client.exec_command(recent_cmd)
    files = stdout.read().decode('utf-8', errors='ignore').strip()
    
    if files:
        print(files)
        
        # Mostrar detalles de cada archivo
        for file_line in files.split('\n'):
            if file_line.strip():
                detail_cmd = f'ls -lh "{file_line.strip()}" 2>/dev/null && echo "---" && head -c 200 "{file_line.strip()}" 2>/dev/null && echo ""'
                stdin, stdout, stderr = client.exec_command(detail_cmd)
                details = stdout.read().decode('utf-8', errors='ignore')
                print(f"\n📄 {file_line}:")
                print(details)
    else:
        print("No se encontraron archivos de test")
    
    # Verificar procesos wget activos
    print("\n🔄 Procesos wget activos:")
    ps_cmd = 'ps aux | grep wget | grep -v grep'
    stdin, stdout, stderr = client.exec_command(ps_cmd)
    processes = stdout.read().decode('utf-8', errors='ignore').strip()
    
    if processes:
        print(processes)
    else:
        print("No hay procesos wget activos")
    
    # Ejecutar descarga simple y verificar inmediatamente
    print("\n🚀 EJECUTANDO DESCARGA DE PRUEBA SIMPLE...")
    test_file = "/tmp/simple_test.json"
    log_file = "/tmp/simple_test.log"
    
    # Limpiar
    cleanup_cmd = f'rm -f {test_file} {log_file}'
    stdin, stdout, stderr = client.exec_command(cleanup_cmd)
    
    # Descarga síncrona (sin nohup para ver el resultado)
    sync_cmd = f'wget --timeout=30 --user-agent="XCIPTV" -O "{test_file}" "https://httpbin.org/uuid" > {log_file} 2>&1; echo "EXIT_CODE: $?"'
    
    print("Ejecutando wget síncrono...")
    start_time = time.time()
    stdin, stdout, stderr = client.exec_command(sync_cmd)
    
    # Esperar a que termine
    output = stdout.read().decode('utf-8', errors='ignore')
    elapsed = time.time() - start_time
    
    print(f"⏱️ Comando terminó en {elapsed:.2f}s")
    print(f"📝 Salida: {output}")
    
    # Verificar resultado
    check_cmd = f'[ -f "{test_file}" ] && ls -lh "{test_file}" && cat "{test_file}" || echo "Archivo no encontrado"'
    stdin, stdout, stderr = client.exec_command(check_cmd)
    result = stdout.read().decode('utf-8', errors='ignore')
    print(f"📄 Resultado:\n{result}")
    
    # Mostrar log
    log_cmd = f'cat {log_file}'
    stdin, stdout, stderr = client.exec_command(log_cmd)
    log_content = stdout.read().decode('utf-8', errors='ignore')
    print(f"📝 Log completo:\n{log_content}")
    
    client.close()

if __name__ == "__main__":
    check_server_files()
