# M3U Manager with SFTP Download

## Descripción

Este software permite gestionar listas M3U con protocolo Xtream Codes y realizar descargas automáticas a través de SFTP. La aplicación cuenta con una interfaz gráfica moderna que permite:

- Cargar y visualizar listas M3U locales o desde URLs
- Conectar con servidores Xtream Codes para obtener contenido (Live TV, Películas, Series)
- Navegar por servidores SFTP/SSH de forma remota
- Realizar descargas automáticas con wget y subida automática al servidor
- Control avanzado de cola de descargas con pausa/reanudación
- Descarga masiva de series completas o temporadas
- Barras de progreso animadas y estadísticas en tiempo real

## Características Principales

### Panel M3U
- **Carga de archivos M3U locales**: Soporte para archivos .m3u y .m3u8
- **Carga desde URL**: Descarga automática de listas desde internet
- **Protocolo Xtream Codes**: Conexión nativa con servidores IPTV
- **Filtrado avanzado**: Por nombre, grupo y tipo de contenido
- **Clasificación automática**: Detecta automáticamente Live TV, Películas y Series

### Panel SFTP
- **Conexión SSH/SFTP**: Acceso seguro a servidores remotos
- **Navegación de archivos**: Interfaz tipo explorador de archivos
- **Gestión de directorios**: Crear, eliminar y navegar carpetas
- **Información detallada**: Tamaños, fechas, permisos de archivos

### Sistema de Descargas
- **Descargas concurrentes**: Hasta 3 descargas simultáneas (configurable)
- **wget integrado**: Utiliza wget para descargas robustas y reanudables
- **Control de cola**: Pausar, reanudar, cancelar descargas
- **Progreso en tiempo real**: Barras de progreso y estadísticas
- **Renombre automático**: Limpieza automática de nombres de archivo
- **Subida automática**: Los archivos se suben automáticamente al servidor SFTP
- **Persistencia**: Mantiene memoria de la cola entre sesiones

### Funciones Especiales para Series
- **Detección de episodios**: Reconoce automáticamente series y episodios
- **Descarga por temporada**: Descarga temporadas completas de una vez
- **Descarga de serie completa**: Descarga todas las temporadas disponibles
- **Organización automática**: Mantiene estructura de carpetas por series

## Requisitos del Sistema

### Software Necesario
- **Python 3.11+**: Lenguaje de programación
- **wget**: Herramienta de descarga (incluida en la mayoría de sistemas Linux/macOS, descargable para Windows)
- **Acceso SFTP/SSH**: Servidor remoto con acceso SSH

### Dependencias Python
```
requests>=2.31.0
paramiko>=3.3.1
m3u8>=3.5.0
urllib3>=2.0.0
```

## Instalación

### 1. Clonar o descargar el proyecto
```bash
git clone <url-del-repositorio>
cd M3U-IMPORT-CLOUD
```

### 2. Crear entorno virtual (recomendado)
```bash
python -m venv .venv
```

#### Activar entorno virtual:
**Windows:**
```bash
.venv\Scripts\activate
```

**Linux/macOS:**
```bash
source .venv/bin/activate
```

### 3. Instalar dependencias
```bash
pip install -r requirements.txt
```

### 4. Instalar wget (si no está disponible)

**Windows:**
- Descargar desde: https://eternallybored.org/misc/wget/
- Agregar al PATH del sistema

**Linux:**
```bash
sudo apt-get install wget  # Ubuntu/Debian
sudo yum install wget      # CentOS/RHEL
```

**macOS:**
```bash
brew install wget
```

## Uso de la Aplicación

### Iniciar la aplicación
```bash
python main.py
```

### Configuración Inicial

#### 1. Configurar Xtream Codes (opcional)
- **URL del servidor**: `http://servidor.com:puerto`
- **Usuario**: Tu nombre de usuario
- **Contraseña**: Tu contraseña
- Hacer clic en "Conectar"

#### 2. Configurar SFTP
- **Host**: Dirección IP o dominio del servidor
- **Puerto**: Normalmente 22
- **Usuario**: Usuario SSH
- **Contraseña**: Contraseña SSH
- Hacer clic en "Conectar"

### Flujo de Trabajo Típico

1. **Cargar contenido M3U**:
   - Usar "Cargar M3U" para archivo local
   - Usar "Cargar URL" para lista online
   - O conectar con Xtream Codes

2. **Navegar al directorio de destino**:
   - Usar el panel SFTP para navegar
   - Crear carpetas si es necesario
   - Seleccionar carpeta de destino

3. **Seleccionar contenido para descarga**:
   - En el panel M3U, seleccionar elementos
   - Click derecho para ver opciones
   - Elegir "Descargar Seleccionado" o opciones de serie

4. **Gestionar descargas**:
   - Usar botones de control para iniciar/pausar
   - Monitorear progreso en panel inferior
   - Ver estadísticas en tiempo real

### Opciones de Descarga

#### Contenido Individual
- **Películas**: Descarga directa del archivo
- **Episodios**: Descarga del episodio seleccionado

#### Series (opciones especiales)
- **Descargar Episodio**: Solo el episodio seleccionado
- **Descargar Temporada**: Todos los episodios de la temporada
- **Descargar Serie Completa**: Todas las temporadas disponibles

### Filtros y Búsqueda

- **Filtro por texto**: Busca en nombres y grupos
- **Filtro por tipo**: Live TV, Películas, Series, Todo
- **Botón Limpiar**: Resetea todos los filtros

## Configuración Avanzada

### Archivo de Configuración
La aplicación guarda automáticamente la configuración en `config.json`:
```json
{
  "xtream": {
    "url": "http://servidor.com:8000",
    "user": "usuario",
    "password": "contraseña"
  },
  "sftp": {
    "host": "servidor.com",
    "port": "22",
    "user": "usuario"
  }
}
```

### Personalización de wget
Puedes modificar las opciones de wget en `download_manager.py`:
```python
self.wget_options = [
    '--no-check-certificate',
    '--timeout=30',
    '--tries=3',
    '--retry-connrefused',
    '--waitretry=5',
    '--progress=bar:force'
]
```

### Número de Descargas Concurrentes
Modificar en `DownloadManager.__init__()`:
```python
self.max_concurrent_downloads = 3  # Cambiar según necesidades
```

## Estructura del Proyecto

```
M3U-IMPORT-CLOUD/
├── main.py              # Aplicación principal
├── m3u_parser.py        # Analizador de M3U y Xtream Codes
├── sftp_manager.py      # Gestor de conexiones SFTP
├── download_manager.py  # Gestor de descargas
├── ui_components.py     # Componentes de interfaz
├── requirements.txt     # Dependencias
├── README.md           # Este archivo
├── config.json         # Configuración (se crea automáticamente)
├── download_queue.json # Cola de descargas (se crea automáticamente)
└── temp_downloads/     # Archivos temporales (se crea automáticamente)
```

## Solución de Problemas

### Error: "wget no encontrado"
- Asegúrate de que wget esté instalado y en el PATH
- En Windows, verifica la instalación de wget

### Error de conexión SFTP
- Verifica credenciales SSH
- Confirma que el puerto 22 (o el configurado) esté abierto
- Prueba la conexión con un cliente SSH manual

### Error de conexión Xtream Codes
- Verifica la URL del servidor (incluye http:// y puerto)
- Confirma credenciales de usuario
- Verifica que el servidor esté online

### Descargas lentas o fallan
- Verifica conexión a internet
- Reduce número de descargas concurrentes
- Verifica espacio disponible en el servidor SFTP

### Problemas de permisos
- Asegúrate de tener permisos de escritura en el servidor SFTP
- Verifica permisos de la carpeta temporal local

## Funciones Técnicas

### Protocolo Xtream Codes
La aplicación utiliza las siguientes APIs:
- `get_live_categories` y `get_live_streams` para canales en vivo
- `get_vod_categories` y `get_vod_streams` para películas
- `get_series_categories`, `get_series` y `get_series_info` para series

### Gestión de Archivos
- **Renombre automático**: Elimina caracteres especiales de nombres
- **Estructura de carpetas**: Mantiene organización por tipo de contenido
- **Limpieza temporal**: Elimina archivos temporales después de subir

### Seguridad
- Las contraseñas no se almacenan en texto plano visible
- Conexiones SFTP cifradas
- Validación de certificados SSL (deshabilitada por defecto para flexibilidad)

## Contribuciones

Para contribuir al proyecto:
1. Fork el repositorio
2. Crea una rama para tu feature
3. Commit tus cambios
4. Push a tu rama
5. Crea un Pull Request

## Licencia

Este proyecto está bajo una licencia de uso personal y educativo.

## Contacto

Para reportar bugs o solicitar features, crear un issue en el repositorio del proyecto.
