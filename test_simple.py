#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple y directo para 185.150.191.172
"""

import time
from sftp_manager import SFTPManager

def test_direct():
    """Test directo sin florituras"""
    print("🎯 TEST DIRECTO SERVIDOR 185.150.191.172")
    print("=" * 40)
    
    sftp = SFTPManager()
    
    print("Conectando...")
    start_time = time.time()
    
    try:
        result = sftp.connect(
            "185.150.191.172", 
            22, 
            "root", 
            "5dhCkm5Dz1BpWgxZpdBisrOVo", 
            timeout=15
        )
        
        elapsed = time.time() - start_time
        
        if result:
            print(f"✅ CONECTADO en {elapsed:.2f}s")
            try:
                files = sftp.list_directory("/")
                print(f"✅ Listado: {len(files)} archivos")
            except Exception as e:
                print(f"❌ Error listado: {e}")
        else:
            print(f"❌ NO CONECTÓ en {elapsed:.2f}s")
            
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ ERROR en {elapsed:.2f}s: {e}")
        print(f"Tipo de error: {type(e).__name__}")
        
    finally:
        sftp.disconnect()
        print("Desconectado.")

if __name__ == "__main__":
    test_direct()
