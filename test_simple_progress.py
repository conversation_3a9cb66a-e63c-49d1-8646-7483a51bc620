#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test simple de progreso de descarga
"""

import paramiko
import time
from datetime import datetime

HOSTNAME = "***************"
PORT = 22
USERNAME = "root"
PASSWORD = "5dhCkm5Dz1BpWgxZpdBisrOVo"

def test_download_with_progress():
    print("🔄 TEST DE PROGRESO DE DESCARGA")
    print("=" * 50)
    
    # Conectar
    client = paramiko.SSHClient()
    client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
    client.connect(
        hostname=HOSTNAME,
        port=PORT,
        username=USERNAME,
        password=PASSWORD,
        timeout=15,
        allow_agent=False,
        look_for_keys=False,
        compress=True
    )
    
    print("✅ Conectado al servidor")
    
    # Configurar descarga
    test_url = "https://proof.ovh.net/files/10Mb.dat"  # Archivo de 10MB para ver progreso
    test_file = "/tmp/test_10mb_download.dat"
    log_file = "/tmp/wget_progress_test.log"
    
    print(f"📥 Descargando: {test_url}")
    print(f"💾 Destino: {test_file}")
    
    # Limpiar archivos anteriores
    cleanup_cmd = f'rm -f {test_file} {log_file}'
    stdin, stdout, stderr = client.exec_command(cleanup_cmd)
    
    # Comando wget con progreso visible
    wget_cmd = (
        f'nohup wget --progress=dot:mega --timeout=120 --tries=3 '
        f'--user-agent="XCIPTV" '
        f'--output-document="{test_file}" "{test_url}" > {log_file} 2>&1 &'
    )
    
    print("🚀 Iniciando descarga...")
    start_time = time.time()
    
    # Ejecutar wget
    stdin, stdout, stderr = client.exec_command(wget_cmd)
    time.sleep(2)
    
    # Buscar PID
    check_process_cmd = f'ps aux | grep wget | grep "{test_url}" | grep -v grep'
    stdin, stdout, stderr = client.exec_command(check_process_cmd)
    process_info = stdout.read().decode('utf-8', errors='ignore').strip()
    
    pid = None
    if process_info:
        try:
            pid = process_info.split()[1]
            print(f"🆔 PID: {pid}")
        except:
            pass
    
    # Monitorear progreso
    print("\n📊 MONITOREANDO PROGRESO:")
    print("Tiempo | Estado | Tamaño | Log")
    print("-" * 50)
    
    max_wait = 120  # 2 minutos máximo
    waited = 0
    
    while waited < max_wait:
        time.sleep(3)
        waited += 3
        
        # Verificar si el proceso sigue corriendo
        is_running = False
        if pid:
            check_running_cmd = f'ps -p {pid} -o pid= | wc -l'
            stdin, stdout, stderr = client.exec_command(check_running_cmd)
            is_running = int(stdout.read().decode('utf-8', errors='ignore').strip()) > 0
        
        # Verificar tamaño del archivo
        check_size_cmd = f'[ -f "{test_file}" ] && stat -c "%s" "{test_file}" 2>/dev/null || echo "0"'
        stdin, stdout, stderr = client.exec_command(check_size_cmd)
        try:
            current_size = int(stdout.read().decode('utf-8', errors='ignore').strip())
        except:
            current_size = 0
        
        # Formatear tamaño
        def format_size(bytes_val):
            if bytes_val < 1024:
                return f"{bytes_val} B"
            elif bytes_val < 1024*1024:
                return f"{bytes_val/1024:.1f} KB"
            elif bytes_val < 1024*1024*1024:
                return f"{bytes_val/(1024*1024):.1f} MB"
            else:
                return f"{bytes_val/(1024*1024*1024):.1f} GB"
        
        size_str = format_size(current_size)
        
        # Obtener últimas líneas del log
        tail_cmd = f'tail -n 3 {log_file} 2>/dev/null | grep -E "(K|M|%)" | tail -1'
        stdin, stdout, stderr = client.exec_command(tail_cmd)
        log_line = stdout.read().decode('utf-8', errors='ignore').strip()
        
        # Estado
        if is_running:
            status = "🔄 ACTIVO"
        elif current_size > 0:
            status = "✅ COMPLETO"
        else:
            status = "❓ PARADO"
        
        print(f"{waited:>3}s | {status} | {size_str:>8} | {log_line[:30]}")
        
        # Si ya no está corriendo y hay archivo, terminar
        if not is_running and current_size > 0:
            print("\n🎉 ¡Descarga completada!")
            break
        elif not is_running and current_size == 0 and waited > 10:
            print("\n❌ Descarga falló o no se inició")
            break
    
    # Mostrar resultado final
    print(f"\n📋 RESULTADO FINAL:")
    
    # Información del archivo
    final_check_cmd = f'[ -f "{test_file}" ] && ls -lh "{test_file}" || echo "Archivo no encontrado"'
    stdin, stdout, stderr = client.exec_command(final_check_cmd)
    file_info = stdout.read().decode('utf-8', errors='ignore').strip()
    print(f"📄 {file_info}")
    
    # Log completo
    print(f"\n📝 LOG COMPLETO:")
    full_log_cmd = f'cat {log_file} 2>/dev/null || echo "Sin log"'
    stdin, stdout, stderr = client.exec_command(full_log_cmd)
    log_content = stdout.read().decode('utf-8', errors='ignore')
    print(log_content)
    
    total_time = time.time() - start_time
    print(f"\n⏱️ Tiempo total: {total_time:.2f}s")
    
    client.close()

if __name__ == "__main__":
    test_download_with_progress()
