#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SFTP Connection Wizard - Asistente para configurar y probar conexión SFTP
"""

import paramiko
import socket
import sys
import json
import os

def test_network_connectivity(host, port):
    """Test de conectividad de red básica"""
    print(f"🔄 Probando conectividad a {host}:{port}...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print("✅ Puerto accesible")
            return True
        else:
            print("❌ Puerto no accesible")
            return False
    except Exception as e:
        print(f"❌ Error de red: {e}")
        return False

def test_ssh_connection(host, port, username, password):
    """Test de conexión SSH"""
    print(f"🔄 Probando conexión SSH...")
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        ssh.connect(
            hostname=host,
            port=port,
            username=username,
            password=password,
            timeout=15,
            allow_agent=False,
            look_for_keys=False
        )
        
        print("✅ Conexión SSH exitosa")
        ssh.close()
        return True
        
    except paramiko.AuthenticationException:
        print("❌ Error de autenticación: Usuario o contraseña incorrectos")
        return False
    except paramiko.SSHException as e:
        print(f"❌ Error SSH: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_sftp_operations(host, port, username, password):
    """Test de operaciones SFTP"""
    print(f"🔄 Probando operaciones SFTP...")
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        ssh.connect(hostname=host, port=port, username=username, password=password, timeout=15)
        sftp = ssh.open_sftp()
        
        # Test básico: listar directorio
        files = sftp.listdir('.')
        print(f"✅ SFTP funcionando - {len(files)} archivos encontrados")
        
        # Mostrar algunos archivos
        print("📁 Archivos encontrados:")
        for i, file in enumerate(files[:5]):
            print(f"   - {file}")
        if len(files) > 5:
            print(f"   ... y {len(files)-5} más")
        
        sftp.close()
        ssh.close()
        return True
        
    except Exception as e:
        print(f"❌ Error SFTP: {e}")
        return False

def save_working_config(host, port, username):
    """Guarda configuración que funciona"""
    config = {
        "sftp": {
            "host": host,
            "port": str(port),
            "user": username
        },
        "note": "Configuración validada por el asistente SFTP"
    }
    
    try:
        with open("config_validated.json", "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2)
        print("💾 Configuración guardada en 'config_validated.json'")
    except Exception as e:
        print(f"⚠️ No se pudo guardar la configuración: {e}")

def main():
    print("🚀 Asistente de Conexión SFTP")
    print("=" * 40)
    print("Este asistente te ayudará a configurar y probar tu conexión SFTP paso a paso")
    print()
    
    # Opción 1: Probar con servidor demo
    print("🎯 OPCIÓN 1: Probar con servidor demo")
    print("¿Quieres probar primero con un servidor demo público? (s/n): ", end="")
    test_demo = input().lower().strip()
    
    if test_demo in ['s', 'si', 'sí', 'y', 'yes']:
        print("\n🔄 Probando con servidor demo...")
        host, port, username, password = "test.rebex.net", 22, "demo", "password"
        
        if test_network_connectivity(host, port):
            if test_ssh_connection(host, port, username, password):
                if test_sftp_operations(host, port, username, password):
                    print("\n✅ ¡El sistema SFTP funciona correctamente!")
                    print("El problema está en tus datos de conexión específicos.")
    
    print("\n" + "="*40)
    print("🎯 OPCIÓN 2: Probar con tus datos")
    
    # Solicitar datos del usuario
    print("\nIngresa tus datos de conexión SFTP:")
    host = input("Host/IP del servidor: ").strip()
    port_str = input("Puerto (presiona Enter para 22): ").strip()
    port = int(port_str) if port_str else 22
    username = input("Usuario: ").strip()
    password = input("Contraseña: ").strip()
    
    if not all([host, username, password]):
        print("❌ Error: Debes completar todos los campos")
        return
    
    print(f"\n🔄 Probando conexión a {username}@{host}:{port}")
    
    # Ejecutar pruebas paso a paso
    step1 = test_network_connectivity(host, port)
    if not step1:
        print("\n❌ FALLO EN CONECTIVIDAD DE RED")
        print("Posibles soluciones:")
        print("• Verificar que el host/IP sea correcto")
        print("• Comprobar que el puerto SSH sea el correcto")
        print("• Revisar configuración de firewall")
        print("• Probar desde otra red")
        return
    
    step2 = test_ssh_connection(host, port, username, password)
    if not step2:
        print("\n❌ FALLO EN AUTENTICACIÓN SSH")
        print("Posibles soluciones:")
        print("• Verificar usuario y contraseña")
        print("• Comprobar si la cuenta está activa")
        print("• Intentar con otro cliente SSH (PuTTY)")
        print("• Contactar al administrador del servidor")
        return
    
    step3 = test_sftp_operations(host, port, username, password)
    if not step3:
        print("\n❌ FALLO EN OPERACIONES SFTP")
        print("Posibles soluciones:")
        print("• Verificar que SFTP esté habilitado en el servidor")
        print("• Comprobar permisos del usuario")
        print("• Contactar al administrador del servidor")
        return
    
    # ¡Éxito!
    print("\n🎉 ¡CONEXIÓN SFTP COMPLETAMENTE FUNCIONAL!")
    print("Todos los tests pasaron exitosamente.")
    
    # Guardar configuración
    save_config = input("\n¿Guardar esta configuración? (s/n): ").lower().strip()
    if save_config in ['s', 'si', 'sí', 'y', 'yes']:
        save_working_config(host, port, username)
    
    print("\n✅ Puedes usar estos mismos datos en la aplicación M3U Manager")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Operación cancelada")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        import traceback
        traceback.print_exc()
