#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test GUI - Prueba básica de la interfaz gráfica
"""

import tkinter as tk
import subprocess
import sys
import time

def test_gui_launch():
    """Test de lanzamiento de la interfaz gráfica"""
    print("🔄 Probando lanzamiento de la interfaz gráfica...")
    
    try:
        # Intentar importar los módulos principales
        from main import M3UDownloadApp
        print("✅ Módulos importados correctamente")
        
        # Crear ventana de prueba
        root = tk.Tk()
        print("✅ Tkinter inicializado")
        
        # Intentar crear la aplicación
        app = M3UDownloadApp(root)
        print("✅ Aplicación creada exitosamente")
        
        # Verificar atributos clave
        assert hasattr(app, 'config_file'), "❌ Falta atributo config_file"
        assert hasattr(app, 'update_queue'), "❌ Falta atributo update_queue"
        assert hasattr(app, 'm3u_parser'), "❌ Falta atributo m3u_parser"
        assert hasattr(app, 'sftp_manager'), "❌ Falta atributo sftp_manager"
        assert hasattr(app, 'download_manager'), "❌ Falta atributo download_manager"
        print("✅ Todos los atributos necesarios están presentes")
        
        # Cerrar la ventana de prueba
        root.destroy()
        print("✅ Test GUI completado exitosamente")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en test GUI: {e}")
        return False

if __name__ == "__main__":
    print("M3U Manager - Test de Interfaz Gráfica")
    print("=" * 50)
    
    success = test_gui_launch()
    
    if success:
        print("\n🎉 ¡La aplicación está lista para usar!")
        print("\nPara ejecutar la aplicación completa:")
        print("   python main.py")
        print("\nCaracterísticas disponibles:")
        print("✅ Carga de listas M3U (archivo, URL, Xtream Codes)")
        print("✅ Conexión SFTP/SSH para navegación remota")
        print("✅ Gestión de descargas con cola y progreso")
        print("✅ Filtrado y búsqueda de contenido")
        print("✅ Interfaz gráfica completa y funcional")
    else:
        print("\n❌ Hay problemas con la aplicación")
        sys.exit(1)
