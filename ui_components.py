#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI Components - Componentes reutilizables para la interfaz de usuario
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
from typing import Callable, Any, Optional

class UIComponents:
    def __init__(self, root):
        self.root = root
        self.setup_styles()
        
    def setup_styles(self):
        """Configura estilos personalizados para la UI"""
        style = ttk.Style()
        
        # Configurar tema
        try:
            style.theme_use('clam')  # Tema moderno
        except:
            pass
        
        # Estilos personalizados
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Status.TLabel', font=('Arial', 9))
        style.configure('Error.TLabel', foreground='red', font=('Arial', 9))
        style.configure('Success.TLabel', foreground='green', font=('Arial', 9))
        style.configure('Warning.TLabel', foreground='orange', font=('Arial', 9))
        
        # Progressbar personalizada
        style.configure('Custom.Horizontal.TProgressbar', background='#4CAF50')
        
    def create_labeled_entry(self, parent, label_text, entry_var=None, width=None, show=None):
        """Crea un Entry con etiqueta"""
        frame = ttk.Frame(parent)
        
        label = ttk.Label(frame, text=label_text)
        label.pack(side='left')
        
        if entry_var is None:
            entry_var = tk.StringVar()
        
        entry_kwargs = {'textvariable': entry_var}
        if width:
            entry_kwargs['width'] = width
        if show:
            entry_kwargs['show'] = show
            
        entry = ttk.Entry(frame, **entry_kwargs)
        entry.pack(side='left', padx=5, fill='x', expand=True)
        
        frame.entry_var = entry_var
        frame.entry = entry
        frame.label = label
        
        return frame
    
    def create_button_group(self, parent, buttons_config):
        """Crea un grupo de botones
        buttons_config: lista de diccionarios con 'text' y 'command'
        """
        frame = ttk.Frame(parent)
        
        for btn_config in buttons_config:
            btn = ttk.Button(frame, 
                           text=btn_config['text'],
                           command=btn_config['command'])
            btn.pack(side='left', padx=2)
            
        return frame
    
    def create_status_bar(self, parent):
        """Crea una barra de estado"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(side='bottom', fill='x', padx=5, pady=2)
        
        # Label principal de estado
        status_label = ttk.Label(status_frame, text="Listo", style='Status.TLabel')
        status_label.pack(side='left')
        
        # Separador
        ttk.Separator(status_frame, orient='vertical').pack(side='left', fill='y', padx=10)
        
        # Información adicional
        info_label = ttk.Label(status_frame, text="", style='Status.TLabel')
        info_label.pack(side='left')
        
        # Progreso general
        progress = ttk.Progressbar(status_frame, mode='determinate', length=100)
        progress.pack(side='right', padx=5)
        
        status_frame.status_label = status_label
        status_frame.info_label = info_label
        status_frame.progress = progress
        
        return status_frame
    
    def create_tree_with_scrollbars(self, parent, columns, headings=None, show='headings'):
        """Crea un Treeview con scrollbars"""
        frame = ttk.Frame(parent)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(frame, orient='vertical')
        h_scrollbar = ttk.Scrollbar(frame, orient='horizontal')
        
        # Treeview
        tree = ttk.Treeview(frame, columns=columns, show=show,
                           yscrollcommand=v_scrollbar.set,
                           xscrollcommand=h_scrollbar.set)
        
        # Configurar scrollbars
        v_scrollbar.config(command=tree.yview)
        h_scrollbar.config(command=tree.xview)
        
        # Configurar headings
        if headings:
            for col, heading in zip(columns, headings):
                tree.heading(col, text=heading)
        else:
            for col in columns:
                tree.heading(col, text=col)
        
        # Pack elementos
        tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')
        
        frame.tree = tree
        frame.v_scrollbar = v_scrollbar
        frame.h_scrollbar = h_scrollbar
        
        return frame
    
    def create_connection_panel(self, parent, title, fields_config, connect_callback):
        """Crea un panel de conexión con campos configurables
        fields_config: lista de diccionarios con configuración de campos
        """
        panel_frame = ttk.LabelFrame(parent, text=title)
        
        fields = {}
        
        for field_config in fields_config:
            field_frame = ttk.Frame(panel_frame)
            field_frame.pack(fill='x', padx=5, pady=2)
            
            label = ttk.Label(field_frame, text=field_config['label'])
            label.pack(side='left')
            
            var = tk.StringVar(value=field_config.get('default', ''))
            entry_kwargs = {
                'textvariable': var,
                'width': field_config.get('width', 20)
            }
            
            if field_config.get('password', False):
                entry_kwargs['show'] = '*'
            
            entry = ttk.Entry(field_frame, **entry_kwargs)
            entry.pack(side='left', padx=5, fill='x', expand=True)
            
            fields[field_config['name']] = {
                'var': var,
                'entry': entry,
                'label': label
            }
        
        # Botones
        btn_frame = ttk.Frame(panel_frame)
        btn_frame.pack(fill='x', padx=5, pady=5)
        
        connect_btn = ttk.Button(btn_frame, text="Conectar", 
                                command=lambda: connect_callback(fields))
        connect_btn.pack(side='left', padx=2)
        
        disconnect_btn = ttk.Button(btn_frame, text="Desconectar")
        disconnect_btn.pack(side='left', padx=2)
        
        status_label = ttk.Label(btn_frame, text="Desconectado", style='Error.TLabel')
        status_label.pack(side='left', padx=10)
        
        panel_frame.fields = fields
        panel_frame.connect_btn = connect_btn
        panel_frame.disconnect_btn = disconnect_btn
        panel_frame.status_label = status_label
        
        return panel_frame
    
    def create_filter_panel(self, parent, filter_callback):
        """Crea un panel de filtros"""
        filter_frame = ttk.Frame(parent)
        
        # Campo de búsqueda
        ttk.Label(filter_frame, text="Filtrar:").pack(side='left')
        
        search_var = tk.StringVar()
        search_var.trace('w', lambda *args: filter_callback())
        search_entry = ttk.Entry(filter_frame, textvariable=search_var, width=30)
        search_entry.pack(side='left', padx=5, fill='x', expand=True)
        
        # Tipo de contenido
        ttk.Label(filter_frame, text="Tipo:").pack(side='left', padx=(10, 0))
        type_var = tk.StringVar(value="all")
        type_combo = ttk.Combobox(filter_frame, textvariable=type_var,
                                 values=["all", "live", "movie", "series"],
                                 state="readonly", width=10)
        type_combo.pack(side='left', padx=5)
        type_combo.bind('<<ComboboxSelected>>', lambda e: filter_callback())
        
        # Botón limpiar
        clear_btn = ttk.Button(filter_frame, text="Limpiar", 
                              command=lambda: self._clear_filters(search_var, type_var, filter_callback))
        clear_btn.pack(side='left', padx=5)
        
        filter_frame.search_var = search_var
        filter_frame.type_var = type_var
        filter_frame.search_entry = search_entry
        filter_frame.type_combo = type_combo
        filter_frame.clear_btn = clear_btn
        
        return filter_frame
    
    def _clear_filters(self, search_var, type_var, callback):
        """Limpia los filtros"""
        search_var.set("")
        type_var.set("all")
        callback()
    
    def create_progress_panel(self, parent, title="Progreso"):
        """Crea un panel de progreso con estadísticas"""
        progress_frame = ttk.LabelFrame(parent, text=title)
        
        # Estadísticas
        stats_frame = ttk.Frame(progress_frame)
        stats_frame.pack(fill='x', padx=5, pady=2)
        
        stats_label = ttk.Label(stats_frame, text="En cola: 0 | Activas: 0 | Completadas: 0 | Errores: 0")
        stats_label.pack(side='left')
        
        # Progreso general
        progress_detail_frame = ttk.Frame(progress_frame)
        progress_detail_frame.pack(fill='x', padx=5, pady=2)
        
        ttk.Label(progress_detail_frame, text="Progreso General:").pack(side='left')
        progress_bar = ttk.Progressbar(progress_detail_frame, mode='determinate', 
                                      style='Custom.Horizontal.TProgressbar')
        progress_bar.pack(side='left', fill='x', expand=True, padx=5)
        
        progress_label = ttk.Label(progress_detail_frame, text="0%")
        progress_label.pack(side='left')
        
        progress_frame.stats_label = stats_label
        progress_frame.progress_bar = progress_bar
        progress_frame.progress_label = progress_label
        
        return progress_frame
    
    def show_context_menu(self, event, menu_items):
        """Muestra un menú contextual"""
        context_menu = tk.Menu(self.root, tearoff=0)
        
        for item in menu_items:
            if item.get('separator', False):
                context_menu.add_separator()
            else:
                context_menu.add_command(
                    label=item['label'],
                    command=item['command'],
                    state=item.get('state', 'normal')
                )
        
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()
    
    def show_loading_dialog(self, parent, title="Procesando", message="Por favor espere..."):
        """Muestra un diálogo de carga"""
        dialog = tk.Toplevel(parent)
        dialog.title(title)
        dialog.transient(parent)
        dialog.grab_set()
        
        # Centrar diálogo
        dialog.geometry("300x100")
        dialog.resizable(False, False)
        
        # Contenido
        ttk.Label(dialog, text=message, font=('Arial', 10)).pack(pady=10)
        
        progress = ttk.Progressbar(dialog, mode='indeterminate')
        progress.pack(pady=10, padx=20, fill='x')
        progress.start()
        
        dialog.progress = progress
        dialog.update()
        
        return dialog
    
    def close_loading_dialog(self, dialog):
        """Cierra un diálogo de carga"""
        if dialog and dialog.winfo_exists():
            dialog.progress.stop()
            dialog.destroy()
    
    def show_info_dialog(self, title, message, dialog_type="info"):
        """Muestra un diálogo de información"""
        if dialog_type == "info":
            messagebox.showinfo(title, message)
        elif dialog_type == "warning":
            messagebox.showwarning(title, message)
        elif dialog_type == "error":
            messagebox.showerror(title, message)
        elif dialog_type == "question":
            return messagebox.askyesno(title, message)
    
    def ask_input(self, title, prompt, default="", password=False):
        """Solicita entrada de texto del usuario"""
        if password:
            # Para contraseñas, usar un diálogo personalizado
            return self._ask_password(title, prompt)
        else:
            return simpledialog.askstring(title, prompt, initialvalue=default)
    
    def _ask_password(self, title, prompt):
        """Diálogo personalizado para contraseñas"""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.geometry("300x120")
        dialog.resizable(False, False)
        
        # Centrar diálogo
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() - dialog.winfo_width()) // 2
        y = (dialog.winfo_screenheight() - dialog.winfo_height()) // 2
        dialog.geometry(f"+{x}+{y}")
        
        result = {"value": None}
        
        # Contenido
        ttk.Label(dialog, text=prompt).pack(pady=10)
        
        password_var = tk.StringVar()
        password_entry = ttk.Entry(dialog, textvariable=password_var, show="*", width=30)
        password_entry.pack(pady=5)
        password_entry.focus()
        
        # Botones
        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(pady=10)
        
        def ok_clicked():
            result["value"] = password_var.get()
            dialog.destroy()
        
        def cancel_clicked():
            result["value"] = None
            dialog.destroy()
        
        ttk.Button(btn_frame, text="OK", command=ok_clicked).pack(side='left', padx=5)
        ttk.Button(btn_frame, text="Cancelar", command=cancel_clicked).pack(side='left', padx=5)
        
        # Bind Enter y Escape
        password_entry.bind('<Return>', lambda e: ok_clicked())
        dialog.bind('<Escape>', lambda e: cancel_clicked())
        
        dialog.wait_window()
        return result["value"]
    
    def create_notebook_tabs(self, parent, tabs_config):
        """Crea un Notebook con pestañas
        tabs_config: lista de diccionarios con 'text' y 'frame_factory'
        """
        notebook = ttk.Notebook(parent)
        tabs = {}
        
        for tab_config in tabs_config:
            frame = ttk.Frame(notebook)
            notebook.add(frame, text=tab_config['text'])
            
            if 'frame_factory' in tab_config:
                tab_config['frame_factory'](frame)
            
            tabs[tab_config['text']] = frame
        
        notebook.tabs = tabs
        return notebook
    
    def create_toolbar(self, parent, buttons_config):
        """Crea una barra de herramientas"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(side='top', fill='x', padx=5, pady=2)
        
        for btn_config in buttons_config:
            if btn_config.get('separator', False):
                ttk.Separator(toolbar, orient='vertical').pack(side='left', fill='y', padx=5)
            else:
                btn = ttk.Button(toolbar,
                               text=btn_config['text'],
                               command=btn_config['command'])
                btn.pack(side='left', padx=2)
                
                if 'tooltip' in btn_config:
                    self.create_tooltip(btn, btn_config['tooltip'])
        
        return toolbar
    
    def create_tooltip(self, widget, text):
        """Crea un tooltip para un widget"""
        def show_tooltip(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            
            label = ttk.Label(tooltip, text=text, background="lightyellow",
                             relief="solid", borderwidth=1)
            label.pack()
            
            widget.tooltip = tooltip
        
        def hide_tooltip(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip
        
        widget.bind('<Enter>', show_tooltip)
        widget.bind('<Leave>', hide_tooltip)
    
    def async_operation(self, operation, callback=None, error_callback=None):
        """Ejecuta una operación de forma asíncrona"""
        def worker():
            try:
                result = operation()
                if callback:
                    self.root.after(0, lambda: callback(result))
            except Exception as e:
                if error_callback:
                    self.root.after(0, lambda: error_callback(e))
                else:
                    self.root.after(0, lambda: messagebox.showerror("Error", str(e)))
        
        thread = threading.Thread(target=worker, daemon=True)
        thread.start()
        return thread
    
    def update_tree_item(self, tree, item_id, values):
        """Actualiza un item del tree de forma segura"""
        try:
            if tree.exists(item_id):
                tree.item(item_id, values=values)
        except tk.TclError:
            pass  # Item ya no existe
    
    def clear_tree(self, tree):
        """Limpia un tree de forma segura"""
        try:
            for item in tree.get_children():
                tree.delete(item)
        except tk.TclError:
            pass
    
    def get_selected_tree_values(self, tree):
        """Obtiene los valores de los items seleccionados en un tree"""
        selected_items = []
        try:
            for selection in tree.selection():
                item = tree.item(selection)
                selected_items.append(item['values'])
        except tk.TclError:
            pass
        return selected_items
    
    def set_widget_state(self, widget, state):
        """Establece el estado de un widget de forma segura"""
        try:
            widget.config(state=state)
        except (tk.TclError, AttributeError):
            pass
    
    def bind_tree_events(self, tree, on_double_click=None, on_right_click=None, on_select=None):
        """Vincula eventos comunes a un tree"""
        if on_double_click:
            tree.bind('<Double-1>', on_double_click)
        
        if on_right_click:
            tree.bind('<Button-3>', on_right_click)
        
        if on_select:
            tree.bind('<<TreeviewSelect>>', on_select)
