#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnóstico SFTP - Script para probar y diagnosticar conexiones SFTP
"""

import paramiko
import socket
import sys
import traceback
from datetime import datetime

def test_sftp_connection():
    """Test de conexión SFTP con diagnóstico detallado"""
    print("🔍 DIAGNÓSTICO DE CONEXIÓN SFTP")
    print("=" * 50)
    
    # Solicitar datos de conexión
    print("Por favor, ingrese los datos de conexión SFTP:")
    host = input("Host/IP: ").strip()
    port = input("Puerto (22): ").strip() or "22"
    username = input("Usuario: ").strip()
    password = input("Contraseña: ").strip()
    
    if not all([host, username, password]):
        print("❌ Error: Debe completar todos los campos")
        return False
    
    try:
        port = int(port)
    except ValueError:
        print("❌ Error: Puerto debe ser un número")
        return False
    
    print(f"\n🔄 Intentando conectar a {username}@{host}:{port}")
    print(f"Fecha/Hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test 1: Conectividad básica
    print("\n1️⃣ Probando conectividad básica...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print("✅ Puerto accesible")
        else:
            print("❌ Puerto no accesible o host inalcanzable")
            return False
    except Exception as e:
        print(f"❌ Error de conectividad: {e}")
        return False
    
    # Test 2: Conexión SSH
    print("\n2️⃣ Probando conexión SSH...")
    ssh_client = None
    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        # Intentar conectar con configuración básica
        ssh_client.connect(
            hostname=host,
            port=port,
            username=username,
            password=password,
            timeout=15,
            compress=True,
            allow_agent=False,
            look_for_keys=False
        )
        print("✅ Conexión SSH exitosa")
        
    except paramiko.AuthenticationException:
        print("❌ Error de autenticación: Usuario o contraseña incorrectos")
        return False
    except paramiko.SSHException as e:
        print(f"❌ Error SSH: {e}")
        return False
    except socket.timeout:
        print("❌ Timeout de conexión SSH")
        return False
    except Exception as e:
        print(f"❌ Error SSH: {e}")
        traceback.print_exc()
        return False
    
    # Test 3: Conexión SFTP
    print("\n3️⃣ Probando conexión SFTP...")
    sftp_client = None
    try:
        sftp_client = ssh_client.open_sftp()
        print("✅ Conexión SFTP exitosa")
        
        # Test básico de operaciones
        print("\n4️⃣ Probando operaciones básicas...")
        
        # Listar directorio actual
        try:
            current_dir = sftp_client.getcwd() or "/"
            print(f"✅ Directorio actual: {current_dir}")
            
            files = sftp_client.listdir(current_dir)
            print(f"✅ Archivos listados: {len(files)} elementos")
            
            # Mostrar algunos archivos
            for i, file in enumerate(files[:5]):
                print(f"   - {file}")
            if len(files) > 5:
                print(f"   ... y {len(files)-5} más")
                
        except Exception as e:
            print(f"⚠️ Error listando directorio: {e}")
        
        # Test de información del servidor
        try:
            channel = sftp_client.get_channel()
            if channel:
                print(f"✅ Canal SFTP establecido")
                print(f"   - ID del canal: {channel.get_id()}")
            else:
                print("⚠️ No se pudo obtener información del canal")
        except Exception as e:
            print(f"⚠️ Error obteniendo info del canal: {e}")
            
    except Exception as e:
        print(f"❌ Error SFTP: {e}")
        traceback.print_exc()
        return False
    
    finally:
        # Limpiar conexiones
        if sftp_client:
            sftp_client.close()
        if ssh_client:
            ssh_client.close()
    
    print("\n✅ TODAS LAS PRUEBAS PASARON EXITOSAMENTE")
    print("La conexión SFTP funciona correctamente")
    return True

def check_paramiko_version():
    """Verifica la versión de paramiko"""
    print("📦 Paramiko instalado correctamente")
    
    # Verificar algoritmos disponibles
    try:
        transport = paramiko.Transport(("127.0.0.1", 22))
        print("🔐 Algoritmos de cifrado disponibles:")
        print(f"   - Cifrado: {transport.get_security_options().ciphers[:3]}...")
        print(f"   - Hash: {transport.get_security_options().digests[:3]}...")
        transport.close()
    except:
        print("🔐 Algoritmos de cifrado: No disponibles (normal en algunos sistemas)")

def main():
    print("🚀 M3U Manager - Diagnóstico SFTP")
    print("Este script ayuda a diagnosticar problemas de conexión SFTP")
    print()
    
    check_paramiko_version()
    print()
    
    # Ejecutar test de conexión
    success = test_sftp_connection()
    
    if success:
        print("\n🎉 ¡Conexión SFTP funcionando correctamente!")
        print("Si el problema persiste en la aplicación principal,")
        print("el problema podría estar en la interfaz de usuario.")
    else:
        print("\n🔧 POSIBLES SOLUCIONES:")
        print("1. Verificar que el host/IP sea correcto")
        print("2. Comprobar que el puerto SSH esté abierto (por defecto 22)")
        print("3. Verificar usuario y contraseña")
        print("4. Comprobar configuración del firewall")
        print("5. Verificar que el servidor SSH esté ejecutándose")
        print("6. Intentar con otro cliente SFTP (WinSCP, FileZilla)")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Operación cancelada por el usuario")
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        traceback.print_exc()
