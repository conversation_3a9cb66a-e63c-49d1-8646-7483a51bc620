#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verificar si el archivo fue descargado correctamente
"""

import paramiko
import time

# --- Configuración ---
HOSTNAME = "***************"
PORT = 22
USERNAME = "root"
PASSWORD = "5dhCkm5Dz1BpWgxZpdBisrOVo"

def verify_download():
    """Verifica si el archivo descargado existe en el servidor"""
    
    print("🔍 VERIFICANDO DESCARGA EN SERVIDOR...")
    
    client = None
    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        client.connect(
            hostname=HOSTNAME,
            port=PORT,
            username=USERNAME,
            password=PASSWORD,
            timeout=15,
            allow_agent=False,
            look_for_keys=False,
            compress=True
        )
        
        print("✅ Conectado al servidor")
        
        # Verificar si el archivo existe
        test_file = "/tmp/test_download_favicon.ico"
        
        # 1. Verificar existencia
        check_cmd = f'[ -f "{test_file}" ] && echo "EXISTS" || echo "NOT_EXISTS"'
        stdin, stdout, stderr = client.exec_command(check_cmd)
        exists = stdout.read().decode().strip() == "EXISTS"
        
        if exists:
            print(f"✅ Archivo {test_file} EXISTE en el servidor")
            
            # 2. Mostrar información del archivo
            info_cmd = f'ls -lh "{test_file}"'
            stdin, stdout, stderr = client.exec_command(info_cmd)
            file_info = stdout.read().decode().strip()
            print(f"📄 Información: {file_info}")
            
            # 3. Verificar tipo de archivo
            type_cmd = f'file "{test_file}"'
            stdin, stdout, stderr = client.exec_command(type_cmd)
            file_type = stdout.read().decode().strip()
            print(f"🔍 Tipo: {file_type}")
            
            # 4. Mostrar hash MD5
            md5_cmd = f'md5sum "{test_file}"'
            stdin, stdout, stderr = client.exec_command(md5_cmd)
            md5_hash = stdout.read().decode().strip()
            print(f"🔑 MD5: {md5_hash}")
            
            print("\n🎉 ¡LA DESCARGA FUE EXITOSA!")
            
        else:
            print(f"❌ Archivo {test_file} NO existe en el servidor")
            
            # Mostrar contenido de /tmp para debug
            ls_cmd = 'ls -la /tmp/test_download* 2>/dev/null || echo "No files found"'
            stdin, stdout, stderr = client.exec_command(ls_cmd)
            tmp_content = stdout.read().decode().strip()
            print(f"🗂️ Archivos en /tmp: {tmp_content}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    verify_download()
