#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba para mostrar el progreso de descarga en tiempo real
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import random

def create_progress_demo():
    """Crea una demo del progreso de descarga"""
    
    root = tk.Tk()
    root.title("Demo de Progreso de Descarga - M3U Manager")
    root.geometry("800x500")
    
    # Simular tema oscuro básico
    root.configure(bg='#1e1e1e')
    
    # Frame principal
    main_frame = ttk.Frame(root)
    main_frame.pack(fill='both', expand=True, padx=10, pady=10)
    
    # Título
    title_label = ttk.Label(main_frame, text="🎬 Demo de Progreso de Descarga", 
                           font=('Arial', 16, 'bold'))
    title_label.pack(pady=10)
    
    # Información de descarga
    info_frame = ttk.LabelFrame(main_frame, text="Descarga Activa")
    info_frame.pack(fill='x', pady=10)
    
    filename_var = tk.StringVar(value="📡 Los Caballeros del Zodiaco - Saint Seiya 1986 S01 E114 699309.mkv")
    status_var = tk.StringVar(value="Descargando")
    progress_var = tk.StringVar(value="0%")
    speed_var = tk.StringVar(value="Iniciando...")
    eta_var = tk.StringVar(value="Calculando...")
    size_var = tk.StringVar(value="1.2 GB")
    
    ttk.Label(info_frame, text="Archivo:").grid(row=0, column=0, sticky='w', padx=5, pady=2)
    ttk.Label(info_frame, textvariable=filename_var).grid(row=0, column=1, sticky='w', padx=5, pady=2)
    
    ttk.Label(info_frame, text="Estado:").grid(row=1, column=0, sticky='w', padx=5, pady=2)
    ttk.Label(info_frame, textvariable=status_var).grid(row=1, column=1, sticky='w', padx=5, pady=2)
    
    ttk.Label(info_frame, text="Velocidad:").grid(row=2, column=0, sticky='w', padx=5, pady=2)
    ttk.Label(info_frame, textvariable=speed_var).grid(row=2, column=1, sticky='w', padx=5, pady=2)
    
    ttk.Label(info_frame, text="ETA:").grid(row=3, column=0, sticky='w', padx=5, pady=2)
    ttk.Label(info_frame, textvariable=eta_var).grid(row=3, column=1, sticky='w', padx=5, pady=2)
    
    # Barra de progreso
    progress_frame = ttk.LabelFrame(main_frame, text="Progreso de Descarga")
    progress_frame.pack(fill='x', pady=10)
    
    progress_bar = ttk.Progressbar(progress_frame, mode='determinate', length=600)
    progress_bar.pack(padx=10, pady=10)
    
    progress_label = ttk.Label(progress_frame, textvariable=progress_var, font=('Arial', 12, 'bold'))
    progress_label.pack(pady=(0, 10))
    
    # Simulación de log
    log_frame = ttk.LabelFrame(main_frame, text="Log de wget (Simulado)")
    log_frame.pack(fill='both', expand=True, pady=10)
    
    log_text = tk.Text(log_frame, height=10, font=('Consolas', 9))
    log_scrollbar = ttk.Scrollbar(log_frame, orient='vertical', command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side='left', fill='both', expand=True, padx=5, pady=5)
    log_scrollbar.pack(side='right', fill='y')
    
    # Botones
    btn_frame = ttk.Frame(main_frame)
    btn_frame.pack(fill='x', pady=10)
    
    def start_demo():
        """Inicia la simulación de descarga"""
        def simulate_download():
            current_progress = 0
            speeds = [1200, 1500, 1800, 2100, 1900, 1650, 2200, 1750]  # KB/s
            
            log_text.insert(tk.END, "Iniciando descarga con wget...\n")
            log_text.insert(tk.END, f"wget --continue --tries=3 --user-agent=\"XCIPTV\" \\\n")
            log_text.insert(tk.END, f"     --output-document=\"{filename_var.get().replace('📡 ', '')}\" \\\n")
            log_text.insert(tk.END, f"     \"https://example.com/video.mkv\"\n\n")
            
            while current_progress < 100:
                # Simular incremento de progreso
                increment = random.uniform(0.5, 3.0)
                current_progress = min(100, current_progress + increment)
                
                # Actualizar progreso
                progress_var.set(f"{current_progress:.1f}%")
                progress_bar['value'] = current_progress
                
                # Simular velocidad variable
                current_speed = random.choice(speeds) + random.randint(-200, 200)
                speed_var.set(f"{current_speed} KB/s")
                
                # Calcular ETA aproximado
                if current_progress > 0:
                    remaining_time = (100 - current_progress) / (current_progress / 10)  # Aproximado
                    if remaining_time < 60:
                        eta_var.set(f"{int(remaining_time)}s")
                    else:
                        eta_var.set(f"{int(remaining_time/60)}m {int(remaining_time%60)}s")
                
                # Agregar línea al log
                if current_progress % 5 < 1:  # Cada 5%
                    log_text.insert(tk.END, f"Progress: {current_progress:.1f}% - Speed: {current_speed} KB/s\n")
                    log_text.see(tk.END)
                
                time.sleep(0.1)  # Actualización cada 100ms
                
                if not root.winfo_exists():
                    break
            
            # Descarga completada
            status_var.set("Completado")
            speed_var.set("--")
            eta_var.set("--")
            progress_var.set("100%")
            log_text.insert(tk.END, "\n¡Descarga completada exitosamente!\n")
            log_text.insert(tk.END, f"Archivo guardado: {filename_var.get().replace('📡 ', '')}\n")
            log_text.see(tk.END)
        
        # Ejecutar simulación en hilo separado
        threading.Thread(target=simulate_download, daemon=True).start()
    
    def reset_demo():
        """Reinicia la demo"""
        progress_var.set("0%")
        progress_bar['value'] = 0
        status_var.set("En cola")
        speed_var.set("--")
        eta_var.set("--")
        log_text.delete(1.0, tk.END)
    
    ttk.Button(btn_frame, text="🚀 Iniciar Demo", command=start_demo).pack(side='left', padx=5)
    ttk.Button(btn_frame, text="🔄 Reiniciar", command=reset_demo).pack(side='left', padx=5)
    ttk.Button(btn_frame, text="❌ Cerrar", command=root.destroy).pack(side='right', padx=5)
    
    # Instrucciones
    instructions = ttk.Label(main_frame, 
                            text="Esta es una demostración de cómo se ve el progreso de descarga en tiempo real.\n"
                                 "En la aplicación real, los datos vienen del log de wget ejecutándose en el servidor.",
                            font=('Arial', 9), justify='center')
    instructions.pack(pady=10)
    
    return root

if __name__ == "__main__":
    demo_window = create_progress_demo()
    demo_window.mainloop()
