#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de navegación SFTP - Valida la robustez de la navegación en carpetas
"""

import sys
import os
import time
from sftp_manager import SFTPManager

# --- Configuración ---
HOSTNAME = "***************"
PORT = 22
USERNAME = "root"
PASSWORD = "5dhCkm5Dz1BpWgxZpdBisrOVo"
TIMEOUT = 15

def test_navigation():
    """Prueba la navegación por diferentes directorios"""
    
    print("\n🚀 INICIANDO PRUEBA DE NAVEGACIÓN SFTP")
    print("="*50)
    
    sftp = SFTPManager()
    
    try:
        # Conectar
        print(f"Conectando a {USERNAME}@{HOSTNAME}:{PORT}...")
        sftp.connect_direct(HOSTNAME, PORT, USERNAME, PASSWORD, TIMEOUT)
        
        # Directorios a probar
        directories = [
            "/",              # Raíz
            "/root",          # Home
            "/etc",           # Etc
            "/tmp",           # Temp
            "/var/log",       # Logs
            "/non_existent",  # No existe
            "/etc/ssh",       # Subdirectorio
            "/root"           # Volver a un directorio válido
        ]
        
        for directory in directories:
            print(f"\n📂 Navegando a: {directory}")
            
            # Verificar si existe
            exists = sftp.verify_directory_exists(directory)
            print(f"¿Directorio existe? {'✅ SÍ' if exists else '❌ NO'}")
            
            if exists:
                # Listar contenido
                try:
                    files = sftp.list_directory(directory)
                    print(f"✅ Directorio listado correctamente, {len(files)} elementos encontrados")
                    
                    # Mostrar primeros 5 elementos
                    for i, file in enumerate(files[:5]):
                        print(f"  {file['icon']} {file['name']} ({file['type']}, {file['size']})")
                    
                    if len(files) > 5:
                        print(f"  ... y {len(files) - 5} más")
                except Exception as e:
                    print(f"❌ Error listando directorio: {e}")
            else:
                # Intentar listar de todos modos para probar robustez
                try:
                    print("⚠️ Intentando listar directorio no existente (debería manejar el error)")
                    files = sftp.list_directory(directory)
                    print(f"⚠️ INESPERADO: Se listó un directorio que no debería existir: {len(files)} elementos")
                except Exception as e:
                    print(f"✅ Error esperado al listar directorio no existente: {e}")
            
            print("-"*40)
        
        print("\n🔍 PRUEBAS ADICIONALES DE NAVEGACIÓN")
        print("="*50)
        
        # Prueba de navegación a subdirectorios
        base_dir = "/etc"
        if sftp.verify_directory_exists(base_dir):
            print(f"\n📂 Listando subdirectorios de {base_dir}")
            files = sftp.list_directory(base_dir)
            
            # Encontrar subdirectorios
            subdirs = [f for f in files if f['type'] == 'directory'][:3]  # Primeros 3 subdirectorios
            
            for subdir in subdirs:
                subdir_path = f"{base_dir}/{subdir['name']}"
                print(f"\n📂 Navegando a subdirectorio: {subdir_path}")
                
                if sftp.verify_directory_exists(subdir_path):
                    try:
                        subdir_files = sftp.list_directory(subdir_path)
                        print(f"✅ Listado correcto: {len(subdir_files)} elementos")
                    except Exception as e:
                        print(f"❌ Error listando subdirectorio: {e}")
                else:
                    print(f"❌ No se pudo acceder al subdirectorio (¿permisos?)")
        
        print("\n✅ PRUEBA DE NAVEGACIÓN COMPLETADA")
        
    except Exception as e:
        print(f"\n❌ ERROR EN PRUEBA: {e}")
    finally:
        # Desconectar
        sftp.disconnect()
        print("\n👋 Conexión cerrada")
        print("="*50)

if __name__ == "__main__":
    test_navigation()
