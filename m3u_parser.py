#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
M3U Parser - Módulo para análisis de listas M3U y protocolo Xtream Codes
"""

import requests
import re
import json
from urllib.parse import urlparse, parse_qs
import logging

class M3UParser:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def parse_file(self, file_path):
        """Analiza un archivo M3U local"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            return self._parse_m3u_content(content)
        except Exception as e:
            raise Exception(f"Error leyendo archivo M3U: {e}")
    
    def parse_url(self, url):
        """Analiza una URL M3U"""
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return self._parse_m3u_content(response.text)
        except Exception as e:
            raise Exception(f"Error descargando M3U desde URL: {e}")
    
    def parse_xtream_codes(self, server_url, username, password):
        """Conecta y obtiene contenido usando el protocolo Xtream Codes"""
        try:
            # Limpiar URL del servidor
            if not server_url.startswith(('http://', 'https://')):
                server_url = 'http://' + server_url
            
            base_url = server_url.rstrip('/')
            
            # Obtener información del servidor
            auth_params = {
                'username': username,
                'password': password
            }
            
            # Test de autenticación
            auth_url = f"{base_url}/player_api.php"
            auth_response = self.session.get(auth_url, params=auth_params, timeout=15)
            auth_response.raise_for_status()
            
            try:
                server_info = auth_response.json()
                if 'user_info' not in server_info:
                    raise Exception("Credenciales inválidas")
            except json.JSONDecodeError:
                raise Exception("Respuesta inválida del servidor")
            
            content = []
            
            # Obtener canales en vivo
            try:
                live_params = auth_params.copy()
                live_params['action'] = 'get_live_categories'
                live_categories_response = self.session.get(auth_url, params=live_params, timeout=15)
                live_categories = live_categories_response.json() if live_categories_response.status_code == 200 else []
                
                live_params['action'] = 'get_live_streams'
                live_streams_response = self.session.get(auth_url, params=live_params, timeout=15)
                live_streams = live_streams_response.json() if live_streams_response.status_code == 200 else []
                
                # Crear diccionario de categorías
                categories_dict = {cat['category_id']: cat['category_name'] for cat in live_categories}
                
                for stream in live_streams:
                    if isinstance(stream, dict):
                        category_name = categories_dict.get(stream.get('category_id', ''), 'Sin categoría')
                        stream_url = f"{base_url}/live/{username}/{password}/{stream.get('stream_id', '')}.ts"
                        
                        content.append({
                            'name': stream.get('name', 'Canal sin nombre'),
                            'group': f"Live - {category_name}",
                            'type': 'live',
                            'duration': '-1',
                            'url': stream_url,
                            'stream_id': stream.get('stream_id', ''),
                            'category_id': stream.get('category_id', ''),
                            'logo': stream.get('stream_icon', ''),
                            'epg_channel_id': stream.get('epg_channel_id', ''),
                            'added': stream.get('added', ''),
                            'is_adult': stream.get('is_adult', '0')
                        })
            except Exception as e:
                print(f"Error obteniendo canales en vivo: {e}")
            
            # Obtener películas
            try:
                movie_params = auth_params.copy()
                movie_params['action'] = 'get_vod_categories'
                movie_categories_response = self.session.get(auth_url, params=movie_params, timeout=15)
                movie_categories = movie_categories_response.json() if movie_categories_response.status_code == 200 else []
                
                movie_params['action'] = 'get_vod_streams'
                movie_streams_response = self.session.get(auth_url, params=movie_params, timeout=15)
                movie_streams = movie_streams_response.json() if movie_streams_response.status_code == 200 else []
                
                # Crear diccionario de categorías de películas
                movie_categories_dict = {cat['category_id']: cat['category_name'] for cat in movie_categories}
                
                for movie in movie_streams:
                    if isinstance(movie, dict):
                        category_name = movie_categories_dict.get(movie.get('category_id', ''), 'Sin categoría')
                        movie_url = f"{base_url}/movie/{username}/{password}/{movie.get('stream_id', '')}.{movie.get('container_extension', 'mp4')}"
                        
                        content.append({
                            'name': movie.get('name', 'Película sin nombre'),
                            'group': f"Movies - {category_name}",
                            'type': 'movie',
                            'duration': movie.get('duration', '0'),
                            'url': movie_url,
                            'stream_id': movie.get('stream_id', ''),
                            'category_id': movie.get('category_id', ''),
                            'logo': movie.get('stream_icon', ''),
                            'rating': movie.get('rating', ''),
                            'year': movie.get('year', ''),
                            'genre': movie.get('genre', ''),
                            'plot': movie.get('plot', ''),
                            'director': movie.get('director', ''),
                            'cast': movie.get('cast', ''),
                            'container_extension': movie.get('container_extension', 'mp4'),
                            'added': movie.get('added', '')
                        })
            except Exception as e:
                print(f"Error obteniendo películas: {e}")
            
            # Obtener series
            try:
                series_params = auth_params.copy()
                series_params['action'] = 'get_series_categories'
                series_categories_response = self.session.get(auth_url, params=series_params, timeout=15)
                series_categories = series_categories_response.json() if series_categories_response.status_code == 200 else []
                
                series_params['action'] = 'get_series'
                series_response = self.session.get(auth_url, params=series_params, timeout=15)
                series_list = series_response.json() if series_response.status_code == 200 else []
                
                # Crear diccionario de categorías de series
                series_categories_dict = {cat['category_id']: cat['category_name'] for cat in series_categories}
                
                for series in series_list:
                    if isinstance(series, dict):
                        category_name = series_categories_dict.get(series.get('category_id', ''), 'Sin categoría')
                        series_id = series.get('series_id', '')
                        
                        # Obtener información detallada de la serie
                        try:
                            series_info_params = auth_params.copy()
                            series_info_params['action'] = 'get_series_info'
                            series_info_params['series_id'] = series_id
                            series_info_response = self.session.get(auth_url, params=series_info_params, timeout=10)
                            series_info = series_info_response.json() if series_info_response.status_code == 200 else {}
                            
                            # Procesar episodios
                            episodes = series_info.get('episodes', {})
                            for season_num, season_episodes in episodes.items():
                                if isinstance(season_episodes, list):
                                    for episode in season_episodes:
                                        if isinstance(episode, dict):
                                            episode_url = f"{base_url}/series/{username}/{password}/{episode.get('id', '')}.{episode.get('container_extension', 'mp4')}"
                                            
                                            content.append({
                                                'name': f"{series.get('name', 'Serie')} - T{season_num}E{episode.get('episode_num', '?')} - {episode.get('title', 'Episodio')}",
                                                'group': f"Series - {category_name}",
                                                'type': 'series',
                                                'duration': episode.get('duration', '0'),
                                                'url': episode_url,
                                                'stream_id': episode.get('id', ''),
                                                'series_id': series_id,
                                                'season': season_num,
                                                'episode': episode.get('episode_num', ''),
                                                'category_id': series.get('category_id', ''),
                                                'logo': series.get('cover', ''),
                                                'rating': episode.get('rating', ''),
                                                'plot': episode.get('plot', ''),
                                                'container_extension': episode.get('container_extension', 'mp4'),
                                                'added': episode.get('added', ''),
                                                'season_name': f"Temporada {season_num}",
                                                'series_name': series.get('name', 'Serie sin nombre')
                                            })
                        except Exception as e:
                            print(f"Error obteniendo episodios de serie {series_id}: {e}")
                            # Agregar entrada de serie sin episodios
                            content.append({
                                'name': series.get('name', 'Serie sin nombre'),
                                'group': f"Series - {category_name}",
                                'type': 'series',
                                'duration': '0',
                                'url': f"{base_url}/series/{username}/{password}/{series_id}/",
                                'stream_id': series_id,
                                'category_id': series.get('category_id', ''),
                                'logo': series.get('cover', ''),
                                'rating': series.get('rating', ''),
                                'plot': series.get('plot', ''),
                                'year': series.get('year', ''),
                                'genre': series.get('genre', ''),
                                'added': series.get('last_modified', ''),
                                'series_name': series.get('name', 'Serie sin nombre')
                            })
            except Exception as e:
                print(f"Error obteniendo series: {e}")
            
            return content
            
        except Exception as e:
            raise Exception(f"Error conectando con Xtream Codes: {e}")
    
    def _parse_m3u_content(self, content):
        """Analiza el contenido de un archivo M3U"""
        lines = content.split('\n')
        items = []
        current_item = {}
        
        for line in lines:
            line = line.strip()
            
            if line.startswith('#EXTM3U'):
                continue
            elif line.startswith('#EXTINF:'):
                # Parsear línea EXTINF
                current_item = self._parse_extinf_line(line)
            elif line and not line.startswith('#'):
                # URL del stream
                if current_item:
                    current_item['url'] = line
                    
                    # Detectar tipo de contenido basado en la URL y metadatos
                    current_item['type'] = self._detect_content_type(current_item)
                    
                    items.append(current_item)
                    current_item = {}
        
        return items
    
    def _parse_extinf_line(self, line):
        """Analiza una línea EXTINF"""
        item = {
            'name': '',
            'group': '',
            'logo': '',
            'duration': '0'
        }
        
        # Extraer duración
        duration_match = re.search(r'#EXTINF:([\d\.-]+)', line)
        if duration_match:
            item['duration'] = duration_match.group(1)
        
        # Extraer atributos
        # tvg-id, tvg-name, tvg-logo, group-title, etc.
        attributes = {
            'tvg-id': r'tvg-id="([^"]*)"',
            'tvg-name': r'tvg-name="([^"]*)"',
            'tvg-logo': r'tvg-logo="([^"]*)"',
            'group-title': r'group-title="([^"]*)"',
            'tvg-chno': r'tvg-chno="([^"]*)"',
            'tvg-shift': r'tvg-shift="([^"]*)"'
        }
        
        for attr, pattern in attributes.items():
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                if attr == 'group-title':
                    item['group'] = match.group(1)
                elif attr == 'tvg-logo':
                    item['logo'] = match.group(1)
                elif attr == 'tvg-id':
                    item['tvg_id'] = match.group(1)
                elif attr == 'tvg-name':
                    item['tvg_name'] = match.group(1)
                elif attr == 'tvg-chno':
                    item['channel_number'] = match.group(1)
                elif attr == 'tvg-shift':
                    item['time_shift'] = match.group(1)
        
        # Extraer nombre del canal (después de la coma)
        name_match = re.search(r',\s*(.+)$', line)
        if name_match:
            item['name'] = name_match.group(1).strip()
        
        return item
    
    def _detect_content_type(self, item):
        """Detecta el tipo de contenido basado en metadatos y URL"""
        name = item.get('name', '').lower()
        group = item.get('group', '').lower()
        url = item.get('url', '').lower()
        
        # Detectar series
        series_keywords = ['serie', 'series', 'season', 'temporada', 'episode', 'episodio', 'cap', 'capitulo']
        if any(keyword in name for keyword in series_keywords) or any(keyword in group for keyword in series_keywords):
            return 'series'
        
        # Detectar películas
        movie_keywords = ['movie', 'pelicula', 'film', 'cinema']
        movie_extensions = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm']
        if (any(keyword in name for keyword in movie_keywords) or 
            any(keyword in group for keyword in movie_keywords) or
            any(ext in url for ext in movie_extensions)):
            return 'movie'
        
        # Detectar transmisiones en vivo
        live_keywords = ['live', 'en vivo', 'directo', 'tv', 'channel', 'canal']
        live_extensions = ['.ts', '.m3u8']
        if (any(keyword in name for keyword in live_keywords) or 
            any(keyword in group for keyword in live_keywords) or
            any(ext in url for ext in live_extensions)):
            return 'live'
        
        # Por defecto, asumir que es contenido en vivo
        return 'live'
    
    def get_series_episodes(self, server_url, username, password, series_id):
        """Obtiene todos los episodios de una serie específica"""
        try:
            base_url = server_url.rstrip('/')
            auth_url = f"{base_url}/player_api.php"
            
            params = {
                'username': username,
                'password': password,
                'action': 'get_series_info',
                'series_id': series_id
            }
            
            response = self.session.get(auth_url, params=params, timeout=15)
            response.raise_for_status()
            series_info = response.json()
            
            episodes = []
            episodes_data = series_info.get('episodes', {})
            
            for season_num, season_episodes in episodes_data.items():
                if isinstance(season_episodes, list):
                    for episode in season_episodes:
                        if isinstance(episode, dict):
                            episode_url = f"{base_url}/series/{username}/{password}/{episode.get('id', '')}.{episode.get('container_extension', 'mp4')}"
                            
                            episodes.append({
                                'id': episode.get('id', ''),
                                'season': season_num,
                                'episode': episode.get('episode_num', ''),
                                'title': episode.get('title', 'Episodio'),
                                'duration': episode.get('duration', '0'),
                                'url': episode_url,
                                'container_extension': episode.get('container_extension', 'mp4'),
                                'rating': episode.get('rating', ''),
                                'plot': episode.get('plot', ''),
                                'added': episode.get('added', '')
                            })
            
            return episodes
            
        except Exception as e:
            raise Exception(f"Error obteniendo episodios de la serie: {e}")
    
    def get_movie_info(self, server_url, username, password, vod_id):
        """Obtiene información detallada de una película"""
        try:
            base_url = server_url.rstrip('/')
            auth_url = f"{base_url}/player_api.php"
            
            params = {
                'username': username,
                'password': password,
                'action': 'get_vod_info',
                'vod_id': vod_id
            }
            
            response = self.session.get(auth_url, params=params, timeout=15)
            response.raise_for_status()
            movie_info = response.json()
            
            return movie_info
            
        except Exception as e:
            raise Exception(f"Error obteniendo información de película: {e}")
    
    def get_live_stream_info(self, server_url, username, password, stream_id):
        """Obtiene información detallada de un canal en vivo"""
        try:
            base_url = server_url.rstrip('/')
            auth_url = f"{base_url}/player_api.php"
            
            params = {
                'username': username,
                'password': password,
                'action': 'get_live_stream_info',
                'stream_id': stream_id
            }
            
            response = self.session.get(auth_url, params=params, timeout=15)
            response.raise_for_status()
            stream_info = response.json()
            
            return stream_info
            
        except Exception as e:
            raise Exception(f"Error obteniendo información del canal: {e}")
    
    def search_content(self, content_list, search_term, content_type=None):
        """Busca contenido en la lista basado en término y tipo"""
        search_term = search_term.lower()
        results = []
        
        for item in content_list:
            # Filtrar por tipo si se especifica
            if content_type and item.get('type', '') != content_type:
                continue
            
            # Buscar en nombre y grupo
            if (search_term in item.get('name', '').lower() or 
                search_term in item.get('group', '').lower()):
                results.append(item)
        
        return results
    
    def filter_by_group(self, content_list, group_name):
        """Filtra contenido por nombre de grupo"""
        group_name = group_name.lower()
        return [item for item in content_list if group_name in item.get('group', '').lower()]
    
    def get_groups(self, content_list):
        """Obtiene lista única de grupos"""
        groups = set()
        for item in content_list:
            group = item.get('group', 'Sin grupo')
            if group:
                groups.add(group)
        return sorted(list(groups))
    
    def get_content_stats(self, content_list):
        """Obtiene estadísticas del contenido"""
        stats = {
            'total': len(content_list),
            'live': 0,
            'movies': 0,
            'series': 0,
            'groups': len(self.get_groups(content_list))
        }
        
        for item in content_list:
            content_type = item.get('type', 'live')
            if content_type == 'live':
                stats['live'] += 1
            elif content_type == 'movie':
                stats['movies'] += 1
            elif content_type == 'series':
                stats['series'] += 1
        
        return stats
