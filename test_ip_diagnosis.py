#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Diagnóstico específico para IP ***************
Verifica conectividad, puerto SSH, y respuesta del servidor
"""

import socket
import time
import sys
import threading
from sftp_manager import SFTPManager

TARGET_IP = "***************"
SSH_PORT = 22

def test_basic_connectivity():
    """Test básico de conectividad TCP"""
    print(f"🔍 DIAGNÓSTICO DE IP: {TARGET_IP}")
    print("=" * 50)
    
    print(f"\n1️⃣ Test de conectividad TCP al puerto {SSH_PORT}...")
    
    start_time = time.time()
    sock = None
    try:
        # Crear socket con timeout
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)  # 10 segundos timeout
        
        # Intentar conectar
        result = sock.connect_ex((TARGET_IP, SSH_PORT))
        elapsed = time.time() - start_time
        
        if result == 0:
            print(f"✅ Puerto {SSH_PORT} ABIERTO en {elapsed:.2f}s")
            
            # Intentar leer banner SSH
            try:
                sock.settimeout(5)
                banner = sock.recv(1024).decode('utf-8', errors='ignore').strip()
                if banner:
                    print(f"✅ Banner SSH recibido: {banner}")
                else:
                    print("⚠️  No se recibió banner SSH")
            except Exception as e:
                print(f"⚠️  Error leyendo banner: {e}")
                
        else:
            print(f"❌ Puerto {SSH_PORT} CERRADO o BLOQUEADO (error {result}) en {elapsed:.2f}s")
            
    except socket.timeout:
        elapsed = time.time() - start_time
        print(f"❌ TIMEOUT después de {elapsed:.2f}s - Posible bloqueo de firewall")
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ Error de conectividad en {elapsed:.2f}s: {e}")
    finally:
        if sock:
            try:
                sock.close()
            except:
                pass

def test_ping_equivalent():
    """Test equivalente a ping usando socket"""
    print(f"\n2️⃣ Test de resolución DNS y conectividad básica...")
    
    try:
        # Resolver DNS
        start_time = time.time()
        resolved_ip = socket.gethostbyname(TARGET_IP)
        dns_time = time.time() - start_time
        
        if resolved_ip == TARGET_IP:
            print(f"✅ IP válida: {TARGET_IP} (resolución: {dns_time:.3f}s)")
        else:
            print(f"🔄 DNS resolvió a: {resolved_ip} (tiempo: {dns_time:.3f}s)")
            
    except socket.gaierror as e:
        print(f"❌ Error de DNS: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

def test_multiple_ports():
    """Test múltiples puertos comunes"""
    print(f"\n3️⃣ Test de puertos comunes en {TARGET_IP}...")
    
    common_ports = [22, 2222, 21, 80, 443, 8080]
    
    for port in common_ports:
        sock = None
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            
            start_time = time.time()
            result = sock.connect_ex((TARGET_IP, port))
            elapsed = time.time() - start_time
            
            if result == 0:
                print(f"✅ Puerto {port} ABIERTO ({elapsed:.2f}s)")
                
                # Si es puerto SSH, intentar leer banner
                if port in [22, 2222]:
                    try:
                        sock.settimeout(2)
                        banner = sock.recv(512).decode('utf-8', errors='ignore').strip()
                        if banner:
                            print(f"   Banner: {banner}")
                    except:
                        pass
            else:
                print(f"❌ Puerto {port} cerrado ({elapsed:.2f}s)")
                
        except Exception as e:
            print(f"❌ Puerto {port} error: {e}")
        finally:
            if sock:
                try:
                    sock.close()
                except:
                    pass

def test_sftp_connection():
    """Test conexión SFTP con credenciales reales"""
    print(f"\n4️⃣ Test de conexión SFTP a {TARGET_IP}...")
    
    # Credenciales reales del usuario + algunas comunes de fallback
    test_credentials = [
        ("root", "5dhCkm5Dz1BpWgxZpdBisrOVo"),  # Credenciales reales
        ("root", ""),
        ("admin", "admin"),
        ("user", "user"),
    ]
    
    for username, password in test_credentials:
        print(f"\n   Probando {username}:{password or '(sin password)'}...")
        
        sftp = SFTPManager()
        start_time = time.time()
        
        try:
            result = sftp.connect(TARGET_IP, SSH_PORT, username, password, timeout=8)
            elapsed = time.time() - start_time
            
            if result:
                print(f"   ✅ ¡CONECTADO! con {username}:{password} en {elapsed:.2f}s")
                
                # Test operación básica
                try:
                    files = sftp.list_directory("/")
                    print(f"   ✅ Listado exitoso: {len(files)} archivos")
                except Exception as e:
                    print(f"   ⚠️  Error en listado: {e}")
                
                sftp.disconnect()
                return True
            else:
                print(f"   ❌ Falló autenticación en {elapsed:.2f}s")
                
        except Exception as e:
            elapsed = time.time() - start_time
            error_msg = str(e)
            
            if "Authentication failed" in error_msg:
                print(f"   ❌ Autenticación rechazada ({elapsed:.2f}s)")
            elif "Connection refused" in error_msg:
                print(f"   ❌ Conexión rechazada ({elapsed:.2f}s)")
            elif "timed out" in error_msg or "timeout" in error_msg.lower():
                print(f"   ❌ Timeout ({elapsed:.2f}s)")
            else:
                print(f"   ❌ Error: {error_msg[:50]}... ({elapsed:.2f}s)")
        finally:
            sftp.disconnect()
    
    return False

def test_telnet_equivalent():
    """Test equivalente a telnet"""
    print(f"\n5️⃣ Test tipo telnet a {TARGET_IP}:{SSH_PORT}...")
    
    sock = None
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(15)
        
        print(f"   Conectando...")
        start_time = time.time()
        
        sock.connect((TARGET_IP, SSH_PORT))
        elapsed = time.time() - start_time
        
        print(f"   ✅ Conectado en {elapsed:.2f}s")
        
        # Esperar y leer respuesta inicial
        sock.settimeout(5)
        data = sock.recv(1024)
        
        if data:
            response = data.decode('utf-8', errors='ignore').strip()
            print(f"   📨 Respuesta del servidor:")
            print(f"   {response}")
            
            # Analizar el banner SSH
            if response.startswith("SSH-"):
                print(f"   ✅ Servidor SSH detectado")
                version_info = response.split(' ')[0] if ' ' in response else response
                print(f"   📋 Versión SSH: {version_info}")
            else:
                print(f"   ⚠️  Respuesta no parece ser SSH estándar")
        else:
            print(f"   ⚠️  No se recibió respuesta del servidor")
            
    except socket.timeout:
        print(f"   ❌ Timeout - El servidor no responde")
    except ConnectionRefusedError:
        print(f"   ❌ Conexión rechazada - Puerto cerrado o bloqueado")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    finally:
        if sock:
            try:
                sock.close()
            except:
                pass

if __name__ == "__main__":
    print("🚀 DIAGNÓSTICO COMPLETO DE CONECTIVIDAD")
    print(f"Objetivo: Verificar por qué {TARGET_IP} no responde por SSH/SFTP")
    print()
    
    total_start = time.time()
    
    try:
        # Ejecutar todos los tests
        dns_ok = test_ping_equivalent()
        
        if dns_ok:
            test_basic_connectivity()
            test_multiple_ports()
            test_telnet_equivalent()
            success = test_sftp_connection()
            
            total_elapsed = time.time() - total_start
            
            print(f"\n" + "=" * 50)
            print(f"🏁 DIAGNÓSTICO COMPLETADO EN {total_elapsed:.2f}s")
            
            if success:
                print("✅ Se logró conectar con alguna credencial")
            else:
                print("❌ No se pudo conectar con ninguna credencial")
                print("\n🔧 POSIBLES CAUSAS:")
                print("- El servidor SSH no está activo")
                print("- Firewall bloqueando conexiones")
                print("- SSH configurado en puerto diferente")
                print("- Restricciones de IP o geolocalización")
                print("- Credenciales incorrectas")
                print("- Servidor configurado para solo claves SSH")
        else:
            print("❌ Problema básico de conectividad o DNS")
            
    except KeyboardInterrupt:
        print("\n🛑 Diagnóstico interrumpido por el usuario")
    except Exception as e:
        print(f"\n❌ Error en diagnóstico: {e}")
    
    print(f"\n📋 RESUMEN PARA {TARGET_IP}:")
    print("- Si puerto 22 está cerrado: SSH no activo o puerto diferente")
    print("- Si timeout: Firewall bloqueando o servidor inaccesible")
    print("- Si conecta pero falla auth: Credenciales incorrectas")
    print("- Si banner extraño: Servicio diferente en puerto 22")
