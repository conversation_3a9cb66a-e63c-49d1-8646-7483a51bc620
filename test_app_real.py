#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test rápido de la aplicación principal con descarga IPTV
"""

import sys
import os

# Agregar directorio actual al path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_main_app():
    """Prueba básica de la aplicación principal"""
    print("🚀 INICIANDO APLICACIÓN M3U DOWNLOAD MANAGER")
    print("=" * 60)
    print()
    print("📋 INSTRUCCIONES PARA PRUEBA:")
    print("1. Conectar al servidor SFTP")
    print("2. Navegar a una carpeta donde descargar")
    print("3. Cargar una lista M3U o usar Xtream Codes")
    print("4. Seleccionar elementos y descargar")
    print("5. Elegir 'SÍ' para descarga remota (wget en servidor)")
    print("6. Observar el progreso en tiempo real")
    print()
    print("🎯 FOCUS: Verificar que el monitoreo NO termine prematuramente")
    print("=" * 60)
    print()
    
    try:
        # Importar y ejecutar la aplicación principal
        from main import main
        main()
        
    except KeyboardInterrupt:
        print("\n⚠️ Aplicación interrumpida por el usuario")
    except Exception as e:
        print(f"\n❌ Error en la aplicación: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_main_app()
