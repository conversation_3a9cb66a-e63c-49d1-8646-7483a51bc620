# Corrección de Descarga Remota con wget

## Problemas identificados

1. **Ruta incorrecta de descarga**: La descarga remota no estaba usando la carpeta SFTP actual seleccionada por el usuario, sino que estaba construyendo una ruta incorrecta duplicando el nombre del archivo.

2. **Formato del comando wget**: Aunque el comando wget ya incluía los parámetros correctos (`--continue`, `--tries=0` y `--user-agent="XCIPTV"`), la duplicación de la ruta del archivo causaba problemas para guardar en la ubicación correcta.

## Solución implementada

### 1. Corrección en `download_manager_requests.py`

Se identificó que al crear un elemento de descarga, se estaba duplicando el nombre del archivo en la ruta remota:

```python
# Código original con error
remote_path=os.path.join(remote_path, clean_filename).replace('\\', '/')
```

Se corrigió para que simplemente use la ruta remota proporcionada sin añadir el nombre del archivo:

```python
# Código corregido
remote_path=remote_path.replace('\\', '/')
```

### 2. Verificación del comando wget

Se verificó que el comando wget ya estaba correctamente configurado en `sftp_manager.py`:

```python
wget_cmd = (
    f'cd "{remote_path}" && '  # Cambiar al directorio de destino primero
    f'nohup wget --continue --tries=0 --user-agent="{user_agent}" '
    f'--progress=dot:mega '  # Mejor formato para logs
    f'--output-document="{filename}" "{url}" > {log_path} 2>&1 &'
)
```

### 3. Flujo correcto de descarga

El flujo de descarga ahora funciona así:

1. El usuario selecciona archivos para descargar en la UI.
2. La aplicación obtiene la ruta actual del SFTP desde `current_path_var`.
3. Se llama a `download_manager.add_remote_download(url, filename, remote_path)`.
4. El `download_manager` guarda la ruta remota sin modificarla.
5. Cuando se inicia la descarga, se ejecuta `sftp_manager.remote_download(url, remote_path, filename)`.
6. El método `remote_download` ejecuta el comando wget en la carpeta correcta con todos los parámetros necesarios.

## Pruebas realizadas

Se creó un script de prueba `test_remote_download_fix.py` que:

1. Conecta a un servidor SFTP.
2. Crea directorios de prueba.
3. Intenta descargar un archivo pequeño usando el gestor de descargas.
4. Verifica que el archivo se descargue en la ubicación correcta.
5. Muestra información detallada sobre el archivo descargado.

## Consideraciones futuras

- Es importante verificar que la ruta del SFTP exista antes de iniciar una descarga.
- Se debe considerar añadir retroalimentación visual en la UI cuando una descarga falla debido a problemas de ruta.
- Podría ser útil añadir una opción para configurar parámetros adicionales para wget en un archivo de configuración.
