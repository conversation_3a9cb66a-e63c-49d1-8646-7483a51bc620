#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test para verificar que los hilos largos se detienen correctamente
"""

import threading
import time
import signal
import sys
from main import M3UDownloadApp
import tkinter as tk

def test_thread_cleanup():
    """Prueba que todos los hilos se detengan correctamente"""
    print("🧪 PRUEBA DE LIMPIEZA DE HILOS")
    print("="*50)
    
    # Crear la aplicación
    root = tk.Tk()
    root.withdraw()  # Ocultar la ventana principal
    
    app = M3UDownloadApp(root)
    
    print(f"📊 Hilos activos iniciales: {threading.active_count()}")
    
    # Simular algo de actividad
    time.sleep(2)
    
    print(f"📊 Hilos activos durante ejecución: {threading.active_count()}")
    
    # Listar hilos activos
    for thread in threading.enumerate():
        print(f"   - {thread.name}: {thread.daemon} (daemon={thread.daemon})")
    
    # Cerrar la aplicación limpiamente
    print("\n🔄 Cerrando aplicación...")
    app.on_closing()
    
    # Esperar un momento para que los hilos terminen
    time.sleep(2)
    
    print(f"📊 Hilos activos después del cierre: {threading.active_count()}")
    
    # Listar hilos restantes
    remaining_threads = []
    for thread in threading.enumerate():
        if thread != threading.current_thread():
            remaining_threads.append(thread)
            print(f"   - {thread.name}: {thread.is_alive()} (daemon={thread.daemon})")
    
    # Verificar resultado
    if len(remaining_threads) <= 1:  # Solo debería quedar el hilo principal
        print("✅ PRUEBA EXITOSA: Todos los hilos se detuvieron correctamente")
        return True
    else:
        print("❌ PRUEBA FALLIDA: Algunos hilos no se detuvieron")
        return False

def test_download_manager_threads():
    """Prueba específicamente los hilos del gestor de descargas"""
    print("\n🧪 PRUEBA DE HILOS DEL GESTOR DE DESCARGAS")
    print("="*55)
    
    from download_manager_requests import DownloadManager
    from sftp_manager import SFTPManager
    
    # Crear gestor de descargas
    sftp_manager = SFTPManager()
    download_manager = DownloadManager(max_concurrent_downloads=1, sftp_manager=sftp_manager)
    
    print(f"📊 Hilos antes de iniciar: {threading.active_count()}")
    
    # Iniciar el gestor
    download_manager.start_downloads()
    
    time.sleep(1)
    print(f"📊 Hilos después de iniciar: {threading.active_count()}")
    
    # Listar hilos activos
    for thread in threading.enumerate():
        if "download" in thread.name.lower() or "monitor" in thread.name.lower():
            print(f"   - {thread.name}: activo={thread.is_alive()}")
    
    # Detener el gestor
    print("\n🛑 Deteniendo gestor de descargas...")
    download_manager.stop_downloads()
    
    time.sleep(2)
    print(f"📊 Hilos después de detener: {threading.active_count()}")
    
    # Verificar que los hilos específicos terminaron
    download_threads = []
    for thread in threading.enumerate():
        if "download" in thread.name.lower() or "monitor" in thread.name.lower():
            download_threads.append(thread)
            print(f"   - {thread.name}: activo={thread.is_alive()}")
    
    if len(download_threads) == 0:
        print("✅ PRUEBA EXITOSA: Todos los hilos del gestor se detuvieron")
        return True
    else:
        print("❌ PRUEBA FALLIDA: Algunos hilos del gestor no se detuvieron")
        return False

def signal_handler(signum, frame):
    """Maneja la señal de interrupción para cerrar limpiamente"""
    print("\n⚠️ Interrupción detectada. Cerrando pruebas...")
    sys.exit(0)

if __name__ == "__main__":
    # Configurar manejo de señales
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # Ejecutar pruebas
        result1 = test_download_manager_threads()
        result2 = test_thread_cleanup()
        
        print("\n🏁 RESUMEN DE PRUEBAS:")
        print(f"   Gestor de descargas: {'✅ EXITOSA' if result1 else '❌ FALLIDA'}")
        print(f"   Aplicación completa: {'✅ EXITOSA' if result2 else '❌ FALLIDA'}")
        
        if result1 and result2:
            print("\n🎉 TODAS LAS PRUEBAS EXITOSAS")
            sys.exit(0)
        else:
            print("\n💥 ALGUNAS PRUEBAS FALLARON")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ ERROR DURANTE LAS PRUEBAS: {e}")
        sys.exit(1)
