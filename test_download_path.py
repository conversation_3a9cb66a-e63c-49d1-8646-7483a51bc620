#!/usr/bin/env python3
"""
Script de prueba para verificar que las descargas se realizan en la carpeta correcta del SFTP.
"""

import sys
import os
import time

# Añadir el directorio actual al path para importar los módulos
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from sftp_manager import SFTPManager
    from download_manager_requests import DownloadManager
except ImportError as e:
    print(f"❌ Error importando módulos: {e}")
    sys.exit(1)

def test_download_path():
    """Prueba que las descargas usen la carpeta correcta"""
    print("🧪 PRUEBA: Verificar que las descargas usen la carpeta SFTP actual")
    print("=" * 60)
    
    # Configuración de conexión (usar las credenciales del debug_final.py)
    config = {
        'hostname': '***************',
        'port': 22,
        'username': 'root',
        'password': '5dhCkm5Dz1BpWgxZpdBisrOVo',
        'timeout': 15
    }
    
    # URL de prueba pequeña
    test_url = "https://upload.wikimedia.org/wikipedia/commons/c/ca/1x1.png"
    test_filename = "test_download_path.png"
    
    # Directorio de prueba
    test_directory = "/tmp/test_download_verification"
    
    sftp = SFTPManager()
    
    try:
        print(f"🔗 Conectando a {config['username']}@{config['hostname']}...")
        
        # Conectar usando el método directo
        success = sftp.connect(
            hostname=config['hostname'],
            port=config['port'],
            username=config['username'],
            password=config['password'],
            timeout=config['timeout']
        )
        
        if not success:
            print("❌ No se pudo conectar al servidor SFTP")
            return False
            
        print("✅ Conexión SFTP establecida")
        
        # Crear directorio de prueba si no existe
        print(f"📁 Creando directorio de prueba: {test_directory}")
        if sftp.ssh_client:
            mkdir_cmd = f"mkdir -p {test_directory}"
            stdin, stdout, stderr = sftp.ssh_client.exec_command(mkdir_cmd)
            time.sleep(1)
        
        # Verificar que el directorio existe
        if not sftp.verify_directory_exists(test_directory):
            print(f"❌ No se pudo crear el directorio: {test_directory}")
            return False
            
        print(f"✅ Directorio creado: {test_directory}")
        
        # Crear gestor de descargas
        download_manager = DownloadManager()
        download_manager.set_sftp_manager(sftp)
        
        print(f"🌐 Iniciando descarga remota:")
        print(f"   URL: {test_url}")
        print(f"   Archivo: {test_filename}")
        print(f"   Carpeta: {test_directory}")
        
        # Iniciar descarga remota
        download_id = download_manager.add_remote_download(
            url=test_url,
            filename=test_filename,
            remote_path=test_directory
        )
        
        print(f"⏳ Descarga iniciada con ID: {download_id}")
        
        # Esperar a que la descarga se complete
        print("⏳ Esperando que complete la descarga...")
        max_wait = 30
        waited = 0
        
        while waited < max_wait:
            time.sleep(2)
            waited += 2
            
            # Verificar si el archivo existe en el directorio correcto
            expected_path = f"{test_directory}/{test_filename}"
            if sftp.ssh_client:
                check_cmd = f'[ -f "{expected_path}" ] && echo "EXISTS" || echo "NOT_EXISTS"'
                stdin, stdout, stderr = sftp.ssh_client.exec_command(check_cmd)
                result = stdout.read().decode().strip()
                
                if result == "EXISTS":
                    print(f"✅ ¡ÉXITO! Archivo descargado en la ubicación correcta:")
                    
                    # Mostrar información del archivo
                    info_cmd = f'ls -lh "{expected_path}"'
                    stdin, stdout, stderr = sftp.ssh_client.exec_command(info_cmd)
                    file_info = stdout.read().decode().strip()
                    print(f"   📄 {file_info}")
                    
                    # Limpiar archivo de prueba
                    cleanup_cmd = f'rm -f "{expected_path}"'
                    stdin, stdout, stderr = sftp.ssh_client.exec_command(cleanup_cmd)
                    
                    return True
                    
            print(f"   ⏳ Esperando... ({waited}s de {max_wait}s)")
        
        print(f"❌ La descarga no se completó en {max_wait} segundos")
        
        # Verificar si hay logs de descarga
        if sftp.ssh_client:
            logs_cmd = "ls -la /tmp/wget_log_* 2>/dev/null | tail -5"
            stdin, stdout, stderr = sftp.ssh_client.exec_command(logs_cmd)
            logs = stdout.read().decode().strip()
            if logs:
                print(f"📜 Logs de wget encontrados:")
                print(logs)
                
                # Mostrar contenido del log más reciente
                latest_log_cmd = "cat $(ls -t /tmp/wget_log_* | head -1) 2>/dev/null | tail -10"
                stdin, stdout, stderr = sftp.ssh_client.exec_command(latest_log_cmd)
                log_content = stdout.read().decode().strip()
                if log_content:
                    print(f"📜 Contenido del log más reciente:")
                    print(log_content)
        
        return False
        
    except Exception as e:
        print(f"❌ Error durante la prueba: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        print("\n🔌 Desconectando...")
        if sftp:
            sftp.disconnect()

if __name__ == "__main__":
    success = test_download_path()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ PRUEBA EXITOSA: Las descargas se realizan en la carpeta correcta")
    else:
        print("❌ PRUEBA FALLIDA: Verificar configuración de descargas")
    
    sys.exit(0 if success else 1)
