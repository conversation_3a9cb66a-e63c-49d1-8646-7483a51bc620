#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SFTP Manager - Módulo para gestión de conexiones SFTP/SSH (Versión Simplificada y Robusta)
"""

import paramiko
import stat
import os
import time
from datetime import datetime
import threading
import socket
from typing import Optional, Dict, List, Any
import re

def parse_wget_progress(log_content: str) -> float:
    """
    Busca en el contenido del log la última aparición del progreso de wget
    y devuelve el porcentaje como un float.
    """
    # Expresión regular para encontrar el porcentaje. Ej: "eta 1s]  55%[================>]"
    # Busca uno o más dígitos seguidos de un '%'
    progress_matches = re.findall(r'(\d{1,3})%', log_content)
    
    if not progress_matches:
        return 0.0
        
    # Devuelve el último porcentaje encontrado, que es el más reciente
    return float(progress_matches[-1])

class SFTPManager:
    def __init__(self):
        self.ssh_client: Optional[paramiko.SSHClient] = None
        self.sftp_client: Optional[paramiko.SFTPClient] = None
        self.is_connected_flag = False
        self.connection_lock = threading.Lock()
        
    def connect(self, hostname: str, port: int = 22, username: Optional[str] = None, 
                password: Optional[str] = None, key_file: Optional[str] = None, timeout: int = 15) -> bool:
        """Establece conexión SSH/SFTP con el servidor de forma robusta y simplificada."""
        with self.connection_lock:
            self.disconnect()
            
            print(f"🔄 Conectando a {username}@{hostname}:{port} (timeout: {timeout}s)")
            
            try:
                self.ssh_client = paramiko.SSHClient()
                self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                
                connect_kwargs = {
                    'hostname': hostname,
                    'port': port,
                    'username': username,
                    'password': password,
                    'timeout': timeout,
                    'allow_agent': False,      # Clave para evitar ciertos bloqueos
                    'look_for_keys': False,    # Clave para evitar ciertos bloqueos
                    'compress': True           # Mejora el rendimiento en redes lentas
                }

                # Si se usa clave privada
                if key_file and os.path.exists(key_file):
                    print("🔐 Autenticación por clave privada")
                    connect_kwargs['key_filename'] = key_file
                    # La contraseña puede ser necesaria para desbloquear la clave
                    if password:
                        connect_kwargs['passphrase'] = password
                    # Eliminar 'password' si se usa clave para evitar ambigüedad
                    connect_kwargs.pop('password', None)
                elif not password:
                     raise ValueError("Se requiere una contraseña o un archivo de clave privada.")

                start_time = datetime.now()
                
                # Conectar
                self.ssh_client.connect(**connect_kwargs)
                
                connect_time = (datetime.now() - start_time).total_seconds()
                print(f"✅ SSH conectado en {connect_time:.2f}s")
                
                # Abrir sesión SFTP
                self.sftp_client = self.ssh_client.open_sftp()
                
                # Configurar timeout del canal para operaciones
                channel = self.sftp_client.get_channel()
                if channel:
                    channel.settimeout(timeout)
                
                self.is_connected_flag = True
                print("🎉 Conexión SFTP establecida con éxito.")
                return True

            except paramiko.AuthenticationException:
                error_msg = "Error de autenticación: Credenciales incorrectas."
                print(f"❌ {error_msg}")
                self._cleanup_connection()
                raise Exception(error_msg)
            
            except (socket.timeout, paramiko.SSHException) as e:
                error_msg = f"Timeout o error de red conectando a {hostname}:{port}. El servidor podría estar bloqueando la conexión. Detalles: {e}"
                print(f"❌ {error_msg}")
                self._cleanup_connection()
                raise Exception(error_msg)

            except Exception as e:
                error_msg = f"Error inesperado durante la conexión: {e}"
                print(f"❌ {error_msg}")
                self._cleanup_connection()
                raise Exception(error_msg)

    def connect_direct(self, hostname: str, port: int = 22, username: Optional[str] = None,
                password: Optional[str] = None, timeout: int = 10) -> bool:
        """
        Conexión directa usando exactamente la misma configuración que funcionó en debug_final.py
        Esta es una implementación simple y robusta sin capas adicionales.
        """
        print(f"\n🚀 CONEXIÓN DIRECTA A {hostname}")
        print("="*50)
        
        # Cerrar conexión existente si hay alguna
        self.disconnect()
        
        # Inicializar start_time para asegurar que siempre esté definido
        start_time = time.time()
        
        try:
            # Crear nuevo cliente SSH
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            print(f"INFO: Intentando conectar a {username}@{hostname}:{port}...")
            start_time = time.time()  # Reiniciamos el contador justo antes de conectar
            
            # Usar EXACTAMENTE la misma configuración que funcionó en debug_final.py
            self.ssh_client.connect(
                hostname=hostname,
                port=port,
                username=username,
                password=password,
                timeout=timeout,
                allow_agent=False,
                look_for_keys=False,
                compress=True  # Probar con compresión activada, es más común
            )
            
            elapsed = time.time() - start_time
            print(f"INFO: ✅ ¡CONEXIÓN EXITOSA en {elapsed:.2f}s!")
            
            # Crear cliente SFTP
            self.sftp_client = self.ssh_client.open_sftp()
            print("INFO: ✅ Cliente SFTP creado correctamente")
            
            # Hacer una prueba simple
            test_result = self.sftp_client.listdir('/')
            print(f"INFO: ✅ Test listdir exitoso: {len(test_result)} elementos")
            
            self.is_connected_flag = True
            return True
            
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"ERROR: ❌ CONEXIÓN FALLIDA después de {elapsed:.2f}s: {e}")
            self._cleanup_connection()
            raise Exception(f"Error de conexión: {str(e)}")
            
        finally:
            print("="*50)

    def _cleanup_connection(self):
        """Limpia recursos de conexión"""
        try:
            if self.sftp_client:
                self.sftp_client.close()
                self.sftp_client = None
            if self.ssh_client:
                self.ssh_client.close()
                self.ssh_client = None
            self.is_connected_flag = False
        except:
            pass
    
    def disconnect(self) -> None:
        """Cierra la conexión SFTP/SSH"""
        with self.connection_lock:
            try:
                if self.sftp_client:
                    self.sftp_client.close()
                    self.sftp_client = None
                    
                if self.ssh_client:
                    self.ssh_client.close()
                    self.ssh_client = None
                    
                self.is_connected_flag = False
                
            except Exception as e:
                print(f"Error cerrando conexión: {e}")
    
    def is_connected(self) -> bool:
        """Verifica si la conexión está activa"""
        if not self.is_connected_flag or not self.sftp_client:
            return False
        
        try:
            # Test simple y rápido para verificar conexión
            # Usar timeout muy corto para evitar bloqueos
            channel = self.sftp_client.get_channel()
            if channel and not channel.closed:
                return True
            else:
                self.is_connected_flag = False
                return False
        except:
            self.is_connected_flag = False
            return False

    def get_remote_download_progress(self, log_file_path: str) -> float:
        """
        Lee el contenido de un archivo de log remoto y extrae el progreso de wget.
        """
        if not self.is_connected() or not self.sftp_client:
            print("WARN: No se puede obtener progreso, SFTP no conectado.")
            return 0.0

        try:
            with self.sftp_client.open(log_file_path, 'r') as f:
                # Leer las últimas líneas podría ser más eficiente, pero para logs
                # pequeños, leer todo el archivo es más simple y robusto.
                content = f.read().decode('utf-8', errors='ignore')
                return parse_wget_progress(content)
        except FileNotFoundError:
            # El archivo de log puede no haber sido creado todavía.
            return 0.0
        except Exception as e:
            print(f"ERROR: No se pudo leer el archivo de log remoto '{log_file_path}': {e}")
            return 0.0

    def _create_remote_directory_recursive(self, remote_path):
        """Crea un directorio remoto recursivamente, similar a mkdir -p."""
        if not self.sftp_client:
            return
        
        current_dir = ''
        # Asegurarse de que la ruta use slashes de Unix
        parts = remote_path.replace('\\', '/').split('/')
        
        for part in parts:
            if not part:
                continue
            current_dir += part + '/'
            try:
                self.sftp_client.stat(current_dir)
            except FileNotFoundError:
                self.sftp_client.mkdir(current_dir)

    def remote_download(self, url: str, remote_path: str, filename: str) -> Dict[str, Any]:
        """
        Ejecuta wget en el servidor remoto para descargar un archivo directamente.
        
        Args:
            url: URL del archivo a descargar
            remote_path: Ruta del directorio donde guardar el archivo en el servidor remoto
            filename: Nombre del archivo para guardar en el servidor remoto
            
        Returns:
            Dict con resultado de la operación y datos para seguimiento
        """
        if not self.is_connected() or not self.ssh_client:
            raise Exception("No hay conexión SSH activa")
            
        try:
            # Asegurar que la ruta de descarga exista
            if not self.verify_directory_exists(remote_path):
                print(f"⚠️ Directorio no encontrado: {remote_path}")
                try:
                    print(f"🔄 Intentando crear directorio: {remote_path}")
                    self._create_remote_directory_recursive(remote_path)
                except Exception as e:
                    raise Exception(f"Error creando directorio de descarga: {e}")
            
            # Ruta completa del archivo a guardar (combinando remote_path y filename)
            remote_file_path = os.path.join(remote_path, filename).replace('\\', '/')
            
            # Generar un nombre único para el archivo de log
            log_timestamp = int(time.time())
            log_filename = f"wget_log_{log_timestamp}_{filename}.log"
            log_path = f"/tmp/{log_filename}"
            
            # User agent para evitar bloqueos - Ahora usando XCIPTV como solicitado
            user_agent = "XCIPTV"

            # Comando wget optimizado ejecutado en segundo plano with nohup y timeout
            # El & al final hace que se ejecute en segundo plano
            # nohup asegura que continúe incluso si se cierra la sesión SSH
            # timeout 600 limita la descarga a 10 minutos máximo para evitar long threads
            wget_cmd = (
                f'cd "{remote_path}" && '  # Cambiar al directorio de destino primero
                f'nohup timeout 600 wget --continue --tries=3 --user-agent="{user_agent}" '
                f'--progress=dot:mega --timeout=15 '  # Timeout de conexión de 15 segundos
                f'--output-document="{filename}" "{url}" > {log_path} 2>&1 &'
            )

            print(f"🌐 Iniciando descarga remota: {filename}")
            print(f"📂 Guardando en: {remote_path}")
            print(f"📝 Log en: {log_path}")
            print(f"🌍 URL: {url}")
            print(f"🔍 Comando: {wget_cmd}")

            # Ejecutar comando en segundo plano con timeout más corto
            stdin, stdout, stderr = self.ssh_client.exec_command(wget_cmd, timeout=5)
            
            # Esperar un corto tiempo para ver si hay errores inmediatos
            time.sleep(1)
            error = stderr.read().decode('utf-8', errors='ignore')
            
            if error:
                return {
                    'success': False,
                    'message': f"Error iniciando descarga: {error}",
                    'log_file': log_path,
                    'process_info': '',
                    'pid': None
                }
            
            # Verificar que el proceso se haya iniciado correctamente
            # Esto busca el proceso wget para la URL específica
            check_cmd = f'ps aux | grep wget | grep "{url}" | grep -v grep'
            stdin, stdout, stderr = self.ssh_client.exec_command(check_cmd)
            process_info = stdout.read().decode('utf-8', errors='ignore')
            
            # Extraer el PID del proceso si está disponible
            pid = None
            if process_info:
                try:
                    # Intentar extraer el PID (generalmente el segundo campo en ps aux)
                    pid = process_info.split()[1]
                except (IndexError, ValueError):
                    pass
            
            if process_info:
                print(f"✅ Descarga iniciada en segundo plano: {os.path.basename(remote_path)}")
                return {
                    'success': True,
                    'message': f"Descarga iniciada en el servidor: {os.path.basename(remote_path)}",
                    'log_file': log_path,
                    'process_info': process_info,
                    'pid': pid
                }
            else:
                # El proceso no se detecta, pero puede ser porque ya terminó (archivos pequeños)
                # Verificar si el archivo se descargó exitosamente
                time.sleep(1)  # Esperar un poco más
                
                try:
                    # Verificar si el archivo existe y tiene contenido
                    check_file_cmd = f'[ -f "{remote_path}" ] && stat -c "%s" "{remote_path}" || echo "0"'
                    stdin, stdout, stderr = self.ssh_client.exec_command(check_file_cmd)
                    file_size = int(stdout.read().decode('utf-8', errors='ignore').strip())
                    
                    if file_size > 0:
                        # El archivo existe y tiene contenido - descarga exitosa
                        def format_size(bytes_val):
                            for unit in ['B', 'KB', 'MB', 'GB']:
                                if bytes_val < 1024:
                                    return f"{bytes_val:.1f} {unit}"
                                bytes_val /= 1024
                            return f"{bytes_val:.1f} TB"
                        
                        formatted_size = format_size(file_size)
                        print(f"✅ Descarga completada (muy rápida): {os.path.basename(remote_path)} ({formatted_size})")
                        
                        return {
                            'success': True,
                            'message': f"Descarga completada en el servidor: {os.path.basename(remote_path)} ({formatted_size})",
                            'log_file': log_path,
                            'process_info': 'Completado rápidamente',
                            'pid': None,
                            'completed_fast': True
                        }
                except Exception as e:
                    print(f"Error verificando archivo descargado: {e}")
                
                # Si llegamos aquí, realmente no se pudo iniciar o completar
                return {
                    'success': False,
                    'message': f"No se pudo verificar el inicio de la descarga para {os.path.basename(remote_path)}",
                    'log_file': log_path,
                    'process_info': '',
                    'pid': None
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f"Error en descarga remota: {e}",
                'log_file': '',
                'process_info': '',
                'pid': None
            }
    
    def check_remote_download_status(self, pid: Optional[str] = None, log_file: Optional[str] = None, file_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Verifica el estado de una descarga remota mediante múltiples métodos:
        1. Verifica si el proceso wget con el PID dado aún está en ejecución
        2. Analiza el archivo de log para extraer información de progreso
        3. Verifica si el archivo existe y su tamaño
        
        Args:
            pid: ID del proceso wget (opcional)
            log_file: Ruta al archivo de log (opcional)
            file_path: Ruta del archivo que se está descargando (opcional)
            
        Returns:
            Dict con estado de la descarga y datos disponibles
        """
        if not self.is_connected() or not self.ssh_client:
            return {
                'success': False, 
                'status': 'UNKNOWN',
                'message': 'No hay conexión SFTP activa',
                'progress': 0,
                'file_exists': False,
                'file_size': '0 B',
                'is_running': False
            }
        
        result = {
            'success': True,
            'status': 'UNKNOWN',
            'message': '',
            'progress': 0,
            'file_exists': False,
            'file_size': '0 B',
            'is_running': False
        }
        
        # 1. Verificar si el proceso sigue en ejecución
        if pid:
            try:
                check_process_cmd = f'ps -p {pid} -o pid= | wc -l'
                stdin, stdout, stderr = self.ssh_client.exec_command(check_process_cmd)
                process_exists = int(stdout.read().decode('utf-8', errors='ignore').strip()) > 0
                result['is_running'] = process_exists
                
                if process_exists:
                    result['status'] = 'DOWNLOADING'
                    result['message'] = 'Descarga en progreso'
            except Exception as e:
                print(f"Error verificando proceso {pid}: {e}")
        
        # 2. Analizar archivo de log para obtener progreso
        if log_file:
            try:
                # Obtener las últimas 50 líneas del log para análisis
                tail_cmd = f'tail -n 50 {log_file}'
                stdin, stdout, stderr = self.ssh_client.exec_command(tail_cmd)
                log_content = stdout.read().decode('utf-8', errors='ignore')
                
                # Buscar patrones de progreso en el log
                if "100%" in log_content:
                    result['status'] = 'COMPLETED'
                    result['message'] = 'Descarga completada según el log'
                    result['progress'] = 100
                elif "saved [" in log_content:
                    # Extraer progreso de wget (formato varía, esto es aproximado)
                    try:
                        # Buscar líneas como "xx% [..." o "100% [xxxxx/xxxxx]"
                        progress_lines = [line for line in log_content.split('\n') if '%' in line and '[' in line]
                        if progress_lines:
                            last_progress = progress_lines[-1]
                            progress_match = last_progress.split('%')[0].strip()
                            if progress_match and progress_match.isdigit():
                                result['progress'] = int(progress_match)
                                if result['progress'] == 100:
                                    result['status'] = 'COMPLETED'
                                    result['message'] = 'Descarga completada (100%)'
                                else:
                                    result['status'] = 'DOWNLOADING'
                                    result['message'] = f'Progreso: {result["progress"]}%'
                    except Exception as e:
                        print(f"Error analizando progreso: {e}")
                
                # Verificar si hay errores en el log
                error_indicators = ["ERROR", "failed", "No such file or directory", "cannot", "Could not"]
                for indicator in error_indicators:
                    if indicator in log_content:
                        result['status'] = 'ERROR'
                        result['message'] = f'Error detectado en log: {indicator}'
                        break
                        
            except Exception as e:
                print(f"Error analizando log {log_file}: {e}")
        
        # 3. Verificar si el archivo existe y su tamaño
        if file_path:
            try:
                # Verificar si el archivo existe
                check_file_cmd = f'[ -f "{file_path}" ] && echo "EXISTS" || echo "NOT_EXISTS"'
                stdin, stdout, stderr = self.ssh_client.exec_command(check_file_cmd)
                file_exists = stdout.read().decode('utf-8', errors='ignore').strip() == "EXISTS"
                result['file_exists'] = file_exists
                
                if file_exists:
                    # Obtener tamaño
                    size_cmd = f'du -h "{file_path}" | cut -f1'
                    stdin, stdout, stderr = self.ssh_client.exec_command(size_cmd)
                    file_size = stdout.read().decode('utf-8', errors='ignore').strip()
                    result['file_size'] = file_size
                    
                    # Si el archivo existe pero el proceso no está corriendo, considerar completado
                    if not result['is_running'] and result['status'] == 'UNKNOWN':
                        result['status'] = 'COMPLETED'
                        result['message'] = 'Archivo encontrado en servidor'
                        result['progress'] = 100
            except Exception as e:
                print(f"Error verificando archivo {file_path}: {e}")
        
        # Evaluación final del estado
        if result['status'] == 'UNKNOWN':
            if result['is_running']:
                result['status'] = 'DOWNLOADING'
                result['message'] = 'Descarga en progreso'
            elif result['file_exists'] and result['file_size'] != '0 B':
                result['status'] = 'COMPLETED'
                result['message'] = 'Archivo encontrado en servidor'
                result['progress'] = 100
            else:
                result['status'] = 'ERROR'
                result['message'] = 'Estado desconocido o error'
        
        return result
    
    def list_directory(self, remote_path: str = '/', timeout: int = 15) -> List[Dict[str, Any]]:
        """
        Lista el contenido de un directorio remoto.
        
        Args:
            remote_path: Ruta del directorio a listar
            timeout: Timeout en segundos para la operación
            
        Returns:
            Lista de diccionarios con información de archivos/directorios
        """
        if not self.is_connected() or not self.sftp_client:
            raise Exception("No hay conexión SFTP activa")
        
        # Formatear ruta
        remote_path = remote_path.replace('\\', '/')
        if not remote_path.startswith('/'):
            remote_path = '/' + remote_path
            
        print(f"📂 Listando directorio: {remote_path}")
        
        # Configurar timeout del canal
        channel = self.sftp_client.get_channel()
        if channel:
            channel.settimeout(timeout)
        
        # Validar que la ruta exista y sea un directorio
        if not self.verify_directory_exists(remote_path):
            print(f"⚠️ Directorio no existe o no es accesible: {remote_path}")
            # Solo usar directorio raíz como fallback cuando se llama desde la UI principal
            # pero no cuando se invoca directamente para probar rutas específicas
            raise Exception(f"El directorio no existe o no es accesible: {remote_path}")
        
        try:
            start_time = time.time()
            # Obtener lista de archivos
            files = self.sftp_client.listdir_attr(remote_path)
            print(f"✅ Listado obtenido en {time.time() - start_time:.2f}s")
            result = []
            
            # Resetear timeout a un valor más alto para operaciones posteriores
            if channel:
                channel.settimeout(60)
            
            for file_attr in files:
                # Determinar tipo de archivo
                if file_attr.st_mode and stat.S_ISDIR(file_attr.st_mode):
                    file_type = "directory"
                    icon = "📁"
                elif file_attr.st_mode and stat.S_ISLNK(file_attr.st_mode):
                    file_type = "symlink"
                    icon = "🔗"
                else:
                    file_type = "file"
                    icon = "📄"
                
                # Formatear tamaño
                size_bytes = file_attr.st_size if file_attr.st_size else 0
                if file_type == "directory":
                    size_str = "-"
                else:
                    size_str = self._format_file_size(size_bytes)
                
                # Formatear fecha de modificación
                if file_attr.st_mtime:
                    mod_time = datetime.fromtimestamp(file_attr.st_mtime)
                    mod_time_str = mod_time.strftime("%Y-%m-%d %H:%M")
                else:
                    mod_time_str = "Desconocida"
                
                result.append({
                    'name': file_attr.filename,
                    'type': file_type,
                    'icon': icon,
                    'size': size_str,
                    'size_bytes': size_bytes,
                    'modified': mod_time_str,
                    'permissions': stat.filemode(file_attr.st_mode) if file_attr.st_mode else "Unknown"
                })
            
            # Ordenar: directorios primero, luego por nombre
            result.sort(key=lambda x: (x['type'] != 'directory', x['name'].lower()))
            
            return result
            
        except FileNotFoundError:
            print(f"⚠️ Directorio no encontrado: {remote_path}")
            raise Exception(f"Directorio no encontrado: {remote_path}")
        except PermissionError:
            print(f"⚠️ Sin permisos para acceder al directorio: {remote_path}")
            raise Exception(f"Sin permisos para acceder al directorio: {remote_path}")
        except Exception as e:
            print(f"⚠️ Error listando directorio '{remote_path}': {e}")
            raise Exception(f"Error listando directorio '{remote_path}': {e}")

    def create_directory(self, remote_path: str) -> bool:
        """
        Crea un directorio en el servidor remoto.
        
        Args:
            remote_path: Ruta del directorio a crear
            
        Returns:
            True si se creó correctamente
        """
        if not self.is_connected() or not self.sftp_client:
            raise Exception("No hay conexión SFTP activa")
        
        try:
            self.sftp_client.mkdir(remote_path)
            print(f"✅ Directorio creado: {remote_path}")
            return True
        except Exception as e:
            raise Exception(f"Error creando directorio '{remote_path}': {e}")

    def delete_file(self, remote_path: str) -> bool:
        """
        Elimina un archivo o directorio del servidor remoto.
        
        Args:
            remote_path: Ruta del archivo/directorio a eliminar
            
        Returns:
            True si se eliminó correctamente
        """
        if not self.is_connected() or not self.sftp_client:
            raise Exception("No hay conexión SFTP activa")
        
        try:
            # Verificar si es directorio o archivo
            file_stat = self.sftp_client.stat(remote_path)
            
            if file_stat.st_mode and stat.S_ISDIR(file_stat.st_mode):
                self.sftp_client.rmdir(remote_path)
                print(f"✅ Directorio eliminado: {remote_path}")
            else:
                self.sftp_client.remove(remote_path)
                print(f"✅ Archivo eliminado: {remote_path}")
            
            return True
        except Exception as e:
            raise Exception(f"Error eliminando '{remote_path}': {e}")

    def verify_directory_exists(self, remote_path: str) -> bool:
        """
        Verifica si un directorio existe en el servidor remoto.
        
        Args:
            remote_path: Ruta remota a verificar
            
        Returns:
            True si existe, False si no existe
        """
        if not self.is_connected() or not self.sftp_client:
            return False
            
        try:
            # Intentar obtener atributos del directorio
            attr = self.sftp_client.stat(remote_path)
            
            # Verificar que sea un directorio
            if attr and attr.st_mode and stat.S_ISDIR(attr.st_mode):
                return True
            else:
                print(f"⚠️ '{remote_path}' no es un directorio")
                return False
                
        except FileNotFoundError:
            print(f"⚠️ El directorio '{remote_path}' no existe")
            return False
        except Exception as e:
            print(f"⚠️ Error verificando directorio '{remote_path}': {e}")
            return False

    def _format_file_size(self, size_bytes: int) -> str:
        """Formatea el tamaño de archivo en formato legible."""
        if size_bytes == 0:
            return "0 B"
        
        units = ['B', 'KB', 'MB', 'GB', 'TB']
        i = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and i < len(units) - 1:
            size /= 1024.0
            i += 1
        
        if i == 0:
            return f"{int(size)} {units[i]}"
        else:
            return f"{size:.1f} {units[i]}"

    def __del__(self):
        """Destructor - cierra conexiones"""
        self.disconnect()
