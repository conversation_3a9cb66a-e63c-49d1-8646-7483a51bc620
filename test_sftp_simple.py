#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test SFTP Simple - Prueba básica de conexión SFTP
"""

import paramiko
import traceback

def test_simple_sftp():
    """Test básico de SFTP"""
    print("🔍 Test Básico de Conexión SFTP")
    print("=" * 40)
    
    # Datos de ejemplo - cambia estos valores por los tuyos
    print("Por favor edita este archivo con tus datos de conexión SFTP:")
    print()
    
    # CAMBIA ESTOS VALORES
    HOST = "tu-servidor.com"  # Cambia por tu servidor
    PORT = 22
    USERNAME = "tu-usuario"   # Cambia por tu usuario
    PASSWORD = "tu-password"  # Cambia por tu contraseña
    
    print(f"Host: {HOST}")
    print(f"Puerto: {PORT}")
    print(f"Usuario: {USERNAME}")
    print(f"Contraseña: {'*' * len(PASSWORD)}")
    print()
    
    if HOST == "tu-servidor.com":
        print("❌ Por favor edita este archivo con tus datos reales de conexión")
        print("Cambia las variables HOST, USERNAME y PASSWORD")
        return False
    
    try:
        print("🔄 Creando cliente SSH...")
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        print("🔄 Conectando SSH...")
        ssh.connect(
            hostname=HOST,
            port=PORT,
            username=USERNAME,
            password=PASSWORD,
            timeout=15
        )
        print("✅ SSH conectado")
        
        print("🔄 Abriendo canal SFTP...")
        sftp = ssh.open_sftp()
        print("✅ SFTP conectado")
        
        print("🔄 Probando operaciones...")
        files = sftp.listdir('.')
        print(f"✅ Directorio listado: {len(files)} archivos")
        
        # Mostrar algunos archivos
        for i, file in enumerate(files[:3]):
            print(f"   📁 {file}")
        
        sftp.close()
        ssh.close()
        
        print("\n🎉 ¡CONEXIÓN SFTP EXITOSA!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("\nDetalles del error:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_simple_sftp()
