#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de integración para verificar el progreso en tiempo real
"""

import time
import threading
from sftp_manager import SFTPManager, parse_wget_progress
from download_manager_requests import DownloadManager, DownloadStatus

# Configuración del servidor
HOSTNAME = "***************"
PORT = 22
USERNAME = "root"
PASSWORD = "5dhCkm5Dz1BpWgxZpdBisrOVo"

def test_progress_integration():
    """Prueba la integración completa del sistema de progreso"""
    
    print("🧪 PRUEBA DE INTEGRACIÓN DEL SISTEMA DE PROGRESO")
    print("="*60)
    
    # 1. Conectar SFTP
    sftp_manager = SFTPManager()
    try:
        print("🔌 Conectando al servidor SFTP...")
        success = sftp_manager.connect_direct(HOSTNAME, PORT, USERNAME, PASSWORD)
        
        if not success:
            print("❌ No se pudo conectar al servidor SFTP")
            return
        
        print("✅ Conexión SFTP establecida")
        
        # 2. Crear gestor de descargas
        download_manager = DownloadManager(max_concurrent_downloads=1, sftp_manager=sftp_manager)
        download_manager.start_downloads()
        
        print("✅ Gestor de descargas iniciado")
        
        # 3. Añadir una descarga de prueba (archivo pequeño para ver progreso rápido)
        test_url = "https://www.google.com/favicon.ico"
        filename = "test_favicon.ico"
        remote_path = "/tmp"
        
        print(f"📥 Iniciando descarga de prueba:")
        print(f"   URL: {test_url}")
        print(f"   Archivo: {filename}")
        print(f"   Destino: {remote_path}")
        
        download_id = download_manager.add_remote_download(test_url, filename, remote_path)
        print(f"✅ Descarga añadida con ID: {download_id}")
        
        # 4. Monitorear progreso
        print("\n📊 MONITOREANDO PROGRESO:")
        print("-" * 40)
        
        max_wait = 30  # Máximo 30 segundos
        waited = 0
        
        while waited < max_wait:
            with download_manager.downloads_lock:
                if download_id in download_manager.downloads:
                    item = download_manager.downloads[download_id]
                    
                    status = item.status.value
                    progress = item.progress
                    
                    print(f"⏱️  {waited:2d}s | Estado: {status:12} | Progreso: {progress:6.1f}%")
                    
                    # Si completó o falló, salir del bucle
                    if item.status in [DownloadStatus.COMPLETED, DownloadStatus.ERROR, DownloadStatus.CANCELLED]:
                        print(f"🏁 Descarga terminada con estado: {status}")
                        if item.error_message:
                            print(f"💬 Mensaje: {item.error_message}")
                        break
            
            time.sleep(2)
            waited += 2
        
        # 5. Verificar resultado final
        print("\n🔍 VERIFICACIÓN FINAL:")
        print("-" * 30)
        
        with download_manager.downloads_lock:
            if download_id in download_manager.downloads:
                final_item = download_manager.downloads[download_id]
                print(f"Estado final: {final_item.status.value}")
                print(f"Progreso final: {final_item.progress}%")
                
                if final_item.remote_log_file:
                    print(f"Archivo de log: {final_item.remote_log_file}")
                    
                    # Leer el log final para verificar
                    try:
                        final_progress = sftp_manager.get_remote_download_progress(final_item.remote_log_file)
                        print(f"Progreso según log: {final_progress}%")
                    except Exception as e:
                        print(f"Error leyendo log final: {e}")
        
        # 6. Limpiar
        download_manager.stop_downloads()
        print("✅ Gestor de descargas detenido")
        
    except Exception as e:
        print(f"❌ Error durante la prueba: {e}")
        
    finally:
        sftp_manager.disconnect()
        print("✅ Conexión SFTP cerrada")
    
    print("\n🏁 PRUEBA DE INTEGRACIÓN COMPLETADA")

def test_progress_parser():
    """Prueba la función de análisis de progreso por separado"""
    
    print("\n🧪 PRUEBA DEL ANALIZADOR DE PROGRESO")
    print("="*45)
    
    # Ejemplos de contenido de log de wget
    test_logs = [
        # Log vacío
        "",
        
        # Log con progreso parcial
        """
        --2025-07-06 15:30:00--  https://example.com/file.zip
        Resolving example.com... *************
        Connecting to example.com|*************|:443... connected.
        HTTP request sent, awaiting response... 200 OK
        Length: 93553 (91K) [application/zip]
        Saving to: '/tmp/file.zip'
        
        /tmp/file.zip           45%[=========>              ]  41.15K  --.-KB/s               
        """,
        
        # Log completo
        """
        --2025-07-06 15:30:00--  https://example.com/file.zip
        Resolving example.com... *************
        Connecting to example.com|*************|:443... connected.
        HTTP request sent, awaiting response... 200 OK
        Length: 93553 (91K) [application/zip]
        Saving to: '/tmp/file.zip'
        
        /tmp/file.zip           45%[=========>              ]  41.15K  --.-KB/s               
        /tmp/file.zip           87%[================>       ]  79.15K  --.-KB/s               
        /tmp/file.zip          100%[========================>]  91.36K  --.-KB/s    in 0.02s 
        
        2025-07-06 15:30:02 (4.50 MB/s) - '/tmp/file.zip' saved [93553/93553]
        """,
        
        # Log con múltiples porcentajes
        "Descargando... 25% completado. Ahora 50%. Finalmente 100%."
    ]
    
    expected_results = [0.0, 45.0, 100.0, 100.0]
    
    for i, (log_content, expected) in enumerate(zip(test_logs, expected_results)):
        result = parse_wget_progress(log_content)
        status = "✅" if result == expected else "❌"
        print(f"{status} Test {i+1}: Esperado {expected}%, Obtenido {result}%")
        
        if result != expected:
            print(f"   Log de prueba: {repr(log_content[:100])}")

if __name__ == "__main__":
    # Probar el analizador de progreso primero
    test_progress_parser()
    
    # Luego probar la integración completa
    test_progress_integration()
