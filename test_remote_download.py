#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de descarga remota con wget - Verifica el formato y ejecución del comando wget en el servidor
"""

import sys
import os
import time
from sftp_manager import SFTPManager

# --- Configuración ---
HOSTNAME = "***************"
PORT = 22
USERNAME = "root"
PASSWORD = "5dhCkm5Dz1BpWgxZpdBisrOVo"
TIMEOUT = 15

def test_remote_download():
    """Prueba una descarga remota usando el método correcto"""
    
    print("\n🚀 INICIANDO PRUEBA DE DESCARGA REMOTA")
    print("="*50)
    
    sftp = SFTPManager()
    
    try:
        # Conectar
        print(f"Conectando a {USERNAME}@{HOSTNAME}:{PORT}...")
        sftp.connect_direct(HOSTNAME, PORT, USERNAME, PASSWORD, TIMEOUT)
        
        # URL de prueba y destino
        test_url = "https://www.google.com/favicon.ico"
        remote_path = "/tmp/test_downloads"  # Directorio de destino
        filename = "test_remote_download.ico"  # Nombre del archivo
        
        print(f"\n📥 Configuración de descarga:")
        print(f"🌐 URL: {test_url}")
        print(f"📂 Directorio destino: {remote_path}")
        print(f"📄 Nombre archivo: {filename}")
        
        # Verificar/crear directorio de destino
        if not sftp.verify_directory_exists(remote_path):
            print(f"⚠️ Directorio no existe, creando: {remote_path}")
            try:
                # Crear directorio directamente con comando mkdir
                mkdir_cmd = f"mkdir -p {remote_path}"
                stdin, stdout, stderr = sftp.ssh_client.exec_command(mkdir_cmd)
                error = stderr.read().decode('utf-8', errors='ignore')
                if error:
                    print(f"❌ Error creando directorio: {error}")
                    return
                print(f"✅ Directorio creado correctamente")
            except Exception as e:
                print(f"❌ Error creando directorio: {e}")
                return
        
        # Iniciar descarga
        print("\n🔽 Iniciando descarga remota...")
        result = sftp.remote_download(test_url, remote_path, filename)
        
        if result['success']:
            print(f"✅ Descarga iniciada correctamente")
            print(f"🆔 PID: {result['pid']}")
            print(f"📝 Log: {result['log_file']}")
            
            # Esperar y verificar progreso
            max_wait = 30
            waited = 0
            
            print("\n⏳ Esperando que termine la descarga (máximo 30s)...")
            while waited < max_wait:
                time.sleep(2)
                waited += 2
                
                # Verificar estado
                status = sftp.check_remote_download_status(
                    pid=result['pid'],
                    log_file=result['log_file'],
                    file_path=os.path.join(remote_path, filename).replace('\\', '/')
                )
                
                print(f"🔄 Estado: {status['status']}, Progreso: {status['progress']}%, Corriendo: {'✅' if status['is_running'] else '❌'}")
                
                if status['status'] == 'COMPLETED' or (not status['is_running'] and waited > 5):
                    break
            
            # Verificar resultado final
            print("\n📊 VERIFICANDO RESULTADO FINAL:")
            
            # 1. Verificar si el archivo existe
            check_file_cmd = f'[ -f "{remote_path}/{filename}" ] && echo "EXISTS" || echo "NOT_EXISTS"'
            stdin, stdout, stderr = sftp.ssh_client.exec_command(check_file_cmd)
            file_exists = stdout.read().decode('utf-8', errors='ignore').strip() == "EXISTS"
            
            if file_exists:
                # Obtener información del archivo
                file_info_cmd = f'ls -lh "{remote_path}/{filename}"'
                stdin, stdout, stderr = sftp.ssh_client.exec_command(file_info_cmd)
                file_info = stdout.read().decode('utf-8', errors='ignore').strip()
                print(f"✅ Archivo descargado exitosamente: {file_info}")
                
                # Verificar contenido del archivo (primeros bytes)
                head_cmd = f'hexdump -C "{remote_path}/{filename}" | head -3'
                stdin, stdout, stderr = sftp.ssh_client.exec_command(head_cmd)
                file_content = stdout.read().decode('utf-8', errors='ignore')
                if file_content:
                    print(f"📄 Contenido (hex): {file_content}")
            else:
                print("❌ El archivo NO fue descargado")
            
            # 2. Mostrar log de wget
            print("\n📝 LOG DE WGET:")
            log_cmd = f'cat {result["log_file"]}'
            stdin, stdout, stderr = sftp.ssh_client.exec_command(log_cmd)
            log_content = stdout.read().decode('utf-8', errors='ignore')
            if log_content:
                print(log_content)
            else:
                print("⚠️ No hay contenido en el log")
        else:
            print(f"❌ Error iniciando descarga: {result['message']}")
        
    except Exception as e:
        print(f"\n❌ ERROR EN PRUEBA: {e}")
    finally:
        # Desconectar
        sftp.disconnect()
        print("\n👋 Conexión cerrada")
        print("="*50)

if __name__ == "__main__":
    test_remote_download()
