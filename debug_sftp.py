#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug SFTP - Test directo sin threads complicados
"""

import paramiko
import time

def test_direct_connection():
    """Test directo sin threading"""
    print("🔍 Test Directo de SFTP")
    print("=" * 30)
    
    # 1. Test servidor demo
    print("1. Probando servidor demo...")
    success_demo = test_server("test.rebex.net", 22, "demo", "password")
    
    if not success_demo:
        print("❌ Problema básico con paramiko")
        return
    
    # 2. Test tu servidor - EDITA ESTOS DATOS
    print("\n2. Probando tu servidor...")
    
    # CAMBIA ESTOS DATOS
    HOST = "***************"
    PORT = 22
    USER = "root"
    PASS = "tu_password_aqui"  # CAMBIAR AQUÍ
    
    if PASS == "tu_password_aqui":
        print("❌ Edita este archivo y cambia PASS por tu contraseña real")
        return
    
    success_yours = test_server(HOST, PORT, USER, PASS)
    
    if success_yours:
        print(f"\n🎉 ¡TU SERVIDOR FUNCIONA!")
        print(f"Usa estos datos en la aplicación:")
        print(f"  Host: {HOST}")
        print(f"  Puerto: {PORT}")
        print(f"  Usuario: {USER}")
        print(f"  Contraseña: {PASS}")
    else:
        print(f"\n❌ Tu servidor no funciona")

def test_server(host, port, user, password):
    """Test individual de servidor"""
    try:
        print(f"🔄 {user}@{host}:{port}")
        
        # Crear cliente con timeouts agresivos
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        start_time = time.time()
        
        # Conectar con timeout corto
        ssh.connect(
            hostname=host,
            port=port,
            username=user,
            password=password,
            timeout=8,
            banner_timeout=3,
            auth_timeout=5,
            allow_agent=False,
            look_for_keys=False
        )
        
        connect_time = time.time() - start_time
        print(f"  ✅ SSH OK ({connect_time:.1f}s)")
        
        # SFTP
        sftp = ssh.open_sftp()
        print(f"  ✅ SFTP OK")
        
        # Test rápido
        try:
            files = sftp.listdir('.')
            print(f"  ✅ {len(files)} archivos")
        except:
            print(f"  ⚠️ Listdir falló (algunos servidores restringen esto)")
        
        sftp.close()
        ssh.close()
        
        total_time = time.time() - start_time
        print(f"  🎯 Total: {total_time:.1f}s")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

if __name__ == "__main__":
    test_direct_connection()
