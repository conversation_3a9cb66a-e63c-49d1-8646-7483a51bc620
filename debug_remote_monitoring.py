#!/usr/bin/env python3
"""
Script para diagnosticar el problema con el monitoreo de descargas remotas.
"""

import sys
import os
import time
import threading

# Agregar el directorio actual al path para importar los módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_remote_download_monitoring():
    """Prueba el monitoreo de descargas remotas paso a paso"""
    print("🔍 DIAGNÓSTICO DE MONITOREO DE DESCARGAS REMOTAS")
    print("=" * 60)
    
    try:
        from download_manager_requests import DownloadManager, DownloadStatus
        from sftp_manager import SFTPManager
        
        # Crear managers
        print("📱 Creando managers...")
        sftp = SFTPManager()
        dm = DownloadManager(sftp_manager=sftp)
        
        # Simular conexión SFTP (sin conectar realmente)
        print("🔗 Configurando SFTP manager...")
        
        # Añadir descarga remota
        print("\n📥 Añadiendo descarga remota...")
        download_id = dm.add_download(
            url="https://httpbin.org/delay/2",
            filename="test_remote_monitoring.json",
            remote_path="/tmp"
        )
        print(f"✅ Descarga añadida: {download_id}")
        
        # Verificar estado inicial
        print("\n📊 Estado inicial:")
        downloads = dm.get_downloads_status()
        print(f"   - Total descargas: {len(downloads)}")
        
        for download in downloads:
            print(f"   - {download['filename']}:")
            print(f"     * Estado: {download['status']}")
            print(f"     * Es remota: {download['is_remote']}")
            print(f"     * Progreso: {download['progress']:.1f}%")
            print(f"     * PID remoto: {download['remote_pid']}")
            print(f"     * Log remoto: {download['remote_log_file']}")
        
        # Verificar lógica de detección de descargas activas
        print("\n🔍 Verificando lógica de detección de descargas activas:")
        
        # Esta es la lógica que usa main.py
        has_active_downloads = any(d.get('status') in ['Downloading', 'Processing', 'Queued'] for d in downloads)
        print(f"   - ¿Hay descargas activas según main.py? {has_active_downloads}")
        
        # Mostrar qué estados se consideran activos
        active_statuses = ['Downloading', 'Processing', 'Queued']
        print(f"   - Estados considerados activos: {active_statuses}")
        
        for download in downloads:
            status = download['status']
            is_active = status in active_statuses
            print(f"   - {download['filename']}: {status} -> {'ACTIVA' if is_active else 'NO ACTIVA'}")
        
        # Verificar lógica del monitor remoto
        print("\n👁️ Verificando lógica del monitor remoto:")
        
        # Esta es la lógica que usa _monitor_remote_downloads
        downloads_to_check = []
        for download in downloads:
            if download['is_remote'] and download['status'] == 'Downloading' and download['remote_log_file']:
                downloads_to_check.append(download)
        
        print(f"   - Descargas que el monitor remoto verificaría: {len(downloads_to_check)}")
        
        for download in downloads_to_check:
            print(f"   - {download['filename']}: cumple criterios para monitoreo")
        
        if not downloads_to_check:
            print("   ⚠️ PROBLEMA: No hay descargas que cumplan los criterios del monitor remoto")
            print("   📋 Criterios requeridos:")
            print("      1. is_remote = True")
            print("      2. status = 'Downloading'")
            print("      3. remote_log_file no vacío")
            
            for download in downloads:
                print(f"\n   📊 Análisis de {download['filename']}:")
                print(f"      - is_remote: {download['is_remote']} ✅" if download['is_remote'] else f"      - is_remote: {download['is_remote']} ❌")
                print(f"      - status: {download['status']} {'✅' if download['status'] == 'Downloading' else '❌'}")
                print(f"      - remote_log_file: {download['remote_log_file']} {'✅' if download['remote_log_file'] else '❌'}")
        
        # Simular inicio del sistema de descargas
        print(f"\n🚀 Estado del sistema de descargas:")
        print(f"   - Sistema corriendo: {dm.is_running}")
        
        if not dm.is_running:
            print("   ⚠️ El sistema de descargas no está corriendo")
            print("   🔧 Esto podría explicar por qué no se procesan las descargas")
        
        # Limpiar
        dm.stop_downloads()
        print("\n✅ Diagnóstico completado")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en diagnóstico: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_status_transitions():
    """Prueba las transiciones de estado de las descargas"""
    print("\n🔄 PRUEBA DE TRANSICIONES DE ESTADO")
    print("=" * 60)
    
    try:
        from download_manager_requests import DownloadManager, DownloadStatus
        from sftp_manager import SFTPManager
        
        # Crear managers
        sftp = SFTPManager()
        dm = DownloadManager(sftp_manager=sftp)
        
        # Añadir descarga
        download_id = dm.add_download(
            url="https://httpbin.org/delay/1",
            filename="test_transitions.json",
            remote_path="/tmp"
        )
        
        print("📊 Monitoreando transiciones de estado...")
        
        previous_status = None
        for i in range(10):  # 5 segundos
            downloads = dm.get_downloads_status()
            
            if downloads:
                current_status = downloads[0]['status']
                
                if current_status != previous_status:
                    print(f"   🔄 t={i*0.5}s: {previous_status} -> {current_status}")
                    previous_status = current_status
                
                # Verificar si es activa según la lógica de main.py
                is_active = current_status in ['Downloading', 'Processing', 'Queued']
                print(f"   📊 t={i*0.5}s: {current_status} ({'ACTIVA' if is_active else 'NO ACTIVA'})")
                
                if current_status in ['Completed', 'Error']:
                    print(f"   ✅ Descarga terminó con estado: {current_status}")
                    break
            
            time.sleep(0.5)
        
        dm.stop_downloads()
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba de transiciones: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_download_manager_logic():
    """Prueba la lógica interna del download manager"""
    print("\n🧠 PRUEBA DE LÓGICA INTERNA DEL DOWNLOAD MANAGER")
    print("=" * 60)
    
    try:
        from download_manager_requests import DownloadManager, DownloadStatus
        from sftp_manager import SFTPManager
        
        # Crear managers
        sftp = SFTPManager()
        dm = DownloadManager(sftp_manager=sftp)
        
        # Verificar estado inicial
        print(f"📊 Estado inicial del download manager:")
        print(f"   - is_running: {dm.is_running}")
        print(f"   - downloads: {len(dm.downloads)}")
        print(f"   - active_downloads: {len(dm.active_downloads)}")
        print(f"   - download_queue size: {dm.download_queue.qsize()}")
        
        # Añadir descarga
        download_id = dm.add_download(
            url="https://httpbin.org/delay/1",
            filename="test_logic.json",
            remote_path="/tmp"
        )
        
        print(f"\n📥 Después de añadir descarga:")
        print(f"   - is_running: {dm.is_running}")
        print(f"   - downloads: {len(dm.downloads)}")
        print(f"   - active_downloads: {len(dm.active_downloads)}")
        print(f"   - download_queue size: {dm.download_queue.qsize()}")
        
        # Verificar threads
        print(f"\n🧵 Estado de threads:")
        print(f"   - manager_thread: {dm.manager_thread}")
        print(f"   - remote_monitor_thread: {dm.remote_monitor_thread}")
        
        if dm.manager_thread:
            print(f"   - manager_thread.is_alive(): {dm.manager_thread.is_alive()}")
        
        if dm.remote_monitor_thread:
            print(f"   - remote_monitor_thread.is_alive(): {dm.remote_monitor_thread.is_alive()}")
        
        # Esperar un poco y verificar cambios
        time.sleep(2)
        
        print(f"\n📊 Después de 2 segundos:")
        downloads = dm.get_downloads_status()
        
        for download in downloads:
            print(f"   - {download['filename']}:")
            print(f"     * Estado: {download['status']}")
            print(f"     * Progreso: {download['progress']:.1f}%")
            print(f"     * PID remoto: {download['remote_pid']}")
            print(f"     * Log remoto: {download['remote_log_file']}")
        
        dm.stop_downloads()
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba de lógica: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Función principal de diagnóstico"""
    tests = [
        ("Monitoreo de descargas remotas", test_remote_download_monitoring),
        ("Transiciones de estado", test_status_transitions),
        ("Lógica interna del download manager", test_download_manager_logic),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 {test_name}")
        print(f"{'='*60}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: ÉXITO")
            else:
                print(f"❌ {test_name}: FALLÓ")
                
        except Exception as e:
            print(f"💥 {test_name}: CRASH - {e}")
            results.append((test_name, False))
    
    # Resumen
    print(f"\n{'='*60}")
    print("📊 RESUMEN DE DIAGNÓSTICO")
    print(f"{'='*60}")
    
    for test_name, result in results:
        status = "✅ ÉXITO" if result else "❌ FALLÓ"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"\n📊 Total: {passed}/{total} pruebas exitosas")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n💥 CRASH EN SCRIPT DE DIAGNÓSTICO: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
