#!/usr/bin/env python3
"""
Este script prueba la descarga remota para verificar que:
1. Se use la carpeta correcta (la carpeta SFTP actual)
2. El comando wget tenga el formato correcto (con --continue, --tries=0 y --user-agent="XCIPTV")
3. El archivo se guarde con el nombre correcto

Uso:
    python test_remote_download_fix.py
"""
import os
import sys
import time
import logging
import json
from getpass import getpass

# Configurar logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_remote_download_fix")

# Cargar configuración de conexión desde el prompt
def get_test_config():
    """Crear configuración de prueba interactiva"""
    config = {}
    print("\n==== Configuración de conexión SFTP para prueba ====")
    config["hostname"] = input("Host: ")
    config["port"] = int(input("Puerto [22]: ") or "22")
    config["username"] = input("Usuario: ")
    config["password"] = getpass("Contraseña: ")
    config["timeout"] = 30
    return config

# Importar directamente las clases relevantes
try:
    # Intentar importar directamente desde el proyecto
    from sftp_manager import SFTPManager
    from download_manager_requests import DownloadManager
except ImportError:
    print("⚠️ No se pudo importar las clases del proyecto")
    sys.exit(1)

def test_remote_download_fix():
    """Prueba que la descarga remota funcione correctamente"""
    print("\n===== PRUEBA DE DESCARGA REMOTA (SOLUCIÓN) =====")
    
    # Configuración para la prueba
    test_url = "https://upload.wikimedia.org/wikipedia/commons/c/ca/1x1.png"  # Archivo pequeño para test
    filename = "test_remote_download_fix.png"  # Nombre del archivo
    
    # Configurar la conexión SFTP
    config = get_test_config()
    print(f"\n🔑 Conectando a {config['hostname']}:{config['port']} como {config['username']}")
    
    # Inicializar el gestor SFTP
    sftp = SFTPManager()
    
    try:
        # Conectar al servidor
        connected = sftp.connect(**config)
        
        if not connected or not sftp.is_connected():
            print("❌ Error conectando al servidor SFTP")
            return False
        
        print("✅ Conexión establecida correctamente")
        
        # Obtener rutas remotas para probar
        test_paths = [
            "/tmp/test_path_1",
            "/tmp/test_path_2/nested"
        ]
        
        for remote_path in test_paths:
            print(f"\n📂 Probando con directorio destino: {remote_path}")
            
            # Asegurar que el directorio exista
            if not sftp.verify_directory_exists(remote_path):
                print(f"⚠️ Directorio no existe, creando: {remote_path}")
                try:
                    mkdir_cmd = f"mkdir -p {remote_path}"
                    if sftp.ssh_client:
                        stdin, stdout, stderr = sftp.ssh_client.exec_command(mkdir_cmd)
                        time.sleep(1)  # Esperar a que se cree el directorio
                except Exception as e:
                    print(f"❌ Error creando directorio: {e}")
                    continue
            
            # Crear un gestor de descargas y asignarle el gestor SFTP
            dm = DownloadManager()
            dm.set_sftp_manager(sftp)
            
            print(f"🌐 Iniciando descarga desde {test_url}")
            print(f"💾 Nombre de archivo: {filename}")
            
            # Agregar la descarga remota
            download_id = dm.add_remote_download(test_url, filename, remote_path)
            print(f"⏳ ID de descarga: {download_id}")
            
            # Esperar a que la descarga inicie
            print("⏳ Esperando a que la descarga inicie...")
            time.sleep(5)
            
            # Comprobar que el archivo está en la ubicación correcta
            print("\n📝 Verificando resultado...")
            if not sftp.ssh_client:
                print("❌ No hay conexión SSH activa")
                return False
                
            check_file_cmd = f'[ -f "{remote_path}/{filename}" ] && echo "EXISTS" || echo "NOT_EXISTS"'
            stdin, stdout, stderr = sftp.ssh_client.exec_command(check_file_cmd)
            exists = stdout.read().decode().strip()
            
            if exists == "EXISTS":
                print(f"✅ Archivo creado correctamente en {remote_path}/{filename}")
                
                # Mostrar info del archivo
                file_info_cmd = f'ls -lh "{remote_path}/{filename}"'
                stdin, stdout, stderr = sftp.ssh_client.exec_command(file_info_cmd)
                info = stdout.read().decode().strip()
                print(f"📄 Información del archivo: {info}")
                
                # Verificar el contenido del archivo
                head_cmd = f'hexdump -C "{remote_path}/{filename}" | head -3'
                stdin, stdout, stderr = sftp.ssh_client.exec_command(head_cmd)
                content = stdout.read().decode().strip()
                print(f"📄 Inicio del contenido:\n{content}")
                
                # Limpiar el archivo de prueba
                clean_cmd = f'rm -f "{remote_path}/{filename}"'
                stdin, stdout, stderr = sftp.ssh_client.exec_command(clean_cmd)
                print(f"🧹 Archivo de prueba eliminado")
            else:
                print(f"❌ El archivo NO se encontró en {remote_path}/{filename}")
                
                # Verificar si hay logs de wget
                check_logs_cmd = "ls -la /tmp/wget_log_* 2>/dev/null || echo 'No logs found'"
                stdin, stdout, stderr = sftp.ssh_client.exec_command(check_logs_cmd)
                logs = stdout.read().decode().strip()
                print(f"📜 Logs de wget: {logs}")
                
                if "No logs found" not in logs:
                    # Mostrar el contenido del log más reciente
                    show_log_cmd = "cat $(ls -t /tmp/wget_log_* | head -1) | tail -20"
                    stdin, stdout, stderr = sftp.ssh_client.exec_command(show_log_cmd)
                    log_content = stdout.read().decode().strip()
                    print(f"📜 Contenido del log más reciente:\n{log_content}")
                
                return False
        
        print("\n✅ PRUEBA COMPLETADA EXITOSAMENTE")
        return True
        
    except Exception as e:
        print(f"❌ Error durante la prueba: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print("\n🔌 Desconectando...")
        if sftp:
            sftp.disconnect()

if __name__ == "__main__":
    success = test_remote_download_fix()
    print(f"\n{'✅ PRUEBA EXITOSA' if success else '❌ PRUEBA FALLIDA'}")
    sys.exit(0 if success else 1)
