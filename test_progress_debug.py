#!/usr/bin/env python3
"""
Test script para verificar que el progreso y estado de las descargas funcionen correctamente.
Este script ejecuta la aplicación y verifica que:
1. Las descargas remotas se inicien correctamente
2. El progreso se actualice en tiempo real
3. Los estados no muestren errores falsos
4. La barra de progreso funcione correctamente
"""

import sys
import os
import time
import threading
import subprocess

# Añadir el directorio actual al path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_progress_system():
    """Test del sistema de progreso"""
    print("🧪 INICIANDO TEST DEL SISTEMA DE PROGRESO")
    print("=" * 60)
    
    try:
        # Importar módulos necesarios
        from download_manager_requests import DownloadManager, DownloadStatus
        from sftp_manager import SFTPManager
        
        print("✅ Módulos importados correctamente")
        
        # Crear instancias
        sftp_manager = SFTPManager()
        download_manager = DownloadManager(sftp_manager=sftp_manager)
        
        print("✅ Instancias creadas correctamente")
        
        # Verificar que los callbacks estén configurados
        print(f"📊 Callbacks de progreso: {len(download_manager.progress_callbacks)}")
        print(f"📊 Callbacks de estado: {len(download_manager.status_callbacks)}")
        
        # Añadir callback de test
        def test_progress_callback(download_id):
            print(f"🎯 TEST CALLBACK - Progreso cambiado para: {download_id}")
            
        def test_status_callback(download_id):
            print(f"🎯 TEST CALLBACK - Estado cambiado para: {download_id}")
            
        download_manager.progress_callbacks.append(test_progress_callback)
        download_manager.status_callbacks.append(test_status_callback)
        
        print("✅ Callbacks de test añadidos")
        
        # Simular una descarga
        print("\n🚀 SIMULANDO DESCARGA REMOTA")
        print("-" * 40)
        
        # Crear un download item de prueba
        from download_manager_requests import DownloadItem
        import uuid
        from datetime import datetime
        
        test_id = str(uuid.uuid4())
        test_item = DownloadItem(
            id=test_id,
            url="http://example.com/test.m3u8",
            filename="test_video.m3u8",
            remote_path="/tmp",
            local_path="/tmp/test_video.m3u8",
            status=DownloadStatus.DOWNLOADING,
            sftp_manager=sftp_manager,
            is_remote=True,
            start_time=datetime.now()
        )
        
        # Añadir al manager
        with download_manager.downloads_lock:
            download_manager.downloads[test_id] = test_item
            
        print(f"✅ Download de prueba creado: {test_id}")
        
        # Simular cambios de progreso
        print("\n📈 SIMULANDO CAMBIOS DE PROGRESO")
        print("-" * 40)
        
        for progress in [10, 25, 50, 75, 90, 100]:
            print(f"🔄 Simulando progreso: {progress}%")
            test_item.progress = progress
            download_manager._notify_progress_change(test_id)
            time.sleep(1)
            
        print("\n📊 VERIFICANDO ESTADO FINAL")
        print("-" * 40)
        
        # Obtener estado final
        downloads = download_manager.get_downloads_status()
        print(f"📊 Total de descargas: {len(downloads)}")
        
        for download in downloads:
            print(f"📁 {download['filename']}")
            print(f"   📊 Progreso: {download['progress']:.1f}%")
            print(f"   📋 Estado: {download['status']}")
            print(f"   📡 Remota: {download['is_remote']}")
            
        print("\n✅ TEST COMPLETADO EXITOSAMENTE")
        
    except Exception as e:
        print(f"❌ ERROR EN TEST: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    return True

def run_main_app():
    """Ejecuta la aplicación principal"""
    print("\n🚀 EJECUTANDO APLICACIÓN PRINCIPAL")
    print("=" * 60)
    print("👀 Observa la consola para ver los mensajes de debug del progreso")
    print("📱 Inicia una descarga remota y verifica que:")
    print("   1. Se muestren mensajes de progreso en la consola")
    print("   2. La barra de progreso se actualice")
    print("   3. No aparezcan estados de error falsos")
    print("=" * 60)
    
    try:
        # Ejecutar la aplicación principal
        from main import main
        main()
    except Exception as e:
        print(f"❌ Error ejecutando aplicación: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 TEST DE SISTEMA DE PROGRESO Y ESTADO")
    print("=" * 60)
    
    # Ejecutar test del sistema
    if test_progress_system():
        print("\n" + "=" * 60)
        print("✅ TESTS BÁSICOS PASARON - EJECUTANDO APLICACIÓN")
        print("=" * 60)
        
        # Ejecutar aplicación principal
        run_main_app()
    else:
        print("\n❌ TESTS BÁSICOS FALLARON")
        sys.exit(1)
