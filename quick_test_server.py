#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test rápido para servidor específico ***************
"""

import sys
import time
import socket
from sftp_manager import SFTPManager

def quick_server_test():
    """Test rápido del servidor específico"""
    server_ip = "***************"
    username = "root"
    password = "5dhCkm5Dz1BpWgxZpdBisrOVo"
    
    print(f"🎯 TEST RÁPIDO SERVIDOR: {server_ip}")
    print("=" * 50)
    
    # 1. Test de conectividad TCP básica
    print("\n1️⃣ Test de conectividad TCP...")
    start_time = time.time()
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((server_ip, 22))
        elapsed = time.time() - start_time
        
        if result == 0:
            print(f"✅ Puerto 22 ABIERTO en {elapsed:.2f}s")
            
            # Leer banner SSH
            try:
                sock.settimeout(5)
                banner = sock.recv(1024).decode('utf-8', errors='ignore').strip()
                if banner:
                    print(f"✅ Banner SSH: {banner}")
            except Exception as e:
                print(f"⚠️  No se pudo leer banner: {e}")
        else:
            print(f"❌ Puerto 22 CERRADO o BLOQUEADO (código {result}) en {elapsed:.2f}s")
            
        sock.close()
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ Error TCP en {elapsed:.2f}s: {e}")
    
    # 2. Test de conexión SFTP
    print(f"\n2️⃣ Test de conexión SFTP...")
    print(f"   Usuario: {username}")
    print(f"   Contraseña: {'*' * len(password)}")
    
    sftp = SFTPManager()
    start_time = time.time()
    
    try:
        result = sftp.connect(server_ip, 22, username, password, timeout=15)
        elapsed = time.time() - start_time
        
        if result:
            print(f"✅ ¡CONEXIÓN SFTP EXITOSA! en {elapsed:.2f}s")
            
            # Test operaciones básicas
            try:
                print("\n📁 Probando operaciones...")
                
                # Listar directorio raíz
                files = sftp.list_directory("/")
                print(f"✅ Listado /: {len(files)} elementos")
                
                # Mostrar algunos archivos
                if files:
                    print("   Primeros elementos:")
                    for f in files[:5]:
                        print(f"   - {f}")
                
                # Listar home del usuario
                try:
                    home_files = sftp.list_directory(f"/{username}")
                    print(f"✅ Listado /{username}: {len(home_files)} elementos")
                except Exception as e:
                    print(f"⚠️  No se pudo acceder a /{username}: {e}")
                
                # Test de directorio actual
                try:
                    if sftp.sftp_client and hasattr(sftp.sftp_client, 'getcwd'):
                        cwd = sftp.sftp_client.getcwd()
                        print(f"📂 Directorio actual: {cwd}")
                    else:
                        print(f"📂 Directorio actual: N/A")
                except Exception as e:
                    print(f"⚠️  No se pudo obtener directorio actual: {e}")
                
            except Exception as e:
                print(f"⚠️  Error en operaciones: {e}")
        else:
            print(f"❌ Falló la conexión SFTP en {elapsed:.2f}s")
            
    except Exception as e:
        elapsed = time.time() - start_time
        error_msg = str(e)
        print(f"❌ Error SFTP en {elapsed:.2f}s: {error_msg}")
        
        # Análisis detallado del error
        print(f"\n🔍 ANÁLISIS DEL ERROR:")
        if "Authentication failed" in error_msg:
            print("❌ AUTENTICACIÓN FALLIDA")
            print("   - Verificar usuario y contraseña")
            print("   - El usuario podría no tener acceso SFTP")
            print("   - SSH podría estar configurado solo para claves")
        elif "Connection refused" in error_msg:
            print("❌ CONEXIÓN RECHAZADA")
            print("   - SSH daemon no está activo")
            print("   - Puerto 22 cerrado o bloqueado")
            print("   - Firewall bloqueando conexiones")
        elif "timeout" in error_msg.lower() or "timed out" in error_msg:
            print("❌ TIMEOUT DE CONEXIÓN")
            print("   - Firewall bloqueando paquetes")
            print("   - Red lenta o sobrecargada")
            print("   - Servidor no responde")
        elif "Name or service not known" in error_msg:
            print("❌ ERROR DE DNS/IP")
            print("   - IP incorrecta")
            print("   - Problema de resolución DNS")
        elif "No route to host" in error_msg:
            print("❌ SIN RUTA AL HOST")
            print("   - Problema de enrutamiento de red")
            print("   - IP inalcanzable desde tu ubicación")
        elif "Connection reset" in error_msg:
            print("❌ CONEXIÓN REINICIADA")
            print("   - Servidor rechazó la conexión activamente")
            print("   - Posible ban por IP o fail2ban")
        else:
            print(f"❓ ERROR DESCONOCIDO: {error_msg}")
            
    finally:
        sftp.disconnect()
    
    print("\n" + "=" * 50)
    print("🏁 TEST COMPLETADO")
    
    return True

if __name__ == "__main__":
    print("🚀 TEST RÁPIDO SERVIDOR ESPECÍFICO")
    print("Verificando conectividad a ***************")
    print()
    
    try:
        quick_server_test()
    except KeyboardInterrupt:
        print("\n🛑 Test interrumpido por el usuario")
    except Exception as e:
        print(f"\n❌ Error general: {e}")
    
    print("\n💡 RECOMENDACIONES SI FALLA:")
    print("1. Verificar que SSH esté activo: systemctl status sshd")
    print("2. Revisar firewall: ufw status o iptables -L")
    print("3. Comprobar logs: tail -f /var/log/auth.log")
    print("4. Probar desde otra ubicación o VPN")
    print("5. Verificar configuración SSH: /etc/ssh/sshd_config")
