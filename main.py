#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
M3U Manager with SFTP Download - Aplicación principal
Gestiona listas M3U con protocolo Xtream Codes y descarga vía SFTP
"""

import threading
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import json
import queue
import time  # Añadido para usar time.sleep en los hilos

from m3u_parser import M3UParser
from sftp_manager import SFTPManager
from download_manager_requests import DownloadManager
from ui_components import UIComponents
from dark_theme import apply_dark_theme

class M3UDownloadApp:
    def __init__(self, root):
        self.root = root
        root.title("M3U Manager with SFTP Download")
        root.geometry("1400x800")
        root.minsize(1200, 700)
        
        # Control de ciclo de vida de hilos
        self.refresh_thread = None
        self.is_refresh_running = False
        self.download_thread = None
        self.is_download_running = False
        
        # Configuration file
        self.config_file = "config.json"
        
        # Thread communication
        self.update_queue = queue.Queue()
        self.is_running = True  # Flag para controlar hilos
        
        # Managers
        self.m3u_parser = M3UParser()
        self.sftp_manager = SFTPManager()
        self.download_manager = DownloadManager(sftp_manager=self.sftp_manager)

        # Configurar callbacks para actualizaciones de progreso más fluidas
        self.download_manager.progress_callbacks.append(self._on_download_progress_change)
        self.download_manager.status_callbacks.append(self._on_download_status_change)
        
        # Content data
        self.m3u_content = []  # Lista para almacenar contenido M3U
        
        # UI Components
        self.ui = UIComponents(self.root)
        
        # Aplicar tema oscuro NVIDIA
        self.theme_manager = apply_dark_theme(self.root)
        
        self.setup_ui()
        self.setup_bindings()
        self.load_config()  # Mover después de setup_ui
        self.start_update_thread()
        
        # Configurar cierre limpio
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def setup_ui(self):
        """Configura la interfaz de usuario"""
        # Frame principal con división horizontal
        main_paned = ttk.PanedWindow(self.root, orient='horizontal')
        main_paned.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Panel izquierdo - M3U
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=1)
        
        # Panel derecho - SFTP
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)
        
        self.setup_m3u_panel(left_frame)
        self.setup_sftp_panel(right_frame)
        self.setup_bottom_panel()
        
    def setup_m3u_panel(self, parent):
        """Configura el panel de listas M3U"""
        # Título
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(title_frame, text="Listas M3U", font=('Arial', 12, 'bold')).pack(side='left')
        
        # Botones de control M3U
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Button(control_frame, text="Cargar M3U", style="Accent.TButton",
                  command=self.load_m3u_file).pack(side='left', padx=2)
        ttk.Button(control_frame, text="Cargar URL", 
                  command=self.load_m3u_url).pack(side='left', padx=2)
        ttk.Button(control_frame, text="Refrescar", 
                  command=self.refresh_m3u).pack(side='left', padx=2)
        
        # Frame para configuración Xtream Codes
        xtream_frame = ttk.LabelFrame(parent, text="Xtream Codes API")
        xtream_frame.pack(fill='x', padx=5, pady=5)
        
        # URL del servidor
        url_frame = ttk.Frame(xtream_frame)
        url_frame.pack(fill='x', padx=5, pady=2)
        ttk.Label(url_frame, text="URL:").pack(side='left')
        self.xtream_url_var = tk.StringVar()
        ttk.Entry(url_frame, textvariable=self.xtream_url_var, width=40).pack(side='left', fill='x', expand=True, padx=5)
        
        # Usuario y contraseña
        auth_frame = ttk.Frame(xtream_frame)
        auth_frame.pack(fill='x', padx=5, pady=2)
        ttk.Label(auth_frame, text="Usuario:").pack(side='left')
        self.xtream_user_var = tk.StringVar()
        ttk.Entry(auth_frame, textvariable=self.xtream_user_var, width=15).pack(side='left', padx=5)
        ttk.Label(auth_frame, text="Contraseña:").pack(side='left')
        self.xtream_pass_var = tk.StringVar()
        ttk.Entry(auth_frame, textvariable=self.xtream_pass_var, width=15, show="*").pack(side='left', padx=5)
        ttk.Button(auth_frame, text="Conectar", style="Accent.TButton", command=self.connect_xtream).pack(side='left', padx=5)
        
        # Filtros
        filter_frame = ttk.Frame(parent)
        filter_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(filter_frame, text="Filtrar:").pack(side='left')
        self.filter_var = tk.StringVar()
        self.filter_var.trace('w', self.filter_m3u_content)
        ttk.Entry(filter_frame, textvariable=self.filter_var, width=30).pack(side='left', padx=5, fill='x', expand=True)
        
        # Tipo de contenido
        ttk.Label(filter_frame, text="Tipo:").pack(side='left')
        self.content_type_var = tk.StringVar(value="all")
        type_combo = ttk.Combobox(filter_frame, textvariable=self.content_type_var, 
                                 values=["all", "live", "movie", "series"], state="readonly", width=10)
        type_combo.pack(side='left', padx=5)
        type_combo.bind('<<ComboboxSelected>>', self.filter_m3u_content)
        
        # Treeview para mostrar contenido M3U
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient='vertical')
        h_scrollbar = ttk.Scrollbar(tree_frame, orient='horizontal')
        
        # Treeview
        columns = ('Name', 'Group', 'Type', 'Duration', 'URL')
        self.m3u_tree = ttk.Treeview(tree_frame, columns=columns, show='headings',
                                    yscrollcommand=v_scrollbar.set,
                                    xscrollcommand=h_scrollbar.set)
        
        # Configurar columnas
        self.m3u_tree.heading('Name', text='Nombre')
        self.m3u_tree.heading('Group', text='Grupo')
        self.m3u_tree.heading('Type', text='Tipo')
        self.m3u_tree.heading('Duration', text='Duración')
        self.m3u_tree.heading('URL', text='URL')
        
        self.m3u_tree.column('Name', width=250)
        self.m3u_tree.column('Group', width=150)
        self.m3u_tree.column('Type', width=80)
        self.m3u_tree.column('Duration', width=80)
        self.m3u_tree.column('URL', width=200)
        
        # Configurar scrollbars
        v_scrollbar.config(command=self.m3u_tree.yview)
        h_scrollbar.config(command=self.m3u_tree.xview)
        
        # Pack elementos
        self.m3u_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')
        
    def setup_sftp_panel(self, parent):
        """Configura el panel SFTP"""
        # Título
        title_frame = ttk.Frame(parent)
        title_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Label(title_frame, text="Navegador SFTP", font=('Arial', 12, 'bold')).pack(side='left')
        
        # Configuración de conexión SFTP
        conn_frame = ttk.LabelFrame(parent, text="Conexión SFTP")
        conn_frame.pack(fill='x', padx=5, pady=5)
        
        # Primera fila - Host y Puerto
        host_frame = ttk.Frame(conn_frame)
        host_frame.pack(fill='x', padx=5, pady=2)
        ttk.Label(host_frame, text="Host:").pack(side='left')
        self.sftp_host_var = tk.StringVar()
        ttk.Entry(host_frame, textvariable=self.sftp_host_var, width=25).pack(side='left', padx=5, fill='x', expand=True)
        ttk.Label(host_frame, text="Puerto:").pack(side='left')
        self.sftp_port_var = tk.StringVar(value="22")
        ttk.Entry(host_frame, textvariable=self.sftp_port_var, width=8).pack(side='left', padx=5)
        
        # Segunda fila - Usuario y contraseña
        auth_frame = ttk.Frame(conn_frame)
        auth_frame.pack(fill='x', padx=5, pady=2)
        ttk.Label(auth_frame, text="Usuario:").pack(side='left')
        self.sftp_user_var = tk.StringVar()
        ttk.Entry(auth_frame, textvariable=self.sftp_user_var, width=15).pack(side='left', padx=5)
        ttk.Label(auth_frame, text="Contraseña:").pack(side='left')
        self.sftp_pass_var = tk.StringVar()
        ttk.Entry(auth_frame, textvariable=self.sftp_pass_var, width=15, show="*").pack(side='left', padx=5)
        
        # Botones de conexión
        btn_frame = ttk.Frame(conn_frame)
        btn_frame.pack(fill='x', padx=5, pady=2)
        ttk.Button(btn_frame, text="Conectar", style="Accent.TButton", command=self.connect_sftp).pack(side='left', padx=2)
        ttk.Button(btn_frame, text="Desconectar", command=self.disconnect_sftp).pack(side='left', padx=2)
        ttk.Button(btn_frame, text="🧪 Test Demo", command=self.test_demo_sftp).pack(side='left', padx=2)
        self.sftp_status_label = ttk.Label(btn_frame, text="Desconectado", style="Error.TLabel")
        self.sftp_status_label.pack(side='left', padx=10)
        
        # Navegación de carpetas
        nav_frame = ttk.Frame(parent)
        nav_frame.pack(fill='x', padx=5, pady=5)
        
        ttk.Button(nav_frame, text="↑ Arriba", command=self.go_up_directory).pack(side='left', padx=2)
        ttk.Button(nav_frame, text="🏠 Home", command=self.go_home_directory).pack(side='left', padx=2)
        ttk.Button(nav_frame, text="📁 Nueva Carpeta", command=self.create_directory).pack(side='left', padx=2)
        ttk.Button(nav_frame, text="🗑️ Eliminar", command=self.delete_selected).pack(side='left', padx=2)
        
        # Ruta actual
        path_frame = ttk.Frame(parent)
        path_frame.pack(fill='x', padx=5, pady=5)
        ttk.Label(path_frame, text="Ruta:").pack(side='left')
        self.current_path_var = tk.StringVar(value="/")
        ttk.Entry(path_frame, textvariable=self.current_path_var, state="readonly").pack(side='left', fill='x', expand=True, padx=5)
        ttk.Button(path_frame, text="Ir", command=self.navigate_to_path).pack(side='left')
        
        # Treeview para archivos remotos
        files_frame = ttk.Frame(parent)
        files_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Scrollbars para archivos
        files_v_scrollbar = ttk.Scrollbar(files_frame, orient='vertical')
        files_h_scrollbar = ttk.Scrollbar(files_frame, orient='horizontal')
        
        # Treeview archivos
        files_columns = ('Name', 'Type', 'Size', 'Modified')
        self.files_tree = ttk.Treeview(files_frame, columns=files_columns, show='headings',
                                      yscrollcommand=files_v_scrollbar.set,
                                      xscrollcommand=files_h_scrollbar.set)
        
        # Configurar columnas archivos
        self.files_tree.heading('Name', text='Nombre')
        self.files_tree.heading('Type', text='Tipo')
        self.files_tree.heading('Size', text='Tamaño')
        self.files_tree.heading('Modified', text='Modificado')
        
        self.files_tree.column('Name', width=200)
        self.files_tree.column('Type', width=80)
        self.files_tree.column('Size', width=100)
        self.files_tree.column('Modified', width=150)
        
        # Configurar scrollbars archivos
        files_v_scrollbar.config(command=self.files_tree.yview)
        files_h_scrollbar.config(command=self.files_tree.xview)
        
        # Pack elementos archivos
        self.files_tree.pack(side='left', fill='both', expand=True)
        files_v_scrollbar.pack(side='right', fill='y')
        files_h_scrollbar.pack(side='bottom', fill='x')
        
    def setup_bottom_panel(self):
        """Configura el panel inferior con descargas"""
        # Frame inferior para descargas
        bottom_frame = ttk.Frame(self.root)
        bottom_frame.pack(side='bottom', fill='x', padx=5, pady=5)
        
        # Panel de control de descargas
        download_control_frame = ttk.LabelFrame(bottom_frame, text="Control de Descargas")
        download_control_frame.pack(fill='x', pady=5)
        
        # Botones de control
        control_btn_frame = ttk.Frame(download_control_frame)
        control_btn_frame.pack(fill='x', padx=5, pady=2)
        
        ttk.Button(control_btn_frame, text="▶️ Iniciar Descargas", style="Accent.TButton",
                  command=self.start_downloads).pack(side='left', padx=2)
        ttk.Button(control_btn_frame, text="⏸️ Pausar Descargas", 
                  command=self.pause_downloads).pack(side='left', padx=2)
        ttk.Button(control_btn_frame, text="⏹️ Detener Descargas", 
                  command=self.stop_downloads).pack(side='left', padx=2)
        ttk.Button(control_btn_frame, text="🗑️ Limpiar Cola", 
                  command=self.clear_queue).pack(side='left', padx=2)
        
        # Estadísticas
        stats_frame = ttk.Frame(download_control_frame)
        stats_frame.pack(fill='x', padx=5, pady=2)
        
        self.stats_label = ttk.Label(stats_frame, text="En cola: 0 | Activas: 0 | Completadas: 0 | Errores: 0")
        self.stats_label.pack(side='left')
        
        # Progreso general
        progress_frame = ttk.Frame(download_control_frame)
        progress_frame.pack(fill='x', padx=5, pady=2)
        
        ttk.Label(progress_frame, text="Progreso General:").pack(side='left')
        self.general_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.general_progress.pack(side='left', fill='x', expand=True, padx=5)
        self.general_progress_label = ttk.Label(progress_frame, text="0%")
        self.general_progress_label.pack(side='left')
        
        # Lista de descargas
        downloads_frame = ttk.LabelFrame(bottom_frame, text="Cola de Descargas")
        downloads_frame.pack(fill='both', expand=True, pady=5)
        
        # Treeview para descargas
        downloads_columns = ('File', 'Status', 'Progress', 'Speed', 'ETA', 'Size')
        self.downloads_tree = ttk.Treeview(downloads_frame, columns=downloads_columns, show='headings', height=6)
        
        # Configurar columnas descargas
        self.downloads_tree.heading('File', text='Archivo')
        self.downloads_tree.heading('Status', text='Estado')
        self.downloads_tree.heading('Progress', text='Progreso')
        self.downloads_tree.heading('Speed', text='Velocidad')
        self.downloads_tree.heading('ETA', text='ETA')
        self.downloads_tree.heading('Size', text='Tamaño')
        
        self.downloads_tree.column('File', width=300)
        self.downloads_tree.column('Status', width=100)
        self.downloads_tree.column('Progress', width=100)
        self.downloads_tree.column('Speed', width=100)
        self.downloads_tree.column('ETA', width=100)
        self.downloads_tree.column('Size', width=100)
        
        downloads_scrollbar = ttk.Scrollbar(downloads_frame, orient='vertical', command=self.downloads_tree.yview)
        self.downloads_tree.configure(yscrollcommand=downloads_scrollbar.set)
        
        self.downloads_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        downloads_scrollbar.pack(side='right', fill='y')
        
        # Actualizar visualización de descargas
        self.update_downloads_display()

    def setup_bindings(self):
        """Configura los eventos y bindings"""
        # Doble click en archivos remotos para navegar
        self.files_tree.bind('<Double-1>', self.on_double_click_file)
        
        # Click derecho en M3U tree para menú contextual
        self.m3u_tree.bind('<Button-3>', self.show_m3u_context_menu)
        
        # Click derecho en files tree para menú contextual
        self.files_tree.bind('<Button-3>', self.show_files_context_menu)
        
        # Selección múltiple en M3U tree
        self.m3u_tree.bind('<Control-Button-1>', self.on_ctrl_click_m3u)
        self.m3u_tree.bind('<Shift-Button-1>', self.on_shift_click_m3u)
        
    def start_update_thread(self):
        """Inicia el hilo de actualización de la UI"""
        def update_ui():
            while self.is_running:
                try:
                    # Usar timeout más corto para mejor responsividad
                    update_func = self.update_queue.get(timeout=0.1)
                    if self.is_running:  # Verificar que aún estamos corriendo
                        self.root.after_idle(update_func)  # Ejecutar en el hilo principal de Tkinter
                    self.update_queue.task_done()
                except queue.Empty:
                    # Pequeña pausa para no consumir CPU
                    import time
                    time.sleep(0.05)
                except Exception as e:
                    if self.is_running:  # Solo mostrar errores si aún estamos corriendo
                        print(f"Error en update thread: {e}")
            print("🔚 Hilo de actualización UI terminado")
        
        update_thread = threading.Thread(target=update_ui, daemon=True)
        update_thread.start()

    def on_closing(self):
        """Maneja el cierre limpio de la aplicación"""
        print("🔄 Cerrando aplicación...")

        try:
            # Detener todos los flags de hilos
            self.is_running = False
            self.is_download_running = False
            self.is_refresh_running = False

            # Guardar configuración
            self.save_config()

            # Detener gestor de descargas
            self.download_manager.stop_downloads()

            # Desconectar SFTP
            self.sftp_manager.disconnect()

            # Esperar un momento para que los threads terminen
            import time
            time.sleep(0.3)

            print("✅ Recursos liberados correctamente")

        except Exception as e:
            print(f"⚠️ Error durante cierre: {e}")

        finally:
            try:
                self.root.quit()  # Salir del mainloop primero
                self.root.destroy()  # Luego destruir la ventana
            except:
                pass
        
    # Métodos de carga de configuración
    def load_config(self):
        """Carga la configuración desde archivo"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                # Cargar configuración Xtream Codes
                if 'xtream' in config:
                    self.xtream_url_var.set(config['xtream'].get('url', ''))
                    self.xtream_user_var.set(config['xtream'].get('user', ''))
                    self.xtream_pass_var.set(config['xtream'].get('password', ''))
                
                # Cargar configuración SFTP
                if 'sftp' in config:
                    self.sftp_host_var.set(config['sftp'].get('host', ''))
                    self.sftp_port_var.set(config['sftp'].get('port', '22'))
                    self.sftp_user_var.set(config['sftp'].get('user', ''))
                    
        except Exception as e:
            print(f"Error cargando configuración: {e}")
    
    def save_config(self):
        """Guarda la configuración actual"""
        try:
            config = {
                'xtream': {
                    'url': self.xtream_url_var.get(),
                    'user': self.xtream_user_var.get(),
                    'password': self.xtream_pass_var.get()
                },
                'sftp': {
                    'host': self.sftp_host_var.get(),
                    'port': self.sftp_port_var.get(),
                    'user': self.sftp_user_var.get()
                }
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Error guardando configuración: {e}")
    
    # Métodos de M3U
    def load_m3u_file(self):
        """Carga un archivo M3U local"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar archivo M3U",
            filetypes=[("Archivos M3U", "*.m3u *.m3u8"), ("Todos los archivos", "*.*")]
        )
        
        if file_path:
            threading.Thread(target=self._load_m3u_file_thread, args=(file_path,), daemon=True).start()
    
    def _load_m3u_file_thread(self, file_path):
        """Thread para cargar archivo M3U"""
        try:
            content = self.m3u_parser.parse_file(file_path)
            self.update_queue.put(lambda: self._update_m3u_content(content))
        except Exception as e:
            self.update_queue.put(lambda: messagebox.showerror("Error", f"Error cargando archivo M3U: {e}"))
    
    def load_m3u_url(self):
        """Carga una URL M3U"""
        from tkinter import simpledialog
        url = simpledialog.askstring("URL M3U", "Ingrese la URL del archivo M3U:")
        
        if url:
            threading.Thread(target=self._load_m3u_url_thread, args=(url,), daemon=True).start()
    
    def _load_m3u_url_thread(self, url):
        """Thread para cargar URL M3U"""
        try:
            content = self.m3u_parser.parse_url(url)
            self.update_queue.put(lambda: self._update_m3u_content(content))
        except Exception as e:
            self.update_queue.put(lambda: messagebox.showerror("Error", f"Error cargando URL M3U: {e}"))
    
    def connect_xtream(self):
        """Conecta con el API de Xtream Codes"""
        url = self.xtream_url_var.get().strip()
        user = self.xtream_user_var.get().strip()
        password = self.xtream_pass_var.get().strip()
        
        if not all([url, user, password]):
            messagebox.showerror("Error", "Complete todos los campos de Xtream Codes")
            return
        
        threading.Thread(target=self._connect_xtream_thread, args=(url, user, password), daemon=True).start()
    
    def _connect_xtream_thread(self, url, user, password):
        """Thread para conectar con Xtream Codes"""
        try:
            content = self.m3u_parser.parse_xtream_codes(url, user, password)
            self.update_queue.put(lambda: self._update_m3u_content(content))
            self.update_queue.put(lambda: self.save_config())
        except Exception as e:
            self.update_queue.put(lambda: messagebox.showerror("Error", f"Error conectando con Xtream Codes: {e}"))
    
    def _update_m3u_content(self, content):
        """Actualiza el contenido M3U en la UI"""
        self.m3u_content = content
        self.filter_m3u_content()
    
    def filter_m3u_content(self, *args):
        """Filtra el contenido M3U según los criterios"""
        # Limpiar tree
        for item in self.m3u_tree.get_children():
            self.m3u_tree.delete(item)
        
        if not self.m3u_content:
            return
        
        filter_text = self.filter_var.get().lower()
        content_type = self.content_type_var.get()
        
        for item in self.m3u_content:
            # Filtrar por texto
            if filter_text and filter_text not in item.get('name', '').lower():
                continue
            
            # Filtrar por tipo
            if content_type != "all" and item.get('type', '') != content_type:
                continue
            
            # Insertar en tree
            self.m3u_tree.insert('', 'end', values=(
                item.get('name', ''),
                item.get('group', ''),
                item.get('type', ''),
                item.get('duration', ''),
                item.get('url', '')[:50] + '...' if len(item.get('url', '')) > 50 else item.get('url', '')
            ), tags=(str(len(self.m3u_tree.get_children())),))
    
    def refresh_m3u(self):
        """Refresca el contenido M3U actual"""
        # Aquí podrías implementar lógica para refrescar
        self.filter_m3u_content()
    
    # Métodos SFTP
    def connect_sftp(self):
        """Conecta con el servidor SFTP"""
        host = self.sftp_host_var.get().strip()
        port = int(self.sftp_port_var.get() or 22)
        user = self.sftp_user_var.get().strip()
        password = self.sftp_pass_var.get().strip()
        
        if not all([host, user, password]):
            messagebox.showerror("Error", "Complete todos los campos SFTP")
            return
        
        threading.Thread(target=self._connect_sftp_thread, args=(host, port, user, password), daemon=True).start()
    
    def _connect_sftp_thread(self, host, port, user, password):
        """Thread para conectar SFTP usando el método directo que sabemos que funciona"""
        try:
            # Actualizar estado a "Conectando..."
            def update_connecting_status():
                self.sftp_status_label.config(text="Conectando...")
                # Aplicar color de advertencia desde el tema
                if hasattr(self.theme_manager, 'theme'):
                    self.sftp_status_label.config(foreground=self.theme_manager.theme.WARNING_COLOR)
            self.update_queue.put(update_connecting_status)

            # Validar datos antes de conectar
            if not host or not user or not password:
                raise Exception("Faltan datos de conexión")

            print(f"🔄 Iniciando conexión SFTP a {user}@{host}:{port}")

            # Usar timeout más agresivo para evitar long threads
            connection_timeout = 10  # Reducido de 15 a 10 segundos

            # Usar el método connect_direct con timeout más corto
            success = self.sftp_manager.connect_direct(host, port, user, password, timeout=connection_timeout)

            if success:
                print("✅ Conexión SFTP exitosa")
                self.update_queue.put(lambda: self._on_sftp_connected())
                self.update_queue.put(lambda: self.save_config())
                self.update_queue.put(lambda: messagebox.showinfo("Éxito", "Conexión SFTP establecida correctamente"))
            else:
                raise Exception("Falló la autenticación SFTP")
                
        except Exception as e:
            error_msg = str(e)
            print(f"❌ Error SFTP: {error_msg}")
            
            # Limpiar recursos agresivamente
            try:
                self.sftp_manager.disconnect()
                self.sftp_manager._cleanup_connection()
            except:
                pass
            
            # Determinar tipo de error para mensaje más específico
            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                detailed_msg = f"Timeout conectando a {host}:{port}\n\n"
                detailed_msg += "El servidor no responde en tiempo límite (10s).\n\n"
                detailed_msg += "Posibles causas:\n"
                detailed_msg += "• El servidor está apagado o inaccesible\n"
                detailed_msg += "• El puerto está bloqueado por firewall\n"
                detailed_msg += "• La dirección IP o host es incorrecta\n"
                detailed_msg += "• Conexión de red muy lenta o inestable\n"
                detailed_msg += "• El servidor SSH está sobrecargado"
            elif "auth" in error_msg.lower() or "permission" in error_msg.lower() or "credenciales" in error_msg.lower():
                detailed_msg = f"Error de autenticación en {host}:{port}\n\n"
                detailed_msg += "Credenciales incorrectas. Verifica:\n"
                detailed_msg += "• Nombre de usuario correcto\n"
                detailed_msg += "• Contraseña correcta\n"
                detailed_msg += "• El usuario tiene permisos SSH/SFTP\n"
                detailed_msg += "• La cuenta no está bloqueada"
            elif "refused" in error_msg.lower():
                detailed_msg = f"Conexión rechazada por {host}:{port}\n\n"
                detailed_msg += "El servidor rechazó la conexión:\n"
                detailed_msg += "• Verifica que el puerto SSH sea correcto (¿22?)\n"
                detailed_msg += "• El servicio SSH puede estar detenido\n"
                detailed_msg += "• El puerto puede estar filtrado por firewall\n"
                detailed_msg += "• La IP puede estar en lista negra"
            elif "resolver" in error_msg.lower() or "gaierror" in error_msg.lower():
                detailed_msg = f"No se puede resolver '{host}'\n\n"
                detailed_msg += "El hostname no se puede resolver:\n"
                detailed_msg += "• Verifica que el hostname sea correcto\n"
                detailed_msg += "• Problemas de DNS\n"
                detailed_msg += "• Prueba usar la IP directamente\n"
                detailed_msg += "• Verifica conectividad a internet"
            else:
                detailed_msg = f"Error conectando SFTP a {host}:{port}\n\n"
                detailed_msg += f"Error: {error_msg}\n\n"
                detailed_msg += "Posibles soluciones:\n"
                detailed_msg += "• Verificar host/IP y puerto\n"
                detailed_msg += "• Comprobar usuario y contraseña\n"
                detailed_msg += "• Verificar conectividad de red\n"
                detailed_msg += "• Usar el botón '🧪 Test Demo' para probar"
            
            def update_error_status():
                self.sftp_status_label.config(text="Error de conexión")
                # Aplicar color de error desde el tema
                if hasattr(self.theme_manager, 'theme'):
                    self.sftp_status_label.config(foreground=self.theme_manager.theme.ERROR_COLOR)
            self.update_queue.put(lambda: messagebox.showerror("Error de Conexión SFTP", detailed_msg))
            self.update_queue.put(update_error_status)
        finally:
            print("🔚 Thread de conexión SFTP terminado")
    
    def _on_sftp_connected(self):
        """Callback cuando se conecta SFTP"""
        self.sftp_status_label.config(text="Conectado")
        # Aplicar color de éxito desde el tema
        if hasattr(self.theme_manager, 'theme'):
            self.sftp_status_label.config(foreground=self.theme_manager.theme.SUCCESS_COLOR)
        self.refresh_files()
    
    def disconnect_sftp(self):
        """Desconecta del servidor SFTP"""
        print("👋 Desconectando del servidor SFTP...")
        
        # Detener todos los hilos activos
        self.stop_refresh_thread()
        self.is_download_running = False
        if self.download_thread and self.download_thread.is_alive():
            print("🛑 Esperando que termine el hilo de descarga...")
            self.download_thread.join(timeout=5.0)
        
        # Limpiar UI
        self.files_tree.delete(*self.files_tree.get_children())
        self.current_path_var.set("/")
        
        # Desconectar SFTP
        if self.sftp_manager:
            self.sftp_manager.disconnect()
            
        # Actualizar estado de botones si existen
        try:
            if hasattr(self, 'connect_button'):
                self.connect_button.config(state='normal')
            if hasattr(self, 'disconnect_button'):
                self.disconnect_button.config(state='disabled')
        except Exception as e:
            print(f"⚠️ Advertencia al actualizar botones: {e}")
            
        print("✅ Desconexión completada")
    
    def refresh_files(self):
        """Refresca la lista de archivos"""
        if not self.sftp_manager.is_connected():
            return
            
        # Detener el hilo anterior si existe
        self.stop_refresh_thread()
        
        # Crear y arrancar nuevo hilo
        self.is_refresh_running = True
        self.refresh_thread = threading.Thread(target=self._refresh_files_thread, daemon=True)
        self.refresh_thread.start()
        
        # Actualizar UI para mostrar que estamos cargando
        self.files_tree.delete(*self.files_tree.get_children())
        self.files_tree.insert('', 'end', values=('Cargando...', '', '', ''))
    
    def stop_refresh_thread(self):
        """Detiene el hilo de refresco si está corriendo"""
        if self.refresh_thread and self.refresh_thread.is_alive():
            print("🛑 Deteniendo hilo de refresco anterior...")
            self.is_refresh_running = False
            # Timeout más corto para evitar bloqueos
            self.refresh_thread.join(timeout=1.0)  # Reducido de 2.0 a 1.0 segundos
            if self.refresh_thread.is_alive():
                print("⚠️ Hilo de refresco no terminó en tiempo límite, continuando...")
                # No bloquear la UI, el hilo terminará eventualmente
    
    def _refresh_files_thread(self):
        """Thread para refrescar archivos"""
        try:
            if not self.is_refresh_running:
                return
                
            current_path = self.current_path_var.get()
            print(f"🔄 Listando archivos en: {current_path}")
            
            # Verificar conexión antes de listar
            if not self.sftp_manager.is_connected():
                raise Exception("Conexión SFTP perdida")
            
            # Verificar que el directorio existe
            if not self.sftp_manager.verify_directory_exists(current_path):
                # Si el directorio no existe, volver a la raíz
                error_msg = f"La ruta '{current_path}' no existe o no es accesible. Volviendo a la raíz."
                print(f"⚠️ {error_msg}")
                self.update_queue.put(lambda: messagebox.showerror("Error", error_msg))
                self.update_queue.put(lambda: self.current_path_var.set("/"))
                
                # Intentar listar la raíz
                current_path = "/"
                print(f"🔄 Volviendo a la raíz: {current_path}")
            
            if not self.is_refresh_running:  # Verificar si debemos continuar
                return
                
            try:
                # Intentar listar los archivos con timeout
                files = self.sftp_manager.list_directory(current_path)
                
                if not self.is_refresh_running:  # Verificar nuevamente antes de actualizar UI
                    return
                    
                print(f"✅ {len(files)} archivos encontrados")
                self.update_queue.put(lambda: self._update_files_tree(files))
                
            except Exception as e:
                # Si falla en la ruta actual, intentar con la raíz como último recurso
                if not self.is_refresh_running:
                    return
                if current_path != "/":
                    error_msg = f"Error listando '{current_path}': {e}. Volviendo a la raíz."
                    print(f"⚠️ {error_msg}")
                    self.update_queue.put(lambda: messagebox.showerror("Error", error_msg))
                    self.update_queue.put(lambda: self.current_path_var.set("/"))
                    
                    # Intentar listar la raíz
                    try:
                        files = self.sftp_manager.list_directory("/")
                        self.update_queue.put(lambda: self._update_files_tree(files))
                    except Exception as e2:
                        raise Exception(f"No se pudo listar ni siquiera la raíz: {e2}")
                else:
                    raise  # Re-lanzar la excepción si ya estamos en la raíz
                
        except Exception as e:
            error_msg = f"Error listando archivos: {e}"
            print(f"❌ {error_msg}")
            self.update_queue.put(lambda: messagebox.showerror("Error", error_msg))
            # No desconectar automáticamente para permitir reintentos
            # self.update_queue.put(lambda: self.disconnect_sftp())
        finally:
            pass
            print("🔚 Thread de refresco de archivos terminado")
    
    def _update_files_tree(self, files):
        """Actualiza el tree de archivos"""
        self._clear_files_tree()
        
        for file_info in files:
            self.files_tree.insert('', 'end', values=(
                file_info['name'],
                file_info['type'],
                file_info['size'],
                file_info['modified']
            ))
    
    def _clear_files_tree(self):
        """Limpia el tree de archivos"""
        for item in self.files_tree.get_children():
            self.files_tree.delete(item)
    
    # Navegación de archivos
    def on_double_click_file(self, event):
        """Maneja doble click en archivo"""
        selection = self.files_tree.selection()
        if not selection:
            return
            
        # Detener cualquier refresco en curso
        self.stop_refresh_thread()
        
        item = self.files_tree.item(selection[0])
        values = item['values']
        
        # Verificar que hay valores y que no sean None
        if not values or len(values) < 2:
            return
            
        file_name = str(values[0])  # Convertir explícitamente a string
        file_type = str(values[1])
        
        # Detectar si es un directorio (puede ser "directory" o "Directorio")
        is_directory = file_type.lower() in ["directory", "directorio"]
        
        if is_directory:
            print(f"🖱️ Doble clic en directorio: {file_name}")
            
            # Construir nueva ruta de forma segura
            current_path = self.current_path_var.get()
            if current_path == "/":
                new_path = "/" + file_name
            else:
                new_path = current_path.rstrip('/') + '/' + file_name
                
            print(f"📂 Intentando navegar a: {new_path}")
            
            # Mostrar feedback visual inmediato
            self.files_tree.delete(*self.files_tree.get_children())
            self.files_tree.insert('', 'end', values=('Cargando...', '', '', ''))
            
            # Intentar navegar al directorio
            try:
                if self.sftp_manager.verify_directory_exists(new_path):
                    print(f"✅ Directorio existe: {new_path}")
                    self.current_path_var.set(new_path)
                    self.refresh_files()
                else:
                    print(f"❌ No se puede acceder al directorio: {new_path}")
                    messagebox.showerror("Error", f"No se puede acceder al directorio:\n{new_path}")
                    self.refresh_files()  # Refrescar para mostrar contenido actual
            except Exception as e:
                print(f"❌ Error navegando a directorio: {e}")
                messagebox.showerror("Error", f"Error accediendo al directorio:\n{str(e)}")
                self.refresh_files()  # Refrescar para mostrar contenido actual
    
    def go_up_directory(self):
        """Sube un directorio"""
        current_path = self.current_path_var.get()
        if current_path != "/":
            parent_path = os.path.dirname(current_path).replace('\\', '/')
            if not parent_path:
                parent_path = "/"
            self.current_path_var.set(parent_path)
            self.refresh_files()
    
    def go_home_directory(self):
        """Va al directorio home"""
        self.current_path_var.set("/")
        self.refresh_files()
    
    def navigate_to_path(self):
        """Navega a la ruta especificada"""
        # Obtener la ruta ingresada por el usuario
        path = self.current_path_var.get()
        
        # Verificar conexión
        if not self.sftp_manager.is_connected():
            messagebox.showerror("Error", "No hay conexión SFTP activa")
            return
                
        # Verificar si la ruta existe
        if self.sftp_manager.verify_directory_exists(path):
            # Si llegamos aquí, la ruta existe, refrescar archivos
            self.refresh_files()
        else:
            messagebox.showerror("Error", f"La ruta '{path}' no existe o no es un directorio.")
            # Restaurar la ruta a la raíz si hay error
            self.current_path_var.set("/")
            self.refresh_files()
    
    def create_directory(self):
        """Crea un nuevo directorio"""
        from tkinter import simpledialog
        dir_name = simpledialog.askstring("Nueva Carpeta", "Nombre de la carpeta:")
        
        if dir_name:
            threading.Thread(target=self._create_directory_thread, args=(dir_name,), daemon=True).start()
    
    def _create_directory_thread(self, dir_name):
        """Thread para crear directorio"""
        try:
            current_path = self.current_path_var.get()
            new_path = os.path.join(current_path, dir_name).replace('\\', '/')
            self.sftp_manager.create_directory(new_path)
            self.update_queue.put(lambda: self.refresh_files())
        except Exception as e:
            self.update_queue.put(lambda: messagebox.showerror("Error", f"Error creando directorio: {e}"))
    
    def delete_selected(self):
        """Elimina el archivo/directorio seleccionado"""
        selection = self.files_tree.selection()
        if not selection:
            return
        
        item = self.files_tree.item(selection[0])
        file_name = item['values'][0]
        
        if messagebox.askyesno("Confirmar", f"¿Eliminar '{file_name}'?"):
            threading.Thread(target=self._delete_file_thread, args=(file_name,), daemon=True).start()
    
    def _delete_file_thread(self, file_name):
        """Thread para eliminar archivo"""
        try:
            current_path = self.current_path_var.get()
            file_path = os.path.join(current_path, str(file_name)).replace('\\', '/')
            self.sftp_manager.delete_file(file_path)
            self.update_queue.put(lambda: self.refresh_files())
        except Exception as e:
            # Capturar el error en una variable local para evitar problemas de scope
            error_msg = str(e)
            self.update_queue.put(lambda error=error_msg: messagebox.showerror("Error", f"Error eliminando archivo: {error}"))
    
    # Menús contextuales
    def show_m3u_context_menu(self, event):
        """Muestra el menú contextual del M3U tree"""
        from tkinter import Menu
        
        context_menu = Menu(self.root, tearoff=0)
        
        selection = self.m3u_tree.selection()
        if selection:
            context_menu.add_command(label="Descargar Seleccionado", command=self.download_selected_m3u)
            context_menu.add_separator()
            
            # Detectar si es serie para opciones adicionales
            item = self.m3u_tree.item(selection[0])
            if "serie" in item['values'][2].lower() or "season" in item['values'][0].lower():
                context_menu.add_command(label="Descargar Temporada Completa", command=self.download_season)
                context_menu.add_command(label="Descargar Serie Completa", command=self.download_series)
                context_menu.add_separator()
            
            context_menu.add_command(label="Agregar a Cola", command=self.add_to_queue)
        
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()
    
    def show_files_context_menu(self, event):
        """Muestra el menú contextual del files tree"""
        # Este menú se puede usar para operaciones adicionales de archivos
        pass
    
    # Selección múltiple
    def on_ctrl_click_m3u(self, event):
        """Maneja Ctrl+Click para selección múltiple"""
        pass  # tkinter maneja esto automáticamente
    
    def on_shift_click_m3u(self, event):
        """Maneja Shift+Click para selección de rango"""
        pass  # tkinter maneja esto automáticamente
    
    # Métodos de descarga
    def download_selected_m3u(self):
        """Descarga los elementos M3U seleccionados"""
        selection = self.m3u_tree.selection()
        if not selection:
            messagebox.showwarning("Advertencia", "Seleccione elementos para descargar")
            return

        if not self.sftp_manager.is_connected():
            messagebox.showerror("Error", "Debe estar conectado al servidor SFTP")
            return

        # Obtener la ruta actual del SFTP y verificar que es válida
        download_path = self.current_path_var.get()

        # Normalizar la ruta para asegurar formato correcto
        if not download_path or download_path.strip() == "":
            download_path = "/"
        download_path = download_path.replace('\\', '/').rstrip('/')
        if not download_path.startswith('/'):
            download_path = '/' + download_path
        if download_path != "/":
            download_path = download_path.rstrip('/')

        print(f"🎯 Carpeta SFTP actual para descarga: '{download_path}'")

        # Verificar que la carpeta existe y es accesible
        try:
            if not self.sftp_manager.verify_directory_exists(download_path):
                messagebox.showerror("Error", f"La carpeta '{download_path}' no existe o no es accesible.\n\nVerifique que está navegando en una carpeta válida del servidor SFTP.")
                return
        except Exception as e:
            messagebox.showerror("Error", f"Error verificando la carpeta de destino '{download_path}':\n{str(e)}\n\nAsegúrese de estar conectado al servidor SFTP.")
            return
        
        # Preguntar si usar descarga remota (wget en servidor) o descarga local (a través de la aplicación)
        download_type = messagebox.askyesno(
            "Tipo de Descarga",
            f"¿Desea que el SERVIDOR descargue directamente los archivos?\n\n"
            f"📁 Carpeta destino en SFTP: {download_path}\n"
            f"📊 Archivos seleccionados: {len(selection)}\n\n"
            f"✅ SÍ: El servidor descargará usando wget (recomendado)\n"
            f"⬇️ NO: La aplicación descargará y luego subirá al servidor\n\n"
            f"Nota: Los archivos se guardarán en la carpeta que tienes abierta actualmente en el navegador SFTP.",
            icon="question"
        )
        
        print(f"🔽 Tipo de descarga seleccionado: {'Remota (wget)' if download_type else 'Local'}")
        
        # Configurar el sftp_manager en el download_manager antes de usarlo
        if hasattr(self.download_manager, 'set_sftp_manager'):
            self.download_manager.set_sftp_manager(self.sftp_manager)
            print("✅ SFTP Manager configurado en Download Manager")
        else:
            print("⚠️ No se pudo configurar SFTP Manager")
        
        success_count = 0
        error_count = 0
        
        for item_id in selection:
            item = self.m3u_tree.item(item_id)
            index = int(item['tags'][0])
            m3u_item = self.m3u_content[index]
            
            print(f"📱 Procesando: {m3u_item['name']}")
            print(f"🌐 URL: {m3u_item['url']}")
            
            try:
                if download_type:  # Descarga remota usando wget
                    print(f"📡 Iniciando descarga remota en: {download_path}")
                    download_id = self.download_manager.add_remote_download(
                        url=m3u_item['url'],
                        filename=m3u_item['name'],
                        remote_path=download_path
                    )
                    print(f"✅ Descarga remota añadida: {download_id}")
                    success_count += 1
                else:  # Descarga local tradicional
                    print(f"💻 Iniciando descarga local para subir a: {download_path}")
                    download_id = self.download_manager.add_download(
                        url=m3u_item['url'],
                        filename=m3u_item['name'],
                        remote_path=download_path
                    )
                    print(f"✅ Descarga local añadida: {download_id}")
                    success_count += 1
            except Exception as e:
                print(f"❌ Error añadiendo descarga para {m3u_item['name']}: {e}")
                error_count += 1
        
        # Mostrar resumen
        summary_msg = f"Descargas añadidas: {success_count}"
        if error_count > 0:
            summary_msg += f"\nErrores: {error_count}"
        summary_msg += f"\nCarpeta destino: {download_path}"
        
        messagebox.showinfo("Resumen", summary_msg)
        
        # Actualizar la visualización de descargas
        self.update_downloads_display()
        
        # Iniciar el hilo de monitoreo si hay descargas exitosas
        if success_count > 0:
            self.start_download_thread()
    
    def download_season(self):
        """Descarga temporada completa"""
        # Implementar lógica para detectar y descargar temporada completa
        messagebox.showinfo("Info", "Función de descarga de temporada en desarrollo")
    
    def download_series(self):
        """Descarga serie completa"""
        # Implementar lógica para detectar y descargar serie completa
        messagebox.showinfo("Info", "Función de descarga de serie completa en desarrollo")
    
    def add_to_queue(self):
        """Agrega elementos a la cola de descarga"""
        self.download_selected_m3u()
    
    # Control de descargas
    def start_downloads(self):
        """Inicia las descargas"""
        self.download_manager.start_downloads()
        self.start_download_thread()  # Iniciar hilo de monitoreo
        
    def pause_downloads(self):
        """Pausa las descargas"""
        self.download_manager.pause_downloads()
        self.update_downloads_display()
        
    def stop_downloads(self):
        """Detiene las descargas"""
        self.download_manager.stop_downloads()
        
        # Detener hilo de monitoreo
        if self.download_thread and self.download_thread.is_alive():
            self.is_download_running = False
            
        self.update_downloads_display()
    
    def clear_queue(self):
        """Limpia la cola de descargas"""
        if messagebox.askyesno("Confirmar", "¿Limpiar toda la cola de descargas?"):
            try:
                self.download_manager.clear_queue()
                self.update_downloads_display()
                messagebox.showinfo("Información", "Cola de descargas limpiada con éxito")
            except Exception as e:
                messagebox.showerror("Error", f"Error al limpiar la cola: {e}")
    
    def update_downloads_display(self):
        """Actualiza la visualización de descargas"""
        try:
            # Limpiar tree de descargas
            for item in self.downloads_tree.get_children():
                self.downloads_tree.delete(item)
            
            # Agregar descargas actuales
            downloads = self.download_manager.get_downloads_status()
            
            for download in downloads:
                # Obtener los datos de la descarga
                download_id = download.get('id', '')
                filename = download.get('filename', 'Desconocido')
                
                # Añadir icono para descargas remotas
                if download.get('is_remote', False):
                    filename = "📡 " + filename
                else:
                    filename = "💻 " + filename
                
                status = download.get('status', 'Desconocido')
                
                # Mejorar la visualización del progreso
                progress_value = download.get('progress', 0)
                if progress_value > 0:
                    progress = f"{progress_value:.1f}%"
                else:
                    progress = "0%"
                
                # Mejorar la visualización de velocidad
                speed = download.get('speed', '0 KB/s')
                if speed == '0 KB/s' or speed == '0 B/s':
                    if status == 'Descargando':
                        speed = "Iniciando..."
                    else:
                        speed = "--"
                
                # Mejorar ETA
                eta = download.get('eta', '--:--')
                if eta == '--:--' and status == 'Descargando' and progress_value > 0:
                    eta = "Calculando..."
                
                # Mejorar visualización de tamaño
                size = download.get('size', 'Desconocido')
                if size == 'Desconocido' or size == '0 B':
                    if status == 'Descargando':
                        size = "Detectando..."
                    else:
                        size = "--"
                
                # Aplicar colores según el estado usando el tema
                if hasattr(self, 'theme_manager') and hasattr(self.theme_manager, 'theme'):
                    if status == 'Completado':
                        # Usar verde para completado
                        status_color = self.theme_manager.theme.SUCCESS_COLOR
                    elif status == 'Error':
                        # Usar rojo para error
                        status_color = self.theme_manager.theme.ERROR_COLOR
                    elif status == 'Descargando':
                        # Usar verde NVIDIA para descargando
                        status_color = self.theme_manager.theme.NVIDIA_GREEN
                    else:
                        # Color normal
                        status_color = self.theme_manager.theme.TEXT_WHITE
                
                # Insertar en el tree
                self.downloads_tree.insert('', 'end', values=(
                    filename,
                    status,
                    progress,
                    speed,
                    eta,
                    size
                ), tags=(download_id,))
                
            # Actualizar estadísticas generales (usando estados en español)
            queued = sum(1 for d in downloads if d.get('status') == 'En cola')
            active = sum(1 for d in downloads if d.get('status') in ['Descargando'])
            completed = sum(1 for d in downloads if d.get('status') == 'Completado')
            error = sum(1 for d in downloads if d.get('status') == 'Error')
            
            # Mejorar estadísticas para incluir descargas remotas
            remote_active = sum(1 for d in downloads if d.get('is_remote', False) and d.get('status') == 'Descargando')
            if remote_active > 0:
                stats_text = f"En cola: {queued} | Activas: {active} (📡{remote_active}) | Completadas: {completed} | Errores: {error}"
            else:
                stats_text = f"En cola: {queued} | Activas: {active} | Completadas: {completed} | Errores: {error}"
            
            self.stats_label.config(text=stats_text)
            
            # Actualizar barra de progreso general
            if downloads:
                total_progress = sum(d.get('progress', 0) for d in downloads) / len(downloads)
                self.general_progress['value'] = total_progress
                self.general_progress_label.config(text=f"{total_progress:.1f}%")
                
                # Mostrar información adicional en consola si hay actividad
                active_downloads = [d for d in downloads if d.get('status') == 'Descargando']
                if active_downloads:
                    for d in active_downloads:
                        name = d.get('filename', 'unknown')[:25]
                        prog = d.get('progress', 0)
                        remote = "📡" if d.get('is_remote') else "💻"
                        if prog > 0:
                            print(f"🔄 {remote} {name}: {prog:.1f}%", end=" | ")
                    print()  # Nueva línea después de mostrar todas las descargas
            else:
                self.general_progress['value'] = 0
                self.general_progress_label.config(text="0%")
                
        except Exception as e:
            print(f"❌ Error actualizando visualización de descargas: {e}")
    
    def test_demo_sftp(self):
        """Prueba conexión con servidor demo"""
        # Guardar datos actuales
        current_host = self.sftp_host_var.get()
        current_port = self.sftp_port_var.get()
        current_user = self.sftp_user_var.get()
        current_pass = self.sftp_pass_var.get()
        
        # Configurar datos demo
        self.sftp_host_var.set("test.rebex.net")
        self.sftp_port_var.set("22")
        self.sftp_user_var.set("demo")
        self.sftp_pass_var.set("password")
        
        # Mostrar mensaje informativo
        messagebox.showinfo("Test Demo", 
            "Probando conexión con servidor demo público:\n\n"
            "Host: test.rebex.net\n"
            "Usuario: demo\n"
            "Contraseña: password\n\n"
            "Si esto funciona, el problema está en tus datos de conexión.")
        
        # Intentar conectar
        self.connect_sftp()
        
        # Función para restaurar datos después de un tiempo
        def restore_data():
            if messagebox.askyesno("Restaurar Datos", 
                "¿Quieres restaurar tus datos de conexión anteriores?"):
                self.sftp_host_var.set(current_host)
                self.sftp_port_var.set(current_port)
                self.sftp_user_var.set(current_user)
                self.sftp_pass_var.set(current_pass)
        
        # Programar restauración después de 5 segundos
        self.root.after(5000, restore_data)
    
    def show_download_logs(self):
        """Mostrar logs de descargas en una ventana separada"""
        try:
            selection = self.downloads_tree.selection()
            if not selection:
                messagebox.showwarning("Selección", "Seleccione una descarga para ver sus logs")
                return
            
            # Obtener información de la descarga seleccionada
            item_values = self.downloads_tree.item(selection[0])['values']
            filename = item_values[0].replace('📡 ', '').replace('💻 ', '')  # Remover iconos
            
            # Buscar la descarga en el manager
            downloads = self.download_manager.get_downloads_status()
            selected_download = None
            for download in downloads:
                if download.get('filename') == filename:
                    selected_download = download
                    break
            
            if not selected_download:
                messagebox.showinfo("Logs", "Descarga no encontrada")
                return
            
            if not selected_download.get('is_remote'):
                messagebox.showinfo("Logs", "Solo disponible para descargas remotas (📡)")
                return
            
            log_file = selected_download.get('remote_log_file')
            if not log_file:
                messagebox.showinfo("Logs", "No hay archivo de log disponible aún.\n\nEl log se genera cuando wget inicia.")
                return
            
            # Crear ventana de logs
            log_window = tk.Toplevel(self.root)
            log_window.title(f"Logs de descarga: {filename}")
            log_window.geometry("900x700")
            log_window.transient(self.root)  # Hacer que la ventana sea hija de la principal
            
            # Aplicar tema a la ventana de logs si está disponible
            if hasattr(self, 'theme_manager'):
                log_window.configure(bg=self.theme_manager.theme.BACKGROUND_DARK)
            
            # Frame principal
            main_frame = ttk.Frame(log_window)
            main_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Información de la descarga
            info_frame = ttk.LabelFrame(main_frame, text="Información de Descarga")
            info_frame.pack(fill='x', pady=(0, 10))
            
            info_text = f"Archivo: {filename}\n"
            info_text += f"Estado: {selected_download.get('status', 'Desconocido')}\n"
            info_text += f"Progreso: {selected_download.get('progress', 0):.1f}%\n"
            info_text += f"Log: {log_file}"
            
            info_label = ttk.Label(info_frame, text=info_text, font=('Segoe UI', 9))
            info_label.pack(anchor='w', padx=5, pady=5)
            
            # Text widget con scroll para logs
            text_frame = ttk.LabelFrame(main_frame, text="Log de wget")
            text_frame.pack(fill='both', expand=True)
            
            log_text = tk.Text(text_frame, wrap='word', font=('Consolas', 9), height=25)
            scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=log_text.yview)
            log_text.configure(yscrollcommand=scrollbar.set)
            
            # Aplicar tema al Text widget
            if hasattr(self, 'theme_manager'):
                log_text.configure(
                    bg=self.theme_manager.theme.BACKGROUND_MEDIUM,
                    fg=self.theme_manager.theme.TEXT_WHITE,
                    insertbackground=self.theme_manager.theme.NVIDIA_GREEN,
                    selectbackground=self.theme_manager.theme.NVIDIA_GREEN,
                    selectforeground=self.theme_manager.theme.TEXT_WHITE
                )
            
            log_text.pack(side='left', fill='both', expand=True, padx=5, pady=5)
            scrollbar.pack(side='right', fill='y')
            
            # Botones
            btn_frame = ttk.Frame(main_frame)
            btn_frame.pack(fill='x', pady=(10, 0))
            
            auto_refresh_var = tk.BooleanVar(value=True)
            auto_refresh_check = ttk.Checkbutton(btn_frame, text="Auto-actualizar", variable=auto_refresh_var)
            auto_refresh_check.pack(side='left')
            
            def refresh_logs():
                """Actualizar contenido de logs"""
                try:
                    if self.sftp_manager.is_connected() and self.sftp_manager.ssh_client:
                        # Obtener contenido del log con más líneas
                        log_cmd = f'tail -50 "{log_file}" 2>/dev/null || echo "Log no disponible"'
                        stdin, stdout, stderr = self.sftp_manager.ssh_client.exec_command(log_cmd, timeout=5)
                        log_content = stdout.read().decode('utf-8', errors='ignore')
                        
                        # Limpiar y actualizar text widget
                        log_text.delete(1.0, tk.END)
                        if log_content.strip():
                            log_text.insert(1.0, log_content)
                            # Agregar información de progreso al final
                            log_text.insert(tk.END, f"\n\n--- Estado actual ---\n")
                            log_text.insert(tk.END, f"Progreso: {selected_download.get('progress', 0):.1f}%\n")
                            log_text.insert(tk.END, f"Velocidad: {selected_download.get('speed', '0 KB/s')}\n")
                            log_text.insert(tk.END, f"ETA: {selected_download.get('eta', '--:--')}\n")
                        else:
                            log_text.insert(1.0, "Log vacío o no disponible")
                        
                        log_text.see(tk.END)  # Scroll al final
                        
                except Exception as e:
                    log_text.delete(1.0, tk.END)
                    log_text.insert(1.0, f"Error obteniendo logs: {e}")
            
            def auto_refresh():
                """Auto-refresh cada 2 segundos si está habilitado"""
                try:
                    if auto_refresh_var.get() and log_window.winfo_exists():
                        refresh_logs()
                        log_window.after(2000, auto_refresh)
                except tk.TclError:
                    # La ventana fue cerrada
                    pass
            
            ttk.Button(btn_frame, text="Actualizar", command=refresh_logs).pack(side='left', padx=5)
            ttk.Button(btn_frame, text="Limpiar", command=lambda: log_text.delete(1.0, tk.END)).pack(side='left', padx=5)
            ttk.Button(btn_frame, text="Cerrar", command=log_window.destroy).pack(side='right', padx=5)
            
            # Cargar logs iniciales
            refresh_logs()
            
            # Iniciar auto-refresh
            log_window.after(2000, auto_refresh)
            
        except Exception as e:
            messagebox.showerror("Error", f"Error mostrando logs: {e}")

    def setup_downloads_panel(self, parent):
        """Configura el panel de descargas"""
        # Frame para controles de descargas
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill='x', padx=5, pady=5)
        
        # Botones de control
        ttk.Button(controls_frame, text="▶️ Iniciar", 
                  command=self.start_downloads).pack(side='left', padx=2)
        ttk.Button(controls_frame, text="⏸️ Pausar", 
                  command=self.pause_downloads).pack(side='left', padx=2)
        ttk.Button(controls_frame, text="⏹️ Detener", 
                  command=self.stop_downloads).pack(side='left', padx=2)
        ttk.Button(controls_frame, text="🗑️ Limpiar Cola", 
                  command=self.clear_queue).pack(side='left', padx=2)
        ttk.Button(controls_frame, text="Ver Logs", 
                  command=self.show_download_logs).pack(side='left', padx=2)
        
        # Estadísticas
        stats_frame = ttk.Frame(parent)
        stats_frame.pack(fill='x', padx=5, pady=2)
        
        self.stats_label = ttk.Label(stats_frame, text="En cola: 0 | Activas: 0 | Completadas: 0 | Errores: 0")
        self.stats_label.pack(side='left')
        
        # Progreso general
        progress_frame = ttk.Frame(parent)
        progress_frame.pack(fill='x', padx=5, pady=2)
        
        ttk.Label(progress_frame, text="Progreso General:").pack(side='left')
        self.general_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.general_progress.pack(side='left', fill='x', expand=True, padx=5)
        self.general_progress_label = ttk.Label(progress_frame, text="0%")
        self.general_progress_label.pack(side='left')
        
        # Treeview para descargas
        downloads_frame = ttk.LabelFrame(parent, text="Cola de Descargas")
        downloads_frame.pack(fill='both', expand=True, pady=5)
        
        # Treeview para descargas
        downloads_columns = ('File', 'Status', 'Progress', 'Speed', 'ETA', 'Size')
        self.downloads_tree = ttk.Treeview(downloads_frame, columns=downloads_columns, show='headings', height=6)
        
        # Configurar columnas descargas
        self.downloads_tree.heading('File', text='Archivo')
        self.downloads_tree.heading('Status', text='Estado')
        self.downloads_tree.heading('Progress', text='Progreso')
        self.downloads_tree.heading('Speed', text='Velocidad')
        self.downloads_tree.heading('ETA', text='ETA')
        self.downloads_tree.heading('Size', text='Tamaño')
        
        self.downloads_tree.column('File', width=300)
        self.downloads_tree.column('Status', width=100)
        self.downloads_tree.column('Progress', width=100)
        self.downloads_tree.column('Speed', width=100)
        self.downloads_tree.column('ETA', width=100)
        self.downloads_tree.column('Size', width=100)
        
        downloads_scrollbar = ttk.Scrollbar(downloads_frame, orient='vertical', command=self.downloads_tree.yview)
        self.downloads_tree.configure(yscrollcommand=downloads_scrollbar.set)
        
        self.downloads_tree.pack(side='left', fill='both', expand=True, padx=5, pady=5)
        downloads_scrollbar.pack(side='right', fill='y')
        
        # Actualizar visualización de descargas
        self.update_downloads_display()

    def start_download_thread(self):
        """Inicia un hilo para monitorear las descargas"""
        # Detener hilo anterior si existe
        if self.download_thread and self.download_thread.is_alive():
            print("🛑 Deteniendo hilo de descarga anterior...")
            self.is_download_running = False
            # Timeout más corto para evitar bloqueos
            self.download_thread.join(timeout=2.0)  # Aumentado para dar más tiempo
            if self.download_thread.is_alive():
                print("⚠️ Hilo de descarga anterior no terminó en tiempo límite, continuando...")

        # Crear y arrancar nuevo hilo si hay descargas que monitorear
        downloads = self.download_manager.get_downloads_status()

        # Verificar diferentes tipos de descargas que necesitan monitoreo
        has_active = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads)
        has_remote = any(d.get('is_remote', False) and d.get('status') in ['Descargando'] for d in downloads)
        
        # Iniciar hilo si hay cualquier tipo de actividad
        if has_active or has_remote:
            self.is_download_running = True
            self.download_thread = threading.Thread(target=self._monitor_downloads_thread, daemon=True)
            self.download_thread.start()
            
            active_count = sum(1 for d in downloads if d.get('status') == 'Descargando')
            remote_count = sum(1 for d in downloads if d.get('is_remote', False) and d.get('status') == 'Descargando')
            print(f"🔄 Hilo de monitoreo iniciado: {active_count} activas, {remote_count} remotas")
        else:
            print("📊 No hay descargas que requieran monitoreo")
    
    def _monitor_downloads_thread(self):
        """Thread para monitorear el progreso de las descargas"""
        try:
            update_counter = 0
            total_monitoring_time = 0
            max_monitoring_time = 300  # 5 minutos máximo de monitoreo
            last_update_time = time.time()
            
            print("🎯 Iniciando monitoreo detallado de descargas...")
            
            while self.is_download_running and self.is_running and total_monitoring_time < max_monitoring_time:
                # Obtener actualizaciones del gestor de descargas con más frecuencia
                downloads = self.download_manager.get_downloads_status()

                # Verificar diferentes tipos de actividad de descarga
                has_active_downloads = any(d.get('status') in ['Descargando'] for d in downloads)
                has_queued_downloads = any(d.get('status') in ['En cola'] for d in downloads)
                has_remote_downloads = any(d.get('is_remote', False) and d.get('status') in ['Descargando', 'En cola'] for d in downloads)

                # Para descargas remotas, ser más tolerante ya que wget puede tardar en iniciar
                any_activity = has_active_downloads or has_queued_downloads or (has_remote_downloads and total_monitoring_time < 60)

                # Forzar actualización de UI más frecuentemente para ver progreso en tiempo real
                current_time = time.time()
                if current_time - last_update_time >= 1.0:  # Actualizar cada segundo
                    self.update_queue.put(lambda dl=downloads: self._update_downloads_ui(dl))
                    last_update_time = current_time
                    
                    # Mostrar información de depuración del progreso
                    active_downloads = [d for d in downloads if d.get('status') == 'Descargando']
                    if active_downloads:
                        for download in active_downloads:
                            progress = download.get('progress', 0)
                            filename = download.get('filename', 'unknown')[:30]
                            is_remote = "📡" if download.get('is_remote') else "💻"
                            print(f"{is_remote} {filename}: {progress:.1f}%")

                if any_activity:
                    # Actualización más rápida para descargas activas
                    time.sleep(0.5)  # Reducido a 0.5 segundos para actualizaciones más fluidas
                    total_monitoring_time += 0.5
                else:
                    # Si no hay actividad aparente, hacer verificaciones más profundas
                    update_counter += 1
                    if update_counter >= 6:  # Actualizar cada 3 segundos cuando no hay actividad (6 * 0.5)
                        self.update_queue.put(lambda dl=downloads: self._update_downloads_ui(dl))
                        update_counter = 0

                    # Verificación extendida para descargas remotas
                    if not any_activity and total_monitoring_time < 120:  # Primeros 2 minutos
                        print(f"🔍 Verificando actividad de descargas remotas... (tiempo: {total_monitoring_time}s)")
                        
                        # Revisar logs de descargas remotas para detectar actividad real
                        remote_activity_detected = False
                        for download in downloads:
                            if download.get('is_remote') and download.get('status') == 'Descargando':
                                # Si hay una descarga remota marcada como activa, continuar monitoreando
                                remote_activity_detected = True
                                print(f"📡 Descarga remota activa detectada: {download.get('filename', 'desconocida')}")
                                break
                        
                        if remote_activity_detected:
                            any_activity = True
                    
                    time.sleep(0.5)  # Pausa más corta para mejor respuesta
                    total_monitoring_time += 0.5

                    # Solo salir si realmente no hay nada que monitorear
                    if not any_activity and total_monitoring_time > 15:  # Después de 15 segundos mínimo
                        # Verificación final: contar descargas que realmente necesitan monitoreo
                        active_or_recent = []
                        for download in downloads:
                            if download.get('status') in ['Descargando', 'En cola']:
                                active_or_recent.append(download.get('filename', 'desconocida'))
                        
                        if not active_or_recent:
                            print(f"📊 No hay descargas activas después de {total_monitoring_time}s, terminando monitoreo")
                            break
                        else:
                            print(f"🔄 Continuando monitoreo para: {', '.join(active_or_recent[:3])}")

        except Exception as e:
            print(f"❌ Error en hilo de monitoreo de descargas: {e}")
        finally:
            self.is_download_running = False
            print("🔚 Hilo de monitoreo de descargas terminado")
    
    def _update_downloads_ui(self, downloads):
        """Actualiza la UI con la información de descargas (llamado desde el hilo principal)"""
        try:
            # Actualizar el árbol de descargas
            self.update_downloads_display()

            # Verificar si necesitamos reiniciar el hilo de monitoreo
            has_active_downloads = any(d.get('status') in ['Descargando', 'En cola'] for d in downloads)
            has_remote_downloads = any(d.get('is_remote', False) and d.get('status') in ['Descargando'] for d in downloads)
            
            # Solo reiniciar el hilo si realmente hay actividad y no está corriendo
            if (has_active_downloads or has_remote_downloads) and not (self.download_thread and self.download_thread.is_alive()):
                print(f"🔄 Reiniciando hilo de monitoreo: activas={has_active_downloads}, remotas={has_remote_downloads}")
                self.start_download_thread()
        except Exception as e:
            print(f"❌ Error actualizando UI de descargas: {e}")

    def _on_download_progress_change(self, download_id):
        """Callback llamado cuando cambia el progreso de una descarga"""
        # Programar actualización de UI en el hilo principal
        if self.is_running:
            self.update_queue.put(lambda: self.update_downloads_display())

    def _on_download_status_change(self, download_id):
        """Callback llamado cuando cambia el estado de una descarga"""
        # Programar actualización de UI en el hilo principal
        if self.is_running:
            self.update_queue.put(lambda: self.update_downloads_display())

def main():
    root = tk.Tk()
    app = M3UDownloadApp(root)
    
    # Manejar cierre de ventana
    def on_closing():
        print("🔄 Cerrando aplicación...")
        try:
            # Marcar que la aplicación se está cerrando
            app.is_running = False
            app.is_download_running = False
            app.is_refresh_running = False

            # Guardar configuración
            app.save_config()

            # Detener sistemas de descarga
            app.download_manager.stop_downloads()

            # Desconectar SFTP
            app.sftp_manager.disconnect()

            # Esperar un momento para que los threads terminen
            import time
            time.sleep(0.5)

            print("✅ Recursos liberados correctamente")
        except Exception as e:
            print(f"⚠️ Error al cerrar: {e}")
        finally:
            try:
                root.quit()  # Salir del mainloop primero
                root.destroy()  # Luego destruir la ventana
            except:
                pass
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\n🔄 Interrupción detectada, cerrando...")
        on_closing()

if __name__ == "__main__":
    main()
