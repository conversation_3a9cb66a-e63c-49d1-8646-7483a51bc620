#!/usr/bin/env python3
"""
Test para verificar que el escapado de nombres de archivo funciona correctamente
"""

import shlex

def test_filename_escaping():
    """Test del escapado de nombres de archivo"""
    print("🧪 TEST DE ESCAPADO DE NOMBRES DE ARCHIVO")
    print("=" * 60)
    
    # Casos de prueba problemáticos
    test_cases = [
        "Los Caballeros del Zodiaco: Omega (2012) S01 E01.mkv",
        "Movie with (parentheses) and spaces.mp4",
        "File with 'single quotes'.avi",
        'File with "double quotes".mkv',
        "File with $pecial ch@racters!.m3u8",
        "Normal_file_name.mp4"
    ]
    
    print("📝 Casos de prueba:")
    for i, filename in enumerate(test_cases, 1):
        print(f"   {i}. {filename}")
    
    print("\n🔒 Resultados del escapado:")
    print("-" * 40)
    
    for filename in test_cases:
        safe_filename = shlex.quote(filename)
        print(f"Original: {filename}")
        print(f"Escapado: {safe_filename}")
        
        # Simular comando wget
        wget_cmd = f'wget --output-document={safe_filename} "http://example.com/file"'
        print(f"Comando:  {wget_cmd}")
        print()
    
    print("✅ Test de escapado completado")
    
    # Test específico del caso problemático
    print("\n🎯 TEST ESPECÍFICO DEL CASO PROBLEMÁTICO")
    print("-" * 50)
    
    problematic_filename = "Los Caballeros del Zodiaco: Omega (2012) S01 E01.mkv"
    safe_filename = shlex.quote(problematic_filename)
    remote_path = "/media"
    safe_remote_path = shlex.quote(remote_path)
    url = "https://zonamovie.live:8443/series/Maujose2024/20240613507/883259.mkv"
    safe_url = shlex.quote(url)
    log_path = "/tmp/wget_log_test.log"
    safe_log_path = shlex.quote(log_path)
    
    # Comando original (problemático)
    old_cmd = (
        f'cd "{remote_path}" && '
        f'nohup timeout 300 wget --continue --tries=0 --user-agent="XCIPTV" '
        f'--progress=dot:mega --timeout=5 '
        f'--output-document="{problematic_filename}" "{url}" > {log_path} 2>&1 &'
    )
    
    # Comando nuevo (corregido)
    new_cmd = (
        f'cd {safe_remote_path} && '
        f'nohup timeout 300 wget --continue --tries=0 --user-agent="XCIPTV" '
        f'--progress=dot:mega --timeout=5 '
        f'--output-document={safe_filename} {safe_url} > {safe_log_path} 2>&1 &'
    )
    
    print("❌ Comando PROBLEMÁTICO (anterior):")
    print(old_cmd)
    print()
    print("✅ Comando CORREGIDO (nuevo):")
    print(new_cmd)
    print()
    
    # Verificar que no hay caracteres problemáticos sin escapar
    problematic_chars = ['(', ')', ' ', ':', '"', "'"]
    has_problems = False
    
    print("🔍 Verificando caracteres problemáticos en comando nuevo:")
    for char in problematic_chars:
        if char in new_cmd and f"'{char}" not in new_cmd and f"{char}'" not in new_cmd:
            # El carácter está presente pero no está dentro de comillas simples (escapado)
            if char == ' ' and ('cd ' in new_cmd or 'wget ' in new_cmd or 'timeout ' in new_cmd):
                # Espacios normales en comandos están bien
                continue
            print(f"   ⚠️ Carácter problemático encontrado: '{char}'")
            has_problems = True
    
    if not has_problems:
        print("   ✅ No se encontraron caracteres problemáticos sin escapar")
    
    print("\n" + "=" * 60)
    print("✅ TEST COMPLETADO - El escapado debería solucionar el problema")

if __name__ == "__main__":
    test_filename_escaping()
