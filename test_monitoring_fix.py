#!/usr/bin/env python3
"""
Script de prueba para verificar que el hilo de monitoreo se inicie correctamente.
"""

import sys
import os
import time
import threading
from unittest.mock import Mock, patch

# Agregar el directorio actual al path para importar los módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from download_manager_requests import DownloadManager, DownloadStatus
    from sftp_manager import SFTPManager
    print("✅ Módulos importados correctamente")
except ImportError as e:
    print(f"❌ Error importando módulos: {e}")
    sys.exit(1)

def test_monitoring_thread_start():
    """Prueba que el hilo de monitoreo se inicie cuando hay descargas"""
    print("\n🔍 Probando inicio del hilo de monitoreo...")
    
    # Crear SFTP manager
    sftp = SFTPManager()
    
    # Crear download manager
    dm = DownloadManager(sftp_manager=sftp)
    
    print(f"📊 Estado inicial:")
    print(f"   - Sistema corriendo: {dm.is_running}")
    print(f"   - Descargas en cola: {len(dm.downloads)}")
    
    # Añadir una descarga remota
    print(f"\n➕ Añadiendo descarga remota...")
    download_id = dm.add_remote_download(
        url="https://httpbin.org/delay/1",
        filename="test_monitoring.json",
        remote_path="/test"
    )
    
    print(f"✅ Descarga añadida: {download_id}")
    print(f"📊 Estado después de añadir:")
    print(f"   - Sistema corriendo: {dm.is_running}")
    print(f"   - Descargas en cola: {len(dm.downloads)}")
    
    # Verificar estado de las descargas
    downloads = dm.get_downloads_status()
    print(f"\n📋 Estado de descargas:")
    for download in downloads:
        print(f"   - {download['filename']}: {download['status']} ({download['progress']:.1f}%)")
    
    # Verificar que hay descargas para monitorear
    has_downloads = len(downloads) > 0
    print(f"\n🔍 ¿Hay descargas para monitorear? {has_downloads}")
    
    if has_downloads:
        print("✅ El hilo de monitoreo DEBERÍA iniciarse")
    else:
        print("❌ No hay descargas, el hilo NO debería iniciarse")
    
    # Esperar un poco para ver el progreso
    print(f"\n⏳ Esperando 3 segundos para ver progreso...")
    time.sleep(3)
    
    # Verificar estado final
    downloads = dm.get_downloads_status()
    print(f"\n📋 Estado final de descargas:")
    for download in downloads:
        print(f"   - {download['filename']}: {download['status']} ({download['progress']:.1f}%)")
    
    # Limpiar
    dm.stop_downloads()
    return has_downloads

def test_multiple_downloads_monitoring():
    """Prueba el monitoreo con múltiples descargas"""
    print("\n🔍 Probando monitoreo con múltiples descargas...")
    
    # Crear SFTP manager
    sftp = SFTPManager()
    
    # Crear download manager
    dm = DownloadManager(sftp_manager=sftp)
    
    # Añadir múltiples descargas
    download_ids = []
    for i in range(3):
        download_id = dm.add_download(
            url=f"https://httpbin.org/delay/{i+1}",
            filename=f"test_multi_{i}.json",
            remote_path="/test"
        )
        download_ids.append(download_id)
        print(f"✅ Añadida descarga {i+1}: {download_id}")
    
    # Verificar estado
    downloads = dm.get_downloads_status()
    print(f"\n📊 Total de descargas: {len(downloads)}")
    print(f"📊 Sistema corriendo: {dm.is_running}")
    
    # Mostrar estado inicial
    print(f"\n📋 Estado inicial:")
    for download in downloads:
        print(f"   - {download['filename']}: {download['status']}")
    
    # Simular monitoreo
    print(f"\n👁️ Simulando monitoreo por 5 segundos...")
    for i in range(10):  # 5 segundos en intervalos de 0.5s
        time.sleep(0.5)
        downloads = dm.get_downloads_status()
        
        active_count = sum(1 for d in downloads if d.get('status') in ['Downloading', 'Processing', 'Queued'])
        completed_count = sum(1 for d in downloads if d.get('status') == 'Completed')
        
        print(f"   Iteración {i+1}: {active_count} activas, {completed_count} completadas")
        
        if active_count == 0:
            print("   ✅ Todas las descargas completadas")
            break
    
    # Estado final
    downloads = dm.get_downloads_status()
    print(f"\n📋 Estado final:")
    for download in downloads:
        print(f"   - {download['filename']}: {download['status']} ({download['progress']:.1f}%)")
    
    # Limpiar
    dm.stop_downloads()
    return True

def test_download_states():
    """Prueba los diferentes estados de descarga"""
    print("\n🔍 Probando estados de descarga...")
    
    # Crear SFTP manager
    sftp = SFTPManager()
    
    # Crear download manager
    dm = DownloadManager(sftp_manager=sftp)
    
    # Añadir una descarga
    download_id = dm.add_download(
        url="https://httpbin.org/delay/1",
        filename="test_states.json",
        remote_path="/test"
    )
    
    print(f"✅ Descarga añadida: {download_id}")
    
    # Monitorear cambios de estado
    print(f"\n👁️ Monitoreando cambios de estado...")
    previous_status = None
    
    for i in range(8):  # 4 segundos en intervalos de 0.5s
        time.sleep(0.5)
        downloads = dm.get_downloads_status()
        
        if downloads:
            current_status = downloads[0]['status']
            progress = downloads[0]['progress']
            
            if current_status != previous_status:
                print(f"   🔄 Estado cambió a: {current_status} ({progress:.1f}%)")
                previous_status = current_status
            
            if current_status in ['Completed', 'Error']:
                print(f"   ✅ Descarga terminada con estado: {current_status}")
                break
    
    # Limpiar
    dm.stop_downloads()
    return True

def main():
    """Función principal"""
    print("🚀 Iniciando pruebas de monitoreo de descargas...")
    print("=" * 60)
    
    try:
        print("\n🧪 Prueba 1: Inicio del hilo de monitoreo")
        test1_ok = test_monitoring_thread_start()
        
        print("\n" + "="*60)
        print("\n🧪 Prueba 2: Monitoreo múltiple")
        test2_ok = test_multiple_downloads_monitoring()
        
        print("\n" + "="*60)
        print("\n🧪 Prueba 3: Estados de descarga")
        test3_ok = test_download_states()
        
        print("\n" + "="*60)
        if test1_ok and test2_ok and test3_ok:
            print("🎉 ¡Todas las pruebas de monitoreo pasaron!")
            print("💡 El sistema debería monitorear las descargas correctamente")
        else:
            print("⚠️ Algunas pruebas fallaron")
        
        return test1_ok and test2_ok and test3_ok
        
    except Exception as e:
        print(f"\n❌ Error durante las pruebas: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
