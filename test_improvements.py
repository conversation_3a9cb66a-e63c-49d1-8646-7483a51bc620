#!/usr/bin/env python3
"""
Script de prueba para validar las mejoras implementadas en el M3U Manager.

Mejoras probadas:
1. Download en carpeta SFTP actual
2. Barra de progreso optimizada
3. Solución de long threads

Uso: python test_improvements.py
"""

import sys
import os
import time
import threading
from unittest.mock import Mock, patch

# Agregar el directorio actual al path para importar los módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from download_manager_requests import DownloadManager
    from sftp_manager import SFTPManager
    print("✅ Módulos importados correctamente")
except ImportError as e:
    print(f"❌ Error importando módulos: {e}")
    sys.exit(1)

def test_download_manager_timeouts():
    """Prueba que los timeouts del download manager sean más cortos"""
    print("\n🔍 Probando timeouts del Download Manager...")
    
    # Crear mock del SFTP manager
    mock_sftp = Mock()
    mock_sftp.is_connected.return_value = True
    
    # Crear download manager
    dm = DownloadManager(sftp_manager=mock_sftp)
    
    # Verificar que el timeout sea más corto (15 segundos en lugar de 30)
    assert dm.timeout == 15, f"❌ Timeout debería ser 15, pero es {dm.timeout}"
    print("✅ Timeout del Download Manager optimizado: 15 segundos")
    
    # Limpiar
    dm.stop_downloads()
    return True

def test_progress_update_optimization():
    """Prueba que las actualizaciones de progreso sean más eficientes"""
    print("\n🔍 Probando optimización de actualizaciones de progreso...")

    # Crear mock del SFTP manager
    mock_sftp = Mock()
    mock_sftp.is_connected.return_value = True

    # Crear download manager
    dm = DownloadManager(sftp_manager=mock_sftp)

    # Verificar que el chunk_size sea apropiado para actualizaciones eficientes
    assert dm.chunk_size == 8192, f"❌ Chunk size debería ser 8192, pero es {dm.chunk_size}"
    print("✅ Chunk size optimizado para actualizaciones fluidas: 8KB")

    # Limpiar
    dm.stop_downloads()
    return True

def test_thread_management():
    """Prueba que el manejo de threads sea más eficiente"""
    print("\n🔍 Probando manejo optimizado de threads...")
    
    # Crear mock del SFTP manager
    mock_sftp = Mock()
    mock_sftp.is_connected.return_value = True
    
    # Crear download manager
    dm = DownloadManager(sftp_manager=mock_sftp)
    
    # Verificar que el manager se inicie correctamente
    print("✅ Download Manager iniciado correctamente")

    # Verificar que se pueda detener rápidamente
    start_time = time.time()
    dm.stop_downloads()
    stop_time = time.time()

    stop_duration = stop_time - start_time
    assert stop_duration < 5.0, f"❌ Detener el manager tomó {stop_duration:.2f}s, debería ser < 5s"
    print(f"✅ Download Manager detenido rápidamente: {stop_duration:.2f}s")
    
    return True

def test_sftp_timeouts():
    """Prueba que los timeouts del SFTP sean más cortos"""
    print("\n🔍 Probando timeouts del SFTP Manager...")
    
    # Crear SFTP manager
    sftp = SFTPManager()
    
    # Verificar que el timeout por defecto del list_directory sea más corto
    # Esto se puede verificar inspeccionando el código o usando reflection
    import inspect
    sig = inspect.signature(sftp.list_directory)
    timeout_param = sig.parameters.get('timeout')
    
    if timeout_param and timeout_param.default:
        assert timeout_param.default == 15, f"❌ Timeout de list_directory debería ser 15, pero es {timeout_param.default}"
        print("✅ Timeout de list_directory optimizado: 15 segundos")
    
    # Verificar timeout del connect_direct
    sig = inspect.signature(sftp.connect_direct)
    timeout_param = sig.parameters.get('timeout')
    
    if timeout_param and timeout_param.default:
        assert timeout_param.default == 10, f"❌ Timeout de connect_direct debería ser 10, pero es {timeout_param.default}"
        print("✅ Timeout de connect_direct optimizado: 10 segundos")
    
    return True

def test_callback_system():
    """Prueba que el sistema de callbacks funcione"""
    print("\n🔍 Probando sistema de callbacks...")
    
    # Crear mock del SFTP manager
    mock_sftp = Mock()
    mock_sftp.is_connected.return_value = True
    
    # Crear download manager
    dm = DownloadManager(sftp_manager=mock_sftp)
    
    # Verificar que las listas de callbacks existan
    assert hasattr(dm, 'progress_callbacks'), "❌ Falta lista progress_callbacks"
    assert hasattr(dm, 'status_callbacks'), "❌ Falta lista status_callbacks"
    
    print("✅ Sistema de callbacks implementado correctamente")

    # Limpiar
    dm.stop_downloads()
    return True

def run_all_tests():
    """Ejecuta todas las pruebas"""
    print("🚀 Iniciando pruebas de las mejoras implementadas...")
    print("=" * 60)
    
    tests = [
        ("Timeouts del Download Manager", test_download_manager_timeouts),
        ("Optimización de progreso", test_progress_update_optimization),
        ("Manejo de threads", test_thread_management),
        ("Timeouts del SFTP", test_sftp_timeouts),
        ("Sistema de callbacks", test_callback_system),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n📋 Ejecutando: {test_name}")
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name}: PASÓ")
            else:
                failed += 1
                print(f"❌ {test_name}: FALLÓ")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Resultados: {passed} pasaron, {failed} fallaron")
    
    if failed == 0:
        print("🎉 ¡Todas las pruebas pasaron! Las mejoras están funcionando correctamente.")
        return True
    else:
        print("⚠️ Algunas pruebas fallaron. Revisar las mejoras implementadas.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
