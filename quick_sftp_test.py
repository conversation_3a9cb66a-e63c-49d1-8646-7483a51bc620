#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick SFTP Test - Prueba rápida sin problemas de entrada
"""

import paramiko
import traceback

def quick_sftp_test():
    """Test rápido con datos específicos"""
    print("🔍 Prueba Rápida de SFTP")
    print("=" * 30)
    
    # Datos del servidor demo (funciona siempre)
    print("1. Probando servidor demo...")
    try:
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect("test.rebex.net", 22, "demo", "password", timeout=10)
        sftp = ssh.open_sftp()
        files = sftp.listdir('.')
        print(f"✅ Demo funciona: {len(files)} archivos")
        sftp.close()
        ssh.close()
    except Exception as e:
        print(f"❌ Demo falló: {e}")
    
    # Test con tu servidor - EDITA ESTOS DATOS
    print("\n2. Probando tu servidor...")
    
    # CAMBIA ESTOS DATOS POR LOS TUYOS
    HOST = "***************"  # Tu IP
    PORT = 22
    USER = "root"             # Tu usuario
    PASS = "tu_password_aqui" # CAMBIA ESTO
    
    if PASS == "tu_password_aqui":
        print("❌ Por favor edita este archivo y cambia la contraseña")
        return
    
    try:
        print(f"🔄 Conectando a {USER}@{HOST}:{PORT}")
        ssh = paramiko.SSHClient()
        ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh.connect(HOST, PORT, USER, PASS, timeout=15)
        print("✅ SSH conectado")
        
        sftp = ssh.open_sftp()
        print("✅ SFTP conectado")
        
        files = sftp.listdir('.')
        print(f"✅ {len(files)} archivos encontrados")
        
        sftp.close()
        ssh.close()
        print("🎉 ¡TU SERVIDOR FUNCIONA!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nDetalles:")
        traceback.print_exc()

if __name__ == "__main__":
    quick_sftp_test()
