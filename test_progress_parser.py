#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test para el parser de progreso de Wget.
Verifica que la expresión regular puede extraer el porcentaje de varias
líneas de log de wget.
"""
import re
import unittest

def parse_wget_progress(log_content: str) -> float:
    """
    Busca en el contenido del log la última aparición del progreso de wget
    y devuelve el porcentaje como un float.
    """
    # Expresión regular para encontrar el porcentaje. Ej: "eta 1s]  55%[================>"
    # Busca uno o más dígitos seguidos de un '%'
    progress_matches = re.findall(r'(\d{1,3})%', log_content)
    
    if not progress_matches:
        return 0.0
        
    # Devuelve el último porcentaje encontrado, que es el más reciente
    return float(progress_matches[-1])

class TestWgetProgressParser(unittest.TestCase):

    def test_no_progress(self):
        log = "Connecting to example.com... connected.\\nHTTP request sent, awaiting response... 200 OK"
        self.assertEqual(parse_wget_progress(log), 0.0)

    def test_single_line_progress(self):
        log = "'/tmp/file.zip' saved [93553/93553]
        
        /tmp/test.log:                   eta 0s] <=>                                              100%[===================================================================================>]  91.36K  --.-KB/s    in 0.02s"
        self.assertEqual(parse_wget_progress(log), 100.0)

    def test_multiple_lines_of_progress(self):
        log = """
        --2025-07-06 15:30:00--  https://example.com/file.zip
        ...
        /tmp/test.log:    15K .......... .......... .......... .......... 16%  83.1K 1s
        /tmp/test.log:    50K .......... .......... .......... .......... 54%  85.1K 0s
        /tmp/test.log:    80K .......... .......... ........               87%  86.1K 0s
        /tmp/test.log:    91K ...                                          100%  87.1K=0.02s
        """
        # Simulación de la salida real de wget con porcentajes
        log_with_real_format = """
        Length: 93553 (91K) [application/zip]
        Saving to: ‘/tmp/file.zip’

        /tmp/file.zip           10%[==>                             ]   9.77K  --.-KB/s               
        /tmp/file.zip           55%[==================>             ]  50.15K  --.-KB/s               
        /tmp/file.zip          100%[==================================>]  91.36K  --.-KB/s    in 0.02s 

        2025-07-06 15:30:02 (4.50 MB/s) - ‘/tmp/file.zip’ saved [93553/93553]
        """
        self.assertEqual(parse_wget_progress(log_with_real_format), 100.0)

    def test_finds_last_progress_value(self):
        log = "some text 10% more text 25% and finally 78%"
        self.assertEqual(parse_wget_progress(log), 78.0)
        
    def test_incomplete_download(self):
        log = """
        /tmp/file.zip           10%[==>                             ]   9.77K  --.-KB/s               
        /tmp/file.zip           33%[==========>                     ]  30.15K  --.-KB/s    eta 1s
        """
        self.assertEqual(parse_wget_progress(log), 33.0)

if __name__ == '__main__':
    print("Ejecutando pruebas para el parser de progreso de wget...")
    unittest.main()
