#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Download Manager - Módulo para gestión de descargas con wget y control de cola
"""

import os
import subprocess
import threading
import queue
import time
import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum

class DownloadStatus(Enum):
    QUEUED = "En cola"
    DOWNLOADING = "Descargando"
    PAUSED = "Pausado"
    COMPLETED = "Completado"
    ERROR = "Error"
    CANCELLED = "Cancelado"

@dataclass
class DownloadItem:
    id: str
    url: str
    filename: str
    remote_path: str
    local_path: str
    status: DownloadStatus
    progress: float = 0.0
    speed: str = "0 B/s"
    eta: str = "--:--"
    file_size: str = "0 B"
    file_size_bytes: int = 0
    downloaded_bytes: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: str = ""
    retry_count: int = 0
    max_retries: int = 3
    wget_process: Optional[subprocess.Popen] = None
    sftp_manager: Optional[Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'url': self.url,
            'filename': self.filename,
            'remote_path': self.remote_path,
            'local_path': self.local_path,
            'status': self.status.value,
            'progress': self.progress,
            'speed': self.speed,
            'eta': self.eta,
            'file_size': self.file_size,
            'file_size_bytes': self.file_size_bytes,
            'downloaded_bytes': self.downloaded_bytes,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries
        }

class DownloadManager:
    def __init__(self, max_concurrent_downloads: int = 3):
        self.max_concurrent_downloads = max_concurrent_downloads
        self.downloads: Dict[str, DownloadItem] = {}
        self.download_queue = queue.Queue()
        self.active_downloads: Dict[str, threading.Thread] = {}
        self.is_running = False
        self.is_paused = False
        
        # Eventos y locks
        self.pause_event = threading.Event()
        self.pause_event.set()  # Inicialmente no pausado
        self.stop_event = threading.Event()
        self.downloads_lock = threading.Lock()
        
        # Configuración
        self.temp_dir = os.path.join(os.getcwd(), 'temp_downloads')
        self.wget_options = [
            '--no-check-certificate',
            '--timeout=30',
            '--tries=3',
            '--retry-connrefused',
            '--waitretry=5',
            '--progress=bar:force',
            '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
        
        # Crear directorio temporal
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # Callbacks
        self.progress_callbacks: List[Callable] = []
        self.status_callbacks: List[Callable] = []
        
        # Hilo principal de gestión
        self.manager_thread = None
        
    def add_download(self, url: str, filename: str, remote_path: str, sftp_manager=None, 
                    local_path: Optional[str] = None) -> str:
        """Agrega una nueva descarga a la cola"""
        
        # Generar ID único
        download_id = f"dl_{int(time.time() * 1000)}_{len(self.downloads)}"
        
        # Limpiar nombre de archivo
        clean_filename = self._clean_filename(filename)
        
        # Determinar ruta local
        if not local_path:
            local_path = os.path.join(self.temp_dir, clean_filename)
        
        # Crear item de descarga
        download_item = DownloadItem(
            id=download_id,
            url=url,
            filename=clean_filename,
            remote_path=remote_path,
            local_path=local_path,
            status=DownloadStatus.QUEUED,
            sftp_manager=sftp_manager
        )
        
        with self.downloads_lock:
            self.downloads[download_id] = download_item
            self.download_queue.put(download_id)
        
        self._notify_status_change(download_id)
        
        return download_id
    
    def start_downloads(self):
        """Inicia el sistema de descargas"""
        if not self.is_running:
            self.is_running = True
            self.stop_event.clear()
            self.pause_event.set()
            
            self.manager_thread = threading.Thread(target=self._download_manager, daemon=True)
            self.manager_thread.start()
            
            print("Sistema de descargas iniciado")
    
    def pause_downloads(self):
        """Pausa todas las descargas"""
        self.is_paused = True
        self.pause_event.clear()
        
        # Pausar procesos wget activos
        with self.downloads_lock:
            for download_id, download in self.downloads.items():
                if download.status == DownloadStatus.DOWNLOADING and download.wget_process:
                    try:
                        # Simplemente terminar el proceso para pausar
                        download.wget_process.terminate()
                        download.status = DownloadStatus.PAUSED
                        self._notify_status_change(download_id)
                    except Exception as e:
                        print(f"Error pausando descarga {download_id}: {e}")
        
        print("Descargas pausadas")
    
    def resume_downloads(self):
        """Reanuda las descargas pausadas"""
        self.is_paused = False
        self.pause_event.set()
        
        # Reanudar procesos wget pausados
        with self.downloads_lock:
            for download_id, download in self.downloads.items():
                if download.status == DownloadStatus.PAUSED:
                    # Para pausadas, simplemente volver a encolar
                    download.status = DownloadStatus.QUEUED
                    self.download_queue.put(download_id)
                    self._notify_status_change(download_id)
        
        print("Descargas reanudadas")
    
    def stop_downloads(self):
        """Detiene todas las descargas"""
        self.is_running = False
        self.stop_event.set()
        self.pause_event.set()
        
        # Terminar procesos wget activos
        with self.downloads_lock:
            for download_id, download in self.downloads.items():
                if download.status == DownloadStatus.DOWNLOADING and download.wget_process:
                    try:
                        download.wget_process.terminate()
                        download.wget_process.wait(timeout=5)
                    except Exception as e:
                        print(f"Error terminando descarga {download_id}: {e}")
                        try:
                            download.wget_process.kill()
                        except:
                            pass
                    
                    download.status = DownloadStatus.CANCELLED
                    download.end_time = datetime.now()
                    self._notify_status_change(download_id)
        
        # Esperar a que termine el hilo manager
        if self.manager_thread and self.manager_thread.is_alive():
            self.manager_thread.join(timeout=5)
        
        print("Descargas detenidas")
    
    def cancel_download(self, download_id: str):
        """Cancela una descarga específica"""
        with self.downloads_lock:
            if download_id in self.downloads:
                download = self.downloads[download_id]
                
                if download.status == DownloadStatus.DOWNLOADING and download.wget_process:
                    try:
                        download.wget_process.terminate()
                        download.wget_process.wait(timeout=5)
                    except Exception:
                        try:
                            download.wget_process.kill()
                        except:
                            pass
                
                download.status = DownloadStatus.CANCELLED
                download.end_time = datetime.now()
                self._notify_status_change(download_id)
    
    def remove_download(self, download_id: str):
        """Elimina una descarga de la lista"""
        with self.downloads_lock:
            if download_id in self.downloads:
                download = self.downloads[download_id]
                
                # Cancelar si está activa
                if download.status == DownloadStatus.DOWNLOADING:
                    self.cancel_download(download_id)
                
                # Eliminar archivo temporal si existe
                if os.path.exists(download.local_path):
                    try:
                        os.remove(download.local_path)
                    except Exception as e:
                        print(f"Error eliminando archivo temporal: {e}")
                
                # Eliminar de la lista
                del self.downloads[download_id]
                
                # Eliminar del hilo activo si existe
                if download_id in self.active_downloads:
                    del self.active_downloads[download_id]
    
    def clear_queue(self):
        """Limpia la cola de descargas"""
        # Detener descargas activas
        active_downloads = []
        with self.downloads_lock:
            for download_id, download in self.downloads.items():
                if download.status == DownloadStatus.DOWNLOADING:
                    active_downloads.append(download_id)
        
        for download_id in active_downloads:
            self.cancel_download(download_id)
        
        # Limpiar cola
        while not self.download_queue.empty():
            try:
                self.download_queue.get_nowait()
            except queue.Empty:
                break
        
        # Limpiar lista de descargas
        with self.downloads_lock:
            self.downloads.clear()
            self.active_downloads.clear()
    
    def _download_manager(self):
        """Hilo principal que gestiona las descargas"""
        while self.is_running and not self.stop_event.is_set():
            try:
                # Esperar si está pausado
                self.pause_event.wait()
                
                if self.stop_event.is_set():
                    break
                
                # Verificar si hay slots disponibles
                if len(self.active_downloads) >= self.max_concurrent_downloads:
                    time.sleep(1)
                    continue
                
                # Obtener siguiente descarga de la cola
                try:
                    download_id = self.download_queue.get(timeout=1)
                except queue.Empty:
                    continue
                
                # Verificar que la descarga aún existe y está en cola
                with self.downloads_lock:
                    if download_id not in self.downloads:
                        continue
                    
                    download = self.downloads[download_id]
                    if download.status != DownloadStatus.QUEUED:
                        continue
                
                # Iniciar descarga en hilo separado
                download_thread = threading.Thread(
                    target=self._download_file,
                    args=(download_id,),
                    daemon=True
                )
                
                self.active_downloads[download_id] = download_thread
                download_thread.start()
                
            except Exception as e:
                print(f"Error en download manager: {e}")
                time.sleep(1)
    
    def _download_file(self, download_id: str):
        """Descarga un archivo específico"""
        with self.downloads_lock:
            if download_id not in self.downloads:
                return
            download = self.downloads[download_id]
        
        try:
            download.status = DownloadStatus.DOWNLOADING
            download.start_time = datetime.now()
            self._notify_status_change(download_id)
            
            # Crear directorio local si no existe
            os.makedirs(os.path.dirname(download.local_path), exist_ok=True)
            
            # Preparar comando wget
            wget_cmd = ['wget'] + self.wget_options + [
                '-O', download.local_path,
                download.url
            ]
            
            # Iniciar proceso wget
            process = subprocess.Popen(
                wget_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            download.wget_process = process
            
            # Monitorear progreso
            self._monitor_wget_progress(download_id, process)
            
        except Exception as e:
            with self.downloads_lock:
                download.status = DownloadStatus.ERROR
                download.error_message = str(e)
                download.end_time = datetime.now()
                self._notify_status_change(download_id)
        
        finally:
            # Limpiar hilo activo
            if download_id in self.active_downloads:
                del self.active_downloads[download_id]
    
    def _monitor_wget_progress(self, download_id: str, process: subprocess.Popen):
        """Monitorea el progreso de wget"""
        with self.downloads_lock:
            download = self.downloads[download_id]
        
        try:
            if process.stdout:
                for line in iter(process.stdout.readline, ''):
                    if self.stop_event.is_set():
                        break
                    
                    # Esperar si está pausado
                    self.pause_event.wait()
                    
                    # Parsear línea de progreso de wget
                    self._parse_wget_output(download_id, line.strip())
            
            # Esperar a que termine el proceso
            return_code = process.wait()
            
            with self.downloads_lock:
                download = self.downloads[download_id]
                
                if return_code == 0:
                    # Descarga completada exitosamente
                    download.status = DownloadStatus.COMPLETED
                    download.progress = 100.0
                    download.end_time = datetime.now()
                    
                    # Subir archivo al servidor SFTP si está configurado
                    if download.sftp_manager and download.sftp_manager.is_connected():
                        self._upload_to_sftp(download_id)
                    
                else:
                    # Error en la descarga
                    if download.retry_count < download.max_retries:
                        # Reintentar
                        download.retry_count += 1
                        download.status = DownloadStatus.QUEUED
                        self.download_queue.put(download_id)
                        print(f"Reintentando descarga {download_id} ({download.retry_count}/{download.max_retries})")
                    else:
                        download.status = DownloadStatus.ERROR
                        download.error_message = f"Error en wget (código: {return_code})"
                        download.end_time = datetime.now()
                
                self._notify_status_change(download_id)
                
        except Exception as e:
            with self.downloads_lock:
                download.status = DownloadStatus.ERROR
                download.error_message = f"Error monitoreando progreso: {e}"
                download.end_time = datetime.now()
                self._notify_status_change(download_id)
    
    def _parse_wget_output(self, download_id: str, line: str):
        """Parsea la salida de wget para extraer progreso"""
        try:
            with self.downloads_lock:
                download = self.downloads[download_id]
            
            # Buscar patrones de progreso de wget
            # Ejemplo: "  2% [====>                            ] 12,345,678  1.23MB/s  eta 5m 30s"
            progress_pattern = r'(\d+)%.*?(\d+(?:,\d+)*)\s+(\d+\.?\d*[KMGT]?B/s)\s+eta\s+(\S+)'
            match = re.search(progress_pattern, line)
            
            if match:
                progress = float(match.group(1))
                downloaded = match.group(2).replace(',', '')
                speed = match.group(3)
                eta = match.group(4)
                
                with self.downloads_lock:
                    download.progress = progress
                    download.downloaded_bytes = int(downloaded) if downloaded.isdigit() else 0
                    download.speed = speed
                    download.eta = eta
                
                self._notify_progress_change(download_id)
            
            # Buscar tamaño total del archivo
            size_pattern = r'Length:\s+(\d+(?:,\d+)*)\s+\(([^)]+)\)'
            size_match = re.search(size_pattern, line)
            
            if size_match:
                size_bytes = int(size_match.group(1).replace(',', ''))
                size_formatted = size_match.group(2)
                
                with self.downloads_lock:
                    download.file_size_bytes = size_bytes
                    download.file_size = size_formatted
                
                self._notify_status_change(download_id)
                
        except Exception as e:
            print(f"Error parseando salida de wget: {e}")
    
    def _upload_to_sftp(self, download_id: str):
        """Sube el archivo descargado al servidor SFTP"""
        download = None
        try:
            with self.downloads_lock:
                download = self.downloads[download_id]
            
            if not download.sftp_manager or not download.sftp_manager.is_connected():
                return
            
            # Construir ruta remota completa
            remote_file_path = os.path.join(download.remote_path, download.filename).replace('\\', '/')
            
            # Callback de progreso para la subida
            def upload_progress(transferred, total):
                if total > 0:
                    upload_percent = (transferred / total) * 100
                    with self.downloads_lock:
                        download.progress = 100.0 + (upload_percent * 0.1)  # Progreso extra para subida
                    self._notify_progress_change(download_id)
            
            # Subir archivo
            download.sftp_manager.upload_file(
                download.local_path,
                remote_file_path,
                progress_callback=upload_progress
            )
            
            # Eliminar archivo temporal
            try:
                os.remove(download.local_path)
            except Exception as e:
                print(f"Error eliminando archivo temporal: {e}")
            
            print(f"Archivo {download.filename} subido exitosamente a {remote_file_path}")
            
        except Exception as e:
            if download:
                with self.downloads_lock:
                    download.error_message = f"Error subiendo archivo: {e}"
                print(f"Error subiendo archivo {download.filename}: {e}")
            else:
                print(f"Error subiendo archivo: {e}")
    
    def _clean_filename(self, filename: str) -> str:
        """Limpia el nombre de archivo para ser válido en el sistema de archivos"""
        # Eliminar caracteres no válidos
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Limitar longitud
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        
        return filename
    
    def _notify_status_change(self, download_id: str):
        """Notifica cambio de estado"""
        for callback in self.status_callbacks:
            try:
                callback(download_id)
            except Exception as e:
                print(f"Error en callback de estado: {e}")
    
    def _notify_progress_change(self, download_id: str):
        """Notifica cambio de progreso"""
        for callback in self.progress_callbacks:
            try:
                callback(download_id)
            except Exception as e:
                print(f"Error en callback de progreso: {e}")
    
    def add_progress_callback(self, callback: Callable):
        """Agrega callback para cambios de progreso"""
        self.progress_callbacks.append(callback)
    
    def add_status_callback(self, callback: Callable):
        """Agrega callback para cambios de estado"""
        self.status_callbacks.append(callback)
    
    def get_download(self, download_id: str) -> Optional[DownloadItem]:
        """Obtiene información de una descarga"""
        with self.downloads_lock:
            return self.downloads.get(download_id)
    
    def get_downloads_status(self) -> List[Dict[str, Any]]:
        """Obtiene estado de todas las descargas"""
        with self.downloads_lock:
            return [download.to_dict() for download in self.downloads.values()]
    
    def get_statistics(self) -> Dict[str, int]:
        """Obtiene estadísticas de descargas"""
        with self.downloads_lock:
            stats = {
                'total': len(self.downloads),
                'queued': 0,
                'active': 0,
                'completed': 0,
                'errors': 0,
                'cancelled': 0,
                'paused': 0
            }
            
            for download in self.downloads.values():
                if download.status == DownloadStatus.QUEUED:
                    stats['queued'] += 1
                elif download.status == DownloadStatus.DOWNLOADING:
                    stats['active'] += 1
                elif download.status == DownloadStatus.COMPLETED:
                    stats['completed'] += 1
                elif download.status == DownloadStatus.ERROR:
                    stats['errors'] += 1
                elif download.status == DownloadStatus.CANCELLED:
                    stats['cancelled'] += 1
                elif download.status == DownloadStatus.PAUSED:
                    stats['paused'] += 1
            
            return stats
    
    def save_queue_state(self, filename: str = 'download_queue.json'):
        """Guarda el estado de la cola de descargas"""
        try:
            state = {
                'downloads': [download.to_dict() for download in self.downloads.values()],
                'max_concurrent_downloads': self.max_concurrent_downloads,
                'saved_at': datetime.now().isoformat()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Error guardando estado de cola: {e}")
    
    def load_queue_state(self, filename: str = 'download_queue.json'):
        """Carga el estado de la cola de descargas"""
        try:
            if not os.path.exists(filename):
                return
            
            with open(filename, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            # Restaurar configuración
            self.max_concurrent_downloads = state.get('max_concurrent_downloads', 3)
            
            # Restaurar descargas (solo las que no estaban activas)
            for download_data in state.get('downloads', []):
                if download_data['status'] in ['En cola', 'Error']:
                    # Recrear item de descarga
                    download_item = DownloadItem(
                        id=download_data['id'],
                        url=download_data['url'],
                        filename=download_data['filename'],
                        remote_path=download_data['remote_path'],
                        local_path=download_data['local_path'],
                        status=DownloadStatus.QUEUED,  # Resetear a cola
                        retry_count=download_data.get('retry_count', 0),
                        max_retries=download_data.get('max_retries', 3)
                    )
                    
                    self.downloads[download_data['id']] = download_item
                    self.download_queue.put(download_data['id'])
            
            print(f"Estado de cola cargado: {len(self.downloads)} descargas restauradas")
            
        except Exception as e:
            print(f"Error cargando estado de cola: {e}")
    
    def __del__(self):
        """Destructor - asegurar limpieza"""
        self.stop_downloads()
